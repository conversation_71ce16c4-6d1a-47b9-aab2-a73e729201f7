# 🎫 Fetchly Support System

A dedicated support dashboard for managing customer tickets from fetchlypr.com

## 🚀 Features

### ✅ Support Dashboard
- **Real-time ticket management** from Firebase
- **Beautiful modern UI** matching <PERSON><PERSON><PERSON>'s design
- **Ticket filtering** by status and user type
- **Live statistics** showing ticket counts and metrics

### ✅ Ticket Management
- **View all tickets** from clients and providers
- **Respond to customers** with real-time messaging
- **Update ticket status** (Open → In Progress → Resolved)
- **Priority handling** (Low, Medium, High, Urgent)
- **User type identification** (Client vs Provider)

### ✅ Secure Access
- **Dedicated login** separate from admin portal
- **Firebase authentication** with support team collection
- **Role-based access** limited to support functions only

## 🔐 Login Credentials

```
Email: <EMAIL>
Password: Supp0rt2025!
```

## 🌐 Access URLs

- **Support Login:** `http://localhost:3000/support/login`
- **Support Dashboard:** `http://localhost:3000/support`
- **All Tickets:** `http://localhost:3000/support/tickets`

## 📊 Firebase Collections

### `supportTickets`
```javascript
{
  id: string,
  subject: string,
  message: string,
  status: 'open' | 'in-progress' | 'resolved' | 'closed',
  priority: 'low' | 'medium' | 'high' | 'urgent',
  userType: 'client' | 'provider',
  userId: string,
  userEmail: string,
  userName: string,
  createdAt: Timestamp,
  updatedAt: Timestamp,
  responses: [
    {
      id: string,
      message: string,
      respondedBy: string,
      respondedAt: Timestamp,
      isSupport: boolean
    }
  ]
}
```

### `supportTeam`
```javascript
{
  uid: string,
  email: string,
  name: string,
  role: 'support',
  permissions: ['read_tickets', 'write_tickets', 'update_tickets'],
  isActive: boolean,
  createdAt: Timestamp,
  updatedAt: Timestamp,
  lastLogin: Timestamp
}
```

## 🛠️ Setup Instructions

### 1. Support User Setup
Run the setup script to create the support user and sample tickets:

```bash
cd scripts
node setup-support-user.js
```

### 2. Manual Firebase Setup (Alternative)
If the script fails, manually create in Firebase Console:

1. **Authentication:**
   - Add user: `<EMAIL>` with password `Supp0rt2025!`

2. **Firestore Collections:**
   - Create `supportTeam` collection
   - Add document with support user UID
   - Create `supportTickets` collection for tickets

### 3. Frontend Integration
To integrate with fetchlypr.com, use the `createSupportTicket` function:

```javascript
import { createSupportTicket } from '@/lib/firestore';

// Create a new support ticket
await createSupportTicket({
  subject: 'Issue with booking',
  message: 'Customer message here...',
  priority: 'medium',
  userType: 'client', // or 'provider'
  userId: 'user_id_from_auth',
  userEmail: '<EMAIL>',
  userName: 'User Name'
});
```

## 🎨 Design Features

- **Glass morphism effects** with backdrop blur
- **Gradient backgrounds** matching Fetchly brand
- **Responsive design** for all devices
- **Real-time updates** from Firebase
- **Modern animations** and hover effects
- **Intuitive navigation** between tickets

## 📱 Usage Workflow

1. **Login** to support dashboard
2. **View dashboard** with ticket statistics
3. **Browse tickets** by status/type filters
4. **Select ticket** to view details and conversation
5. **Respond** to customer with real-time messaging
6. **Update status** as tickets progress
7. **Resolve** tickets when complete

## 🔧 Technical Stack

- **Frontend:** Next.js 14, React, TypeScript
- **Styling:** Tailwind CSS with custom Fetchly theme
- **Database:** Firebase Firestore
- **Authentication:** Firebase Auth
- **Real-time:** Firebase real-time listeners
- **Icons:** Heroicons

## 🚀 Production Ready

- ✅ **Real Firebase integration** (no mock data)
- ✅ **Secure authentication** with role-based access
- ✅ **Responsive design** for all devices
- ✅ **Error handling** and loading states
- ✅ **Production-grade** code structure
- ✅ **Scalable architecture** for growth

## 📞 Support Team Features

- **Ticket Dashboard:** Overview of all support metrics
- **Ticket Management:** Full CRUD operations on tickets
- **Customer Communication:** Real-time messaging system
- **Status Tracking:** Complete ticket lifecycle management
- **User Identification:** Clear distinction between clients and providers
- **Priority Management:** Handle urgent tickets first

---

**🎉 Your Fetchly Support System is ready to handle customer inquiries from fetchlypr.com!**
