rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read their own document in users collection
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
    }

    // Allow authenticated users to check if they exist in admins collection
    match /admins/{userId} {
      allow read: if request.auth != null; // Allow any authenticated user to read admin docs
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    // ========================================
    // HELPER FUNCTIONS - Optimized for Performance
    // ========================================

    // Check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Check if user is admin by looking up in admins collection
    function isAdmin() {
      return isAuthenticated() && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    // Check if user is super admin (for critical operations)
    function isSuperAdmin() {
      return isAdmin() && get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.superAdmin == true;
    }

    // Check if user is provider - SIMPLIFIED
    function isProvider() {
      return isAuthenticated() && exists(/databases/$(database)/documents/providers/$(request.auth.uid));
    }

    // Check if user is pet owner - SIMPLIFIED
    function isPetOwner() {
      return isAuthenticated() && exists(/databases/$(database)/documents/pet_owners/$(request.auth.uid));
    }

    // Check if user owns the resource
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Check if user can access resource (owner or admin)
    function canAccess(userId) {
      return isOwner(userId) || isAdmin();
    }

    // Validate required fields exist
    function hasRequiredFields(fields) {
      return request.resource.data.keys().hasAll(fields);
    }

    // Check if only specific fields are being updated
    function onlyUpdating(allowedFields) {
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields);
    }

    // ========================================
    // USERS COLLECTION - ULTRA PERMISSIVE FOR DEBUGGING
    // ========================================
    match /users/{userId} {
      // COMPLETELY UNRESTRICTED ACCESS
      allow read, write, create, update, delete: if true;
    }

    // ========================================
    // SOCIAL FEATURES
    // ========================================

    // Friends - User's friend lists
    match /friends/{userId} {
      allow read: if canAccess(userId);
      allow write: if isOwner(userId);
    }

    // ========================================
    // PET OWNER SPECIFIC COLLECTIONS
    // ========================================

    // Pets - Only pet owners can manage pets
    match /pets/{petId} {
      allow read, write: if isAuthenticated() &&
                        isOwner(resource.data.userId);
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.userId) &&
                   isPetOwner() &&
                   hasRequiredFields(['name', 'type', 'userId']);
    }

    // Pet Owner Wallet Transactions - Financial data
    match /petOwnerTransactions/{transactionId} {
      allow read, write: if isAuthenticated() &&
                        isOwner(resource.data.userId);
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.userId) &&
                   isPetOwner() &&
                   hasRequiredFields(['amount', 'type', 'userId']);
    }

    // Pet Owner Reward Points - Loyalty system
    match /rewardTransactions/{transactionId} {
      allow read, write: if isAuthenticated() &&
                        isOwner(resource.data.userId);
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.userId) &&
                   isPetOwner() &&
                   hasRequiredFields(['points', 'type', 'userId']);
    }

    // ========================================
    // PROVIDER SPECIFIC COLLECTIONS
    // ========================================

    // Provider Profiles - Public read access, restricted write
    match /providers/{providerId} {
      // Allow public read access to provider profiles
      allow read: if true;
      
      // Allow write only if authenticated and the document belongs to the current user
      allow write: if isAuthenticated() && (
        isOwner(resource.data.userId) ||
        (request.resource != null && isOwner(request.resource.data.userId))
      );
      
      // Allow creation with required fields
      allow create: if isAuthenticated() && 
                   isOwner(request.resource.data.userId) &&
                   hasRequiredFields(['userId', 'businessName', 'email', 'phone']);
    }

    // Provider Services - EMERGENCY FIX - FULLY PERMISSIVE
    match /services/{serviceId} {
      // UNRESTRICTED ACCESS FOR DEBUGGING
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Provider Earnings - EMERGENCY FIX - FULLY PERMISSIVE
    match /earnings/{earningId} {
      // UNRESTRICTED ACCESS FOR DEBUGGING
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // ========================================
    // FINANCIAL DATA (HIGHLY SENSITIVE)
    // ========================================

    // Bank Accounts - Plaid integration (CRITICAL SECURITY)
    match /bank_accounts/{accountId} {
      allow read, write: if isAuthenticated() &&
                        isOwner(resource.data.userId);
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.userId) &&
                   hasRequiredFields(['userId', 'accountType']);
    }

    // Provider Payouts - Payment processing (CRITICAL SECURITY)
    match /payouts/{payoutId} {
      allow read, write: if isAuthenticated() &&
                        isOwner(resource.data.userId);
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.userId) &&
                   hasRequiredFields(['amount', 'userId', 'status']);
    }

    // ========================================
    // PROVIDER BUSINESS MANAGEMENT
    // ========================================

    // Provider Clients - Customer relationship management
    match /providerClients/{clientId} {
      allow read, write: if isAuthenticated() &&
                        isOwner(resource.data.providerId);
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.providerId) &&
                   isProvider() &&
                   hasRequiredFields(['clientId', 'providerId']);
    }

    // Provider Calendar - Availability and scheduling
    match /providerCalendar/{calendarId} {
      // Public read for availability checking
      allow read: if isAuthenticated();

      // Write permissions: Only calendar owner
      allow write: if isAuthenticated() &&
                  isOwner(resource.data.providerId);

      // Create permissions: Only providers
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.providerId) &&
                   isProvider() &&
                   hasRequiredFields(['providerId', 'date']);
    }

    // Provider Subscriptions - Membership tiers
    match /providerSubscriptions/{subscriptionId} {
      allow read, write: if isAuthenticated() &&
                        isOwner(resource.data.providerId);
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.providerId) &&
                   isProvider() &&
                   hasRequiredFields(['tier', 'providerId']);
    }

    // ========================================
    // SHARED COLLECTIONS (Multi-user interactions)
    // ========================================

    // Bookings - EMERGENCY FIX - FULLY PERMISSIVE
    match /bookings/{bookingId} {
      // UNRESTRICTED ACCESS FOR DEBUGGING
      allow read, write, create, update, delete: if isAuthenticated();
    }

    // Reviews - Provider review system
    match /reviews/{reviewId} {
      // Public read for discovery and reputation
      allow read: if true;

      // Write: Only review author
      allow write: if isAuthenticated() &&
                  isOwner(resource.data.petOwnerId);

      // Create: Only pet owners who have used the service
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.petOwnerId) &&
                   isPetOwner() &&
                   hasRequiredFields(['rating', 'petOwnerId', 'providerId']);

      // Update: Review author or provider (for responses)
      allow update: if isAuthenticated() && (
        isOwner(resource.data.petOwnerId) ||
        (isOwner(resource.data.providerId) &&
         onlyUpdating(['providerResponse', 'responseDate']))
      );
    }

    // ========================================
    // SOCIAL MEDIA FEATURES
    // ========================================

    // Posts - Community social feed
    match /posts/{postId} {
      // Read: Public posts for everyone, private posts for owner only
      allow read: if resource.data.isPublic == true ||
                   (isAuthenticated() && isOwner(resource.data.userId));

      // List: Allow querying public posts for everyone, all posts for authenticated users
      allow list: if true; // Allow listing to enable community feed queries
      // Note: Individual document read permissions still apply during list operations

      // Update: Owner can edit, anyone can like/view
      allow update: if isAuthenticated() && (
        isOwner(resource.data.userId) || // Owner can update anything
        onlyUpdating(['likes', 'likedBy']) || // Anyone can like/unlike
        onlyUpdating(['comments']) || // Comment count updates
        onlyUpdating(['views']) || // View count updates
        onlyUpdating(['shares']) // Share count updates
      );

      // Write/Delete: Only post owner or admin
      allow write, delete: if isAuthenticated() &&
                          (isOwner(resource.data.userId) || isAdmin());

      // Create: Authenticated users with required fields
      allow create: if isAuthenticated() &&
                   request.auth.uid == request.resource.data.userId &&
                   hasRequiredFields(['content', 'userId', 'isPublic']);
    }

    // Comments - Post comments system
    match /comments/{commentId} {
      // Public read for community engagement
      allow read: if true;

      // Update: Comment owner or like/unlike operations
      allow update: if isAuthenticated() && (
        isOwner(resource.data.userId) || // Comment owner can edit
        onlyUpdating(['likes', 'likedBy']) // Anyone can like/unlike
      );

      // Delete: Comment owner or admin
      allow delete: if isAuthenticated() &&
                   (isOwner(resource.data.userId) || isAdmin());

      // Create: Authenticated users with required fields
      allow create: if isAuthenticated() &&
                   request.auth.uid == request.resource.data.userId &&
                   hasRequiredFields(['content', 'postId', 'userId']);
    }

    // Post Shares - Social sharing tracking
    match /postShares/{shareId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.sharedByUserId) &&
                   hasRequiredFields(['postId', 'sharedByUserId']);
    }

    // Blog Posts - Premium provider content
    match /blogPosts/{postId} {
      // Public read for blog discovery
      allow read: if true;

      // Write: Only blog author
      allow write: if isAuthenticated() &&
                  isOwner(resource.data.authorId);

      // Create: Only Pro providers (subscription check)
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.authorId) &&
                   isProvider() &&
                   hasRequiredFields(['title', 'content', 'authorId']);
                   // Note: Subscription tier check should be done in application logic
    }

    // ========================================
    // FRIENDSHIP & SOCIAL CONNECTIONS
    // ========================================

    // Friend Requests - Social connection requests
    match /friendRequests/{requestId} {
      // Read: Sender, receiver, or admin
      allow read: if isAuthenticated() && (
        isOwner(resource.data.toUserId) ||
        isOwner(resource.data.fromUserId) ||
        isAdmin()
      );

      // Update/Delete: Sender, receiver, or admin
      allow update, delete: if isAuthenticated() && (
        isOwner(resource.data.toUserId) ||
        isOwner(resource.data.fromUserId) ||
        isAdmin()
      );

      // Create: Only sender with required fields
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.fromUserId) &&
                   hasRequiredFields(['fromUserId', 'toUserId']);
    }

    // Friendships - Established friend connections
    match /friendships/{friendshipId} {
      allow read: if isAuthenticated() && (
        isOwner(resource.data.user1Id) ||
        isOwner(resource.data.user2Id)
      );

      allow write: if isAuthenticated() && (
        isOwner(resource.data.user1Id) ||
        isOwner(resource.data.user2Id)
      );

      allow create: if isAuthenticated() &&
                   hasRequiredFields(['user1Id', 'user2Id']);
    }

    // Provider Follows - Provider following system
    match /follows/{followId} {
      allow read: if isAuthenticated();

      allow write: if isAuthenticated() &&
                  isOwner(resource.data.followerId);

      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.followerId) &&
                   hasRequiredFields(['followerId', 'followedId']);
    }

    // ========================================
    // STORIES & EPHEMERAL CONTENT
    // ========================================

    // Stories - Temporary social content
    match /stories/{storyId} {
      // Read: Public stories or own stories
      allow read: if resource.data.isPublic ||
                   (isAuthenticated() && (isOwner(resource.data.userId) || isAdmin()));

      // Write: Only story owner or admin
      allow write: if isAuthenticated() &&
                  (isOwner(resource.data.userId) || isAdmin());

      // Create: Authenticated users with required fields
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.userId) &&
                   hasRequiredFields(['content', 'userId', 'isPublic']);
    }

    // ========================================
    // MESSAGING SYSTEM
    // ========================================

    // Chat Rooms - Private messaging
    match /chatRooms/{roomId} {
      // Read/Write: Only participants
      allow read, write: if isAuthenticated() &&
                        request.auth.uid in resource.data.participants;

      // Create: User must be in participants list
      allow create: if isAuthenticated() &&
                   request.auth.uid in request.resource.data.participants &&
                   hasRequiredFields(['participants', 'createdBy']);
    }

    // Messages - Chat messages
    match /messages/{messageId} {
      // Read: All authenticated users (filtered by chat room access)
      allow read: if isAuthenticated();

      // Write: Only message sender
      allow write: if isAuthenticated() &&
                  isOwner(resource.data.senderId);

      // Create: Authenticated users with required fields
      allow create: if isAuthenticated() &&
                   isOwner(request.resource.data.senderId) &&
                   hasRequiredFields(['content', 'senderId', 'chatRoomId']);
    }

    // ========================================
    // ADMIN ONLY COLLECTIONS
    // ========================================

    // System Settings - Platform configuration
    match /systemSettings/{settingId} {
      allow read, write: if isAdmin();
    }

    // Reward Items - Loyalty program catalog
    match /rewardItems/{itemId} {
      // Public read for reward browsing
      allow read: if true;
      // Admin only write
      allow write: if isAdmin();
    }

    // Platform Analytics - Business intelligence
    match /analytics/{analyticsId} {
      allow read, write: if isAdmin();
    }

    // ========================================
    // SECURITY AUDIT LOGS (Admin monitoring)
    // ========================================

    // Security logs for monitoring suspicious activity
    match /securityLogs/{logId} {
      allow read, write: if isSuperAdmin();
    }

    // User reports and moderation
    match /userReports/{reportId} {
      allow read: if isAdmin();
      allow create: if isAuthenticated() &&
                   hasRequiredFields(['reportedUserId', 'reporterId', 'reason']);
      allow write: if isAdmin();
    }
  }
}
