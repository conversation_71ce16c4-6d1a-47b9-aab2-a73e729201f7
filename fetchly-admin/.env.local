# Database Configuration - Using Firebase Firestore
# PostgreSQL configuration removed - migrated to Firebase
# DATABASE_TYPE=firebase

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_2024_fetchly
REFRESH_TOKEN_SECRET=your_super_secret_refresh_token_key_change_this_in_production_2024_fetchly
JWT_EXPIRES_IN=7d

# Admin Configuration (SECURE - DO NOT COMMIT)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=P0l@r!$2025
ADMIN_NAME=Fetchly Administrator
NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>

# Socket.IO Configuration
SOCKET_IO_PORT=3002

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Analytics Configuration
ANALYTICS_START_DATE=2025-01-01
RESET_ANALYTICS_ON_STARTUP=true
CLEAN_DATABASE_ON_STARTUP=false

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51OEvzDAGmZKDxTSzIcokuaySFNhAhSIfpp7Kqqwo17SE6HNztMebCxeX0jqSrLvt96Yp72GtRfcva1GKhC4vrxvP00SrWUgCub
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_WEBHOOK_SECRET=whsec_e654f95edec840061144f483f1f780cc85dc86a5af77e5dd3bed51c83cd0f448

# Firebase Admin SDK (Server-side Authentication)
FIREBASE_ADMIN_PROJECT_ID=fetchly-724b6
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>

# Plaid Configuration (Sandbox)
PLAID_CLIENT_ID=your_plaid_client_id_here
PLAID_SECRET=your_plaid_secret_here
NEXT_PUBLIC_PLAID_CLIENT_ID=your_plaid_client_id_here
PLAID_ENVIRONMENT=sandbox

# Environment
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyC5Fy9h_pQbrJCDCUfyR25_Deswb50HJwo
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=fetchly-724b6.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=fetchly-724b6
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=fetchly-724b6.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=192530321990
NEXT_PUBLIC_FIREBASE_APP_ID=1:192530321990:web:c1bb50473a7e1060a77047
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-EJ47KTH83Z
