import { formatDistance } from "./zh-CN/_lib/formatDistance.js";
import { formatLong } from "./zh-CN/_lib/formatLong.js";
import { formatRelative } from "./zh-CN/_lib/formatRelative.js";
import { localize } from "./zh-CN/_lib/localize.js";
import { match } from "./zh-CN/_lib/match.js";

/**
 * @category Locales
 * @summary Chinese Simplified locale.
 * @language Chinese Simplified
 * @iso-639-2 zho
 * <AUTHOR> [@KingMario](https://github.com/KingMario)
 * <AUTHOR> [@fnlctrl](https://github.com/fnlctrl)
 * <AUTHOR> [@sabrinamiao](https://github.com/sabrinamiao)
 * <AUTHOR> [@cubicwork](https://github.com/cubicwork)
 * <AUTHOR> [@skyuplam](https://github.com/skyuplam)
 */
export const zhCN = {
  code: "zh-CN",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default zhCN;
