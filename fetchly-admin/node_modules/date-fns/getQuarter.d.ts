import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link getQuarter} function options.
 */
export interface GetQuarterOptions extends ContextOptions<Date> {}
/**
 * @name getQuarter
 * @category Quarter Helpers
 * @summary Get the year quarter of the given date.
 *
 * @description
 * Get the year quarter of the given date.
 *
 * @param date - The given date
 * @param options - An object with options
 *
 * @returns The quarter
 *
 * @example
 * // Which quarter is 2 July 2014?
 * const result = getQuarter(new Date(2014, 6, 2));
 * //=> 3
 */
export declare function getQuarter(
  date: DateArg<Date> & {},
  options?: GetQuarterOptions | undefined,
): number;
