import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  DocumentSnapshot,
  QueryConstraint,
  Timestamp,
  onSnapshot,
  count,
  getCountFromServer
} from 'firebase/firestore';
import { db } from './firebase';
import { User, Provider, Booking, Transaction, WithdrawalRequest, DashboardStats } from '@/types';

// Collections
const COLLECTIONS = {
  USERS: 'users',
  PROVIDERS: 'providers', 
  BOOKINGS: 'bookings',
  TRANSACTIONS: 'transactions',
  WITHDRAWALS: 'withdrawals',
  ADMINS: 'admins'
};

// Helper function to convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp?.toDate) {
    return timestamp.toDate();
  }
  if (timestamp?.seconds) {
    return new Date(timestamp.seconds * 1000);
  }
  return new Date(timestamp);
};

// Helper function to convert data with timestamps
const convertDocData = (data: any) => {
  const converted = { ...data };
  
  // Convert common timestamp fields
  if (converted.createdAt) converted.createdAt = convertTimestamp(converted.createdAt);
  if (converted.updatedAt) converted.updatedAt = convertTimestamp(converted.updatedAt);
  if (converted.lastLoginAt) converted.lastLoginAt = convertTimestamp(converted.lastLoginAt);
  if (converted.startDate) converted.startDate = convertTimestamp(converted.startDate);
  if (converted.endDate) converted.endDate = convertTimestamp(converted.endDate);
  if (converted.requestedAt) converted.requestedAt = convertTimestamp(converted.requestedAt);
  if (converted.processedAt) converted.processedAt = convertTimestamp(converted.processedAt);
  if (converted.reviewedAt) converted.reviewedAt = convertTimestamp(converted.reviewedAt);
  if (converted.uploadedAt) converted.uploadedAt = convertTimestamp(converted.uploadedAt);
  
  return converted;
};

// Users
export const getUsers = async (filters?: {
  searchTerm?: string;
  status?: 'all' | 'active' | 'inactive';
  limit?: number;
  lastDoc?: DocumentSnapshot;
}): Promise<{ users: User[]; lastDoc?: DocumentSnapshot }> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters?.status && filters.status !== 'all') {
      constraints.push(where('isActive', '==', filters.status === 'active'));
    }
    
    constraints.push(orderBy('createdAt', 'desc'));
    
    if (filters?.limit) {
      constraints.push(limit(filters.limit));
    }
    
    if (filters?.lastDoc) {
      constraints.push(startAfter(filters.lastDoc));
    }
    
    const q = query(collection(db, COLLECTIONS.USERS), ...constraints);
    const snapshot = await getDocs(q);
    
    const users = snapshot.docs.map(doc => ({
      id: doc.id,
      ...convertDocData(doc.data())
    })) as User[];
    
    // Filter by search term on client side (for simplicity)
    let filteredUsers = users;
    if (filters?.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filteredUsers = users.filter(user => 
        user.displayName?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower)
      );
    }
    
    return {
      users: filteredUsers,
      lastDoc: snapshot.docs[snapshot.docs.length - 1]
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

export const updateUserStatus = async (userId: string, isActive: boolean): Promise<void> => {
  try {
    const userRef = doc(db, COLLECTIONS.USERS, userId);
    await updateDoc(userRef, { 
      isActive,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating user status:', error);
    throw error;
  }
};

// Providers
export const getProviders = async (filters?: {
  searchTerm?: string;
  status?: 'all' | 'approved' | 'pending' | 'rejected';
  limit?: number;
}): Promise<Provider[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters?.status && filters.status !== 'all') {
      if (filters.status === 'approved') {
        constraints.push(where('isApproved', '==', true));
      } else if (filters.status === 'pending') {
        constraints.push(where('isApproved', '==', false));
      }
    }
    
    constraints.push(orderBy('createdAt', 'desc'));
    
    if (filters?.limit) {
      constraints.push(limit(filters.limit));
    }
    
    const q = query(collection(db, COLLECTIONS.PROVIDERS), ...constraints);
    const snapshot = await getDocs(q);
    
    const providers = snapshot.docs.map(doc => ({
      id: doc.id,
      ...convertDocData(doc.data())
    })) as Provider[];
    
    // Filter by search term on client side
    let filteredProviders = providers;
    if (filters?.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filteredProviders = providers.filter(provider => 
        provider.businessName?.toLowerCase().includes(searchLower) ||
        provider.description?.toLowerCase().includes(searchLower)
      );
    }
    
    return filteredProviders;
  } catch (error) {
    console.error('Error fetching providers:', error);
    throw error;
  }
};

export const updateProviderStatus = async (
  providerId: string, 
  isApproved: boolean, 
  isVerified?: boolean
): Promise<void> => {
  try {
    const providerRef = doc(db, COLLECTIONS.PROVIDERS, providerId);
    const updateData: any = { 
      isApproved,
      updatedAt: Timestamp.now()
    };
    
    if (isVerified !== undefined) {
      updateData.isVerified = isVerified;
    }
    
    await updateDoc(providerRef, updateData);
  } catch (error) {
    console.error('Error updating provider status:', error);
    throw error;
  }
};

// Bookings
export const getBookings = async (filters?: {
  searchTerm?: string;
  status?: 'all' | 'pending' | 'confirmed' | 'completed' | 'cancelled';
  limit?: number;
}): Promise<Booking[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (filters?.status && filters.status !== 'all') {
      constraints.push(where('status', '==', filters.status));
    }
    
    constraints.push(orderBy('createdAt', 'desc'));
    
    if (filters?.limit) {
      constraints.push(limit(filters.limit));
    }
    
    const q = query(collection(db, COLLECTIONS.BOOKINGS), ...constraints);
    const snapshot = await getDocs(q);
    
    const bookings = snapshot.docs.map(doc => ({
      id: doc.id,
      ...convertDocData(doc.data())
    })) as Booking[];
    
    return bookings;
  } catch (error) {
    console.error('Error fetching bookings:', error);
    throw error;
  }
};

// Transactions
export const getTransactions = async (limit?: number): Promise<Transaction[]> => {
  try {
    const constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')];
    
    if (limit) {
      constraints.push(limit);
    }
    
    const q = query(collection(db, COLLECTIONS.TRANSACTIONS), ...constraints);
    const snapshot = await getDocs(q);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...convertDocData(doc.data())
    })) as Transaction[];
  } catch (error) {
    console.error('Error fetching transactions:', error);
    throw error;
  }
};

// Withdrawal Requests
export const getWithdrawalRequests = async (status?: string): Promise<WithdrawalRequest[]> => {
  try {
    const constraints: QueryConstraint[] = [];
    
    if (status && status !== 'all') {
      constraints.push(where('status', '==', status));
    }
    
    constraints.push(orderBy('requestedAt', 'desc'));
    
    const q = query(collection(db, COLLECTIONS.WITHDRAWALS), ...constraints);
    const snapshot = await getDocs(q);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...convertDocData(doc.data())
    })) as WithdrawalRequest[];
  } catch (error) {
    console.error('Error fetching withdrawal requests:', error);
    throw error;
  }
};

export const updateWithdrawalStatus = async (
  withdrawalId: string, 
  status: 'approved' | 'rejected' | 'processed'
): Promise<void> => {
  try {
    const withdrawalRef = doc(db, COLLECTIONS.WITHDRAWALS, withdrawalId);
    await updateDoc(withdrawalRef, { 
      status,
      processedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating withdrawal status:', error);
    throw error;
  }
};

// Real-time Dashboard Stats with actual Firestore + Stripe data
export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    console.log('📊 Fetching REAL dashboard stats from Firestore + Stripe...');

    // Try multiple collection names to find your actual collections
    const possibleCollections = ['users', 'providers', 'bookings', 'transactions', 'services', 'reviews', 'posts'];
    const stats = {
      totalUsers: 0,
      totalProviders: 0,
      totalBookings: 0,
      totalRevenue: 0,
      pendingApprovals: 0,
      activeDisputes: 0,
      monthlyGrowth: { users: 0, providers: 0, bookings: 0, revenue: 0 }
    };

    // Try to get Stripe revenue data (skip for now until Stripe is configured)
    try {
      // const { getStripeStats } = await import('./stripe');
      // const stripeStats = await getStripeStats();
      // stats.totalRevenue = stripeStats.totalRevenue;
      // console.log(`💳 Added Stripe revenue: $${stripeStats.totalRevenue}`);
      console.log('💳 Stripe integration ready (configure STRIPE_SECRET_KEY to enable)');
    } catch (stripeError) {
      console.log('⚠️ Stripe not configured or accessible, using Firestore revenue only');
    }

    // Check each possible collection
    for (const collectionName of possibleCollections) {
      try {
        const collectionRef = collection(db, collectionName);
        const snapshot = await getDocs(query(collectionRef, limit(1))); // Just check if collection exists

        if (!snapshot.empty) {
          console.log(`✅ Found collection: ${collectionName}`);

          // Get full count for this collection
          const fullSnapshot = await getDocs(collectionRef);
          const count = fullSnapshot.size;
          const docs = fullSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

          console.log(`📊 ${collectionName}: ${count} documents`);

          // Map collections to stats
          switch (collectionName) {
            case 'users':
              stats.totalUsers = count;
              break;
            case 'providers':
              stats.totalProviders = count;
              // Count pending approvals
              stats.pendingApprovals = docs.filter(doc =>
                doc.status === 'pending' || doc.approved === false || !doc.isApproved
              ).length;
              break;
            case 'bookings':
              stats.totalBookings = count;
              // Count disputes
              stats.activeDisputes = docs.filter(doc =>
                doc.status === 'disputed' || doc.status === 'dispute'
              ).length;
              // Calculate revenue from completed bookings
              stats.totalRevenue = docs
                .filter(doc => doc.status === 'completed' || doc.status === 'paid')
                .reduce((sum, doc) => {
                  const amount = doc.totalAmount || doc.amount || doc.price || 0;
                  return sum + (typeof amount === 'number' ? amount : 0);
                }, 0);
              break;
            case 'transactions':
              // Add transaction revenue if exists
              const transactionRevenue = docs
                .filter(doc => doc.type === 'payment' && doc.status === 'completed')
                .reduce((sum, doc) => sum + (doc.amount || 0), 0);
              stats.totalRevenue += transactionRevenue;
              break;
          }
        }
      } catch (error) {
        console.log(`⚠️ Collection ${collectionName} not accessible or doesn't exist`);
      }
    }

    // Calculate monthly growth (simplified)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    try {
      // Try to calculate user growth
      const recentUsersQuery = query(
        collection(db, 'users'),
        where('createdAt', '>=', Timestamp.fromDate(thirtyDaysAgo))
      );
      const recentUsersSnapshot = await getDocs(recentUsersQuery);
      const recentUserCount = recentUsersSnapshot.size;

      if (stats.totalUsers > 0) {
        stats.monthlyGrowth.users = Math.round(((recentUserCount / stats.totalUsers) * 100) * 100) / 100;
      }
    } catch (error) {
      console.log('⚠️ Could not calculate user growth');
    }

    console.log('✅ Final dashboard stats:', stats);

    return stats;
  } catch (error) {
    console.error('💥 Error fetching dashboard stats:', error);

    // Return empty stats if there's an error
    return {
      totalUsers: 0,
      totalProviders: 0,
      totalBookings: 0,
      totalRevenue: 0,
      pendingApprovals: 0,
      activeDisputes: 0,
      monthlyGrowth: {
        users: 0,
        providers: 0,
        bookings: 0,
        revenue: 0,
      }
    };
  }
};

// Real-time Users Management
export const getAllUsers = async (limitCount: number = 50) => {
  try {
    console.log('👥 Fetching real users from Firestore...');

    const usersQuery = query(
      collection(db, 'users'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(usersQuery);
    const users = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      lastLoginAt: doc.data().lastLoginAt?.toDate() || null,
    }));

    console.log(`✅ Fetched ${users.length} users`);
    return users;
  } catch (error) {
    console.error('💥 Error fetching users:', error);
    return [];
  }
};

// Real-time Providers Management
export const getAllProviders = async (limitCount: number = 50) => {
  try {
    console.log('🏢 Fetching real providers from Firestore...');

    const providersQuery = query(
      collection(db, 'providers'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(providersQuery);
    const providers = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || null,
    }));

    console.log(`✅ Fetched ${providers.length} providers`);
    return providers;
  } catch (error) {
    console.error('💥 Error fetching providers:', error);
    return [];
  }
};

// Real-time Bookings Management
export const getAllBookings = async (limitCount: number = 50) => {
  try {
    console.log('📅 Fetching real bookings from Firestore...');

    const bookingsQuery = query(
      collection(db, 'bookings'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(bookingsQuery);
    const bookings = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || null,
      scheduledDate: doc.data().scheduledDate?.toDate() || null,
    }));

    console.log(`✅ Fetched ${bookings.length} bookings`);
    return bookings;
  } catch (error) {
    console.error('💥 Error fetching bookings:', error);
    return [];
  }
};

// Real-time Services Management
export const getAllServices = async (limitCount: number = 50) => {
  try {
    console.log('🛠️ Fetching real services from Firestore...');

    const servicesQuery = query(
      collection(db, 'services'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(servicesQuery);
    const services = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || null,
    }));

    console.log(`✅ Fetched ${services.length} services`);
    return services;
  } catch (error) {
    console.error('💥 Error fetching services:', error);
    return [];
  }
};

// Real-time Reviews Management
export const fetchAllReviews = async (limitCount: number = 50) => {
  try {
    console.log('⭐ Fetching real reviews from Firestore...');

    const reviewsQuery = query(
      collection(db, 'reviews'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(reviewsQuery);
    const reviews = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
    }));

    console.log(`✅ Fetched ${reviews.length} reviews`);
    return reviews;
  } catch (error) {
    console.error('💥 Error fetching reviews:', error);
    return [];
  }
};

// Real-time Posts/Ads Management
export const getAllPosts = async (limitCount: number = 50) => {
  try {
    console.log('📝 Fetching real posts/ads from Firestore...');

    const postsQuery = query(
      collection(db, 'posts'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(postsQuery);
    const posts = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || null,
    }));

    console.log(`✅ Fetched ${posts.length} posts`);
    return posts;
  } catch (error) {
    console.error('💥 Error fetching posts:', error);
    return [];
  }
};

// Real-time Financial Transactions
export const getAllTransactions = async (limitCount: number = 50) => {
  try {
    console.log('💰 Fetching real transactions from Firestore...');

    const transactionsQuery = query(
      collection(db, 'transactions'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(transactionsQuery);
    const transactions = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      processedAt: doc.data().processedAt?.toDate() || null,
    }));

    console.log(`✅ Fetched ${transactions.length} transactions`);
    return transactions;
  } catch (error) {
    console.error('💥 Error fetching transactions:', error);
    return [];
  }
};





// Real-time Blog Management
export const getAllBlogPosts = async (limitCount: number = 50) => {
  try {
    console.log('📰 Fetching real blog posts from Firestore...');

    const blogQuery = query(
      collection(db, 'blog'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(blogQuery);
    const posts = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || null,
      publishedAt: doc.data().publishedAt?.toDate() || null,
    }));

    console.log(`✅ Fetched ${posts.length} blog posts`);
    return posts;
  } catch (error) {
    console.error('💥 Error fetching blog posts:', error);
    return [];
  }
};

// Update document status (for approvals, etc.)
export const updateDocumentStatus = async (collectionName: string, docId: string, status: string, additionalData: any = {}) => {
  try {
    console.log(`🔄 Updating ${collectionName}/${docId} status to: ${status}`);

    await updateDoc(doc(db, collectionName, docId), {
      status,
      updatedAt: Timestamp.now(),
      ...additionalData
    });

    console.log(`✅ Successfully updated ${collectionName}/${docId}`);
    return true;
  } catch (error) {
    console.error(`💥 Error updating ${collectionName}/${docId}:`, error);
    throw error;
  }
};

// Approve/Reject Reviews
export const updateReviewStatus = async (reviewId: string, isApproved: boolean, isFeatured: boolean = false) => {
  try {
    await updateDoc(doc(db, 'reviews', reviewId), {
      isApproved,
      isFeatured,
      updatedAt: Timestamp.now()
    });
    console.log(`✅ Review ${reviewId} ${isApproved ? 'approved' : 'rejected'}`);
    return true;
  } catch (error) {
    console.error('💥 Error updating review status:', error);
    throw error;
  }
};

// Update Featured Provider Status
export const updateFeaturedProvider = async (featuredId: string, isActive: boolean, priority?: number) => {
  try {
    const updateData: any = {
      isActive,
      updatedAt: Timestamp.now()
    };

    if (priority !== undefined) {
      updateData.priority = priority;
    }

    await updateDoc(doc(db, 'featuredProviders', featuredId), updateData);
    console.log(`✅ Featured provider ${featuredId} updated`);
    return true;
  } catch (error) {
    console.error('💥 Error updating featured provider:', error);
    throw error;
  }
};

// Publish/Unpublish Blog Posts
export const updateBlogPostStatus = async (postId: string, isPublished: boolean, isFeatured: boolean = false) => {
  try {
    const updateData: any = {
      isPublished,
      isFeatured,
      status: isPublished ? 'published' : 'draft',
      updatedAt: Timestamp.now()
    };

    if (isPublished) {
      updateData.publishedAt = Timestamp.now();
    }

    await updateDoc(doc(db, 'blog', postId), updateData);
    console.log(`✅ Blog post ${postId} ${isPublished ? 'published' : 'unpublished'}`);
    return true;
  } catch (error) {
    console.error('💥 Error updating blog post status:', error);
    throw error;
  }
};
