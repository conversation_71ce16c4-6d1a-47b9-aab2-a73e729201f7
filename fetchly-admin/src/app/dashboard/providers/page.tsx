'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import { formatDate, getStatusColor, getInitials } from '@/lib/utils';
import { Provider } from '@/types';

// Mock data - replace with real data from Firebase
const mockProviders: Provider[] = [
  {
    id: '1',
    userId: 'user1',
    businessName: 'PetCare Plus',
    description: 'Professional pet sitting and dog walking services',
    services: [
      { id: '1', name: 'Dog Walking', description: '30-60 minute walks', category: 'dog-walking', price: 25, duration: 30, isActive: true },
      { id: '2', name: 'Pet Sitting', description: 'In-home pet care', category: 'pet-sitting', price: 40, duration: 60, isActive: true }
    ],
    location: {
      street: '123 Main St',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94102',
      country: 'USA'
    },
    rating: 4.8,
    reviewCount: 127,
    isVerified: true,
    isApproved: true,
    documents: [
      { id: '1', type: 'license', url: '/docs/license1.pdf', status: 'approved', uploadedAt: new Date('2024-01-10'), reviewedAt: new Date('2024-01-12') }
    ],
    availability: [],
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-25')
  },
  {
    id: '2',
    userId: 'user2',
    businessName: 'Happy Tails Pet Services',
    description: 'Loving care for your furry friends',
    services: [
      { id: '3', name: 'Pet Grooming', description: 'Full grooming service', category: 'grooming', price: 60, duration: 120, isActive: true }
    ],
    location: {
      street: '456 Oak Ave',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94103',
      country: 'USA'
    },
    rating: 4.5,
    reviewCount: 89,
    isVerified: false,
    isApproved: false,
    documents: [
      { id: '2', type: 'license', url: '/docs/license2.pdf', status: 'pending', uploadedAt: new Date('2024-01-28') }
    ],
    availability: [],
    createdAt: new Date('2024-01-28'),
    updatedAt: new Date('2024-01-28')
  },
];

export default function ProvidersPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [providers, setProviders] = useState<Provider[]>(mockProviders);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'approved' | 'pending' | 'rejected'>('all');

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.businessName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'approved' && provider.isApproved) ||
                         (statusFilter === 'pending' && !provider.isApproved) ||
                         (statusFilter === 'rejected' && !provider.isApproved);
    return matchesSearch && matchesStatus;
  });

  const handleApprove = (providerId: string) => {
    setProviders(prev => prev.map(provider => 
      provider.id === providerId 
        ? { ...provider, isApproved: true, isVerified: true }
        : provider
    ));
  };

  const handleReject = (providerId: string) => {
    setProviders(prev => prev.map(provider => 
      provider.id === providerId 
        ? { ...provider, isApproved: false, isVerified: false }
        : provider
    ));
  };

  return (
    <DashboardLayout title="Providers Management">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Providers</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage service providers and their applications
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn btn-primary">
              Export Providers
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6">
          <div className="card p-4">
            <div className="text-2xl font-bold text-gray-900">
              {providers.filter(p => p.isApproved).length}
            </div>
            <div className="text-sm text-gray-600">Approved Providers</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {providers.filter(p => !p.isApproved).length}
            </div>
            <div className="text-sm text-gray-600">Pending Approval</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-green-600">
              {providers.filter(p => p.isVerified).length}
            </div>
            <div className="text-sm text-gray-600">Verified Providers</div>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search providers..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'approved' | 'pending' | 'rejected')}
              >
                <option value="all">All Status</option>
                <option value="approved">Approved</option>
                <option value="pending">Pending</option>
                <option value="rejected">Rejected</option>
              </select>
              <button className="btn btn-secondary">
                <FunnelIcon className="h-4 w-4 mr-2" />
                More Filters
              </button>
            </div>
          </div>
        </div>

        {/* Providers Table */}
        <div className="card overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Services
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rating
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Applied
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProviders.map((provider) => (
                  <tr key={provider.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-700">
                              {getInitials(provider.businessName)}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {provider.businessName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {provider.location.city}, {provider.location.state}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {provider.services.length} services
                      </div>
                      <div className="text-sm text-gray-500">
                        {provider.services.slice(0, 2).map(s => s.name).join(', ')}
                        {provider.services.length > 2 && '...'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <StarIcon className="h-4 w-4 text-yellow-400 mr-1" />
                        <span className="text-sm text-gray-900">{provider.rating}</span>
                        <span className="text-sm text-gray-500 ml-1">({provider.reviewCount})</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`badge ${getStatusColor(provider.isApproved ? 'approved' : 'pending')}`}>
                          {provider.isApproved ? 'Approved' : 'Pending'}
                        </span>
                        {provider.isVerified && (
                          <span className="badge bg-blue-100 text-blue-800">
                            Verified
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(provider.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button className="text-blue-600 hover:text-blue-900">
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        {!provider.isApproved && (
                          <>
                            <button 
                              onClick={() => handleApprove(provider.id)}
                              className="text-green-600 hover:text-green-900"
                            >
                              <CheckIcon className="h-4 w-4" />
                            </button>
                            <button 
                              onClick={() => handleReject(provider.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredProviders.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No providers found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
