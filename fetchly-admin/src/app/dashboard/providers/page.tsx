'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  StarIcon,
  CheckIcon,
  XMarkIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';
import { formatDate, getStatusColor, getInitials } from '@/lib/utils';
import { getAllProviders, updateProviderStatus, deleteProvider, updateProviderVerification } from '@/lib/firestore';
import { Provider } from '@/types';

// Mock data - replace with real data from Firebase
const mockProviders: Provider[] = [
  {
    id: '1',
    userId: 'user1',
    businessName: 'PetCare Plus',
    description: 'Professional pet sitting and dog walking services',
    services: [
      { id: '1', name: 'Dog Walking', description: '30-60 minute walks', category: 'dog-walking', price: 25, duration: 30, isActive: true },
      { id: '2', name: 'Pet Sitting', description: 'In-home pet care', category: 'pet-sitting', price: 40, duration: 60, isActive: true }
    ],
    location: {
      street: '123 Main St',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94102',
      country: 'USA'
    },
    rating: 4.8,
    reviewCount: 127,
    isVerified: true,
    isApproved: true,
    documents: [
      { id: '1', type: 'license', url: '/docs/license1.pdf', status: 'approved', uploadedAt: new Date('2024-01-10'), reviewedAt: new Date('2024-01-12') }
    ],
    availability: [],
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-25')
  },
  {
    id: '2',
    userId: 'user2',
    businessName: 'Happy Tails Pet Services',
    description: 'Loving care for your furry friends',
    services: [
      { id: '3', name: 'Pet Grooming', description: 'Full grooming service', category: 'grooming', price: 60, duration: 120, isActive: true }
    ],
    location: {
      street: '456 Oak Ave',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94103',
      country: 'USA'
    },
    rating: 4.5,
    reviewCount: 89,
    isVerified: false,
    isApproved: false,
    documents: [
      { id: '2', type: 'license', url: '/docs/license2.pdf', status: 'pending', uploadedAt: new Date('2024-01-28') }
    ],
    availability: [],
    createdAt: new Date('2024-01-28'),
    updatedAt: new Date('2024-01-28')
  },
];

export default function ProvidersPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loadingProviders, setLoadingProviders] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'approved' | 'pending' | 'rejected'>('all');
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [showProviderModal, setShowProviderModal] = useState(false);

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  useEffect(() => {
    const fetchRealProviders = async () => {
      if (user && adminUser) {
        try {
          setLoadingProviders(true);
          console.log('🏢 Fetching real providers from Firebase...');

          const realProviders = await getAllProviders(100);
          setProviders(realProviders as Provider[]);

          console.log(`✅ Loaded ${realProviders.length} real providers`);
          console.log('🏢 Sample provider data:', realProviders[0]); // Debug: see actual data structure
        } catch (error) {
          console.error('💥 Error fetching providers:', error);
          setProviders([]);
        } finally {
          setLoadingProviders(false);
        }
      }
    };

    fetchRealProviders();
  }, [user, adminUser]);

  if (loading || loadingProviders) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {loading ? 'Authenticating...' : 'Loading real providers from Firebase...'}
          </p>
        </div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.businessName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'approved' && provider.isApproved) ||
                         (statusFilter === 'pending' && !provider.isApproved) ||
                         (statusFilter === 'rejected' && !provider.isApproved);
    return matchesSearch && matchesStatus;
  });

  const handleViewProvider = (provider: Provider) => {
    setSelectedProvider(provider);
    setShowProviderModal(true);
  };

  const handleCloseModal = () => {
    setSelectedProvider(null);
    setShowProviderModal(false);
  };

  const handleApprove = async (providerId: string) => {
    try {
      console.log(`✅ Approving provider: ${providerId}`);
      await updateProviderStatus(providerId, true, true);

      // Update local state
      setProviders(prev => prev.map(provider =>
        provider.id === providerId
          ? { ...provider, isApproved: true, isVerified: true }
          : provider
      ));

      console.log(`✅ Provider ${providerId} approved successfully`);
    } catch (error) {
      console.error('💥 Error approving provider:', error);
    }
  };

  const handleReject = async (providerId: string) => {
    try {
      console.log(`❌ Rejecting provider: ${providerId}`);
      await updateProviderStatus(providerId, false, false);

      // Update local state
      setProviders(prev => prev.map(provider =>
        provider.id === providerId
          ? { ...provider, isApproved: false, isVerified: false }
          : provider
      ));

      console.log(`❌ Provider ${providerId} rejected successfully`);
    } catch (error) {
      console.error('💥 Error rejecting provider:', error);
    }
  };

  const handleVerify = async (providerId: string, isVerified: boolean) => {
    try {
      console.log(`🔒 ${isVerified ? 'Verifying' : 'Unverifying'} provider: ${providerId}`);
      await updateProviderVerification(providerId, isVerified);

      // Update local state
      setProviders(prev => prev.map(provider =>
        provider.id === providerId
          ? { ...provider, isVerified }
          : provider
      ));

      console.log(`🔒 Provider ${providerId} ${isVerified ? 'verified' : 'unverified'} successfully`);
    } catch (error) {
      console.error('💥 Error updating provider verification:', error);
    }
  };

  const handleDelete = async (providerId: string) => {
    if (!confirm('Are you sure you want to delete this provider? This action cannot be undone.')) {
      return;
    }

    try {
      console.log(`🗑️ Deleting provider: ${providerId}`);
      await deleteProvider(providerId);

      // Update local state
      setProviders(prev => prev.filter(provider => provider.id !== providerId));

      console.log(`🗑️ Provider ${providerId} deleted successfully`);
    } catch (error) {
      console.error('💥 Error deleting provider:', error);
    }
  };

  return (
    <DashboardLayout title="Providers Management">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Providers</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage service providers and their applications
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn btn-primary">
              Export Providers
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6">
          <div className="card p-4">
            <div className="text-2xl font-bold text-gray-900">
              {providers.filter(p => p.isApproved).length}
            </div>
            <div className="text-sm text-gray-600">Approved Providers</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {providers.filter(p => !p.isApproved).length}
            </div>
            <div className="text-sm text-gray-600">Pending Approval</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-green-600">
              {providers.filter(p => p.isVerified).length}
            </div>
            <div className="text-sm text-gray-600">Verified Providers</div>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search providers..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'approved' | 'pending' | 'rejected')}
              >
                <option value="all">All Status</option>
                <option value="approved">Approved</option>
                <option value="pending">Pending</option>
                <option value="rejected">Rejected</option>
              </select>
              <button className="btn btn-secondary">
                <FunnelIcon className="h-4 w-4 mr-2" />
                More Filters
              </button>
            </div>
          </div>
        </div>

        {/* Providers Table */}
        <div className="card overflow-hidden">
          {filteredProviders.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {providers.length === 0 ? 'No Providers Found' : 'No Providers Match Your Search'}
              </h3>
              <p className="text-gray-600 mb-4">
                {providers.length === 0
                  ? 'No providers have applied yet. Provider applications will appear here once they register.'
                  : 'Try adjusting your search terms or filters to find providers.'
                }
              </p>
              {providers.length === 0 && (
                <p className="text-sm text-blue-600">
                  📊 Total providers in database: {providers.length}
                </p>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Business Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location (State, Zipcode)
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Services Listed
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Account Created
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProviders.map((provider) => (
                  <tr key={provider.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-700">
                              {getInitials(provider.businessName)}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {provider.businessName || provider.name || 'Business Name'}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {provider.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {provider.location?.state || provider.state || 'N/A'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {provider.location?.zipcode || provider.zipcode || provider.location?.zip || 'No zipcode'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {provider.services?.length || 0} services
                      </div>
                      <div className="text-sm text-gray-500">
                        {provider.services?.length > 0
                          ? provider.services.slice(0, 2).map(s => typeof s === 'string' ? s : s?.name || 'Service').join(', ')
                          : 'No services listed'
                        }
                        {provider.services?.length > 2 && '...'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`badge ${getStatusColor(provider.isApproved ? 'approved' : 'pending')}`}>
                          {provider.isApproved ? 'Approved' : 'Pending'}
                        </span>
                        {provider.isVerified && (
                          <span className="badge bg-blue-100 text-blue-800">
                            Verified
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {provider.createdAt ? formatDate(provider.createdAt) : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleViewProvider(provider)}
                          className="text-blue-600 hover:text-blue-900"
                          title="View Provider Profile"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>

                        {!provider.isApproved ? (
                          <button
                            onClick={() => handleApprove(provider.id)}
                            className="text-green-600 hover:text-green-900"
                            title="Approve Provider"
                          >
                            <CheckIcon className="h-4 w-4" />
                          </button>
                        ) : (
                          <button
                            onClick={() => handleReject(provider.id)}
                            className="text-yellow-600 hover:text-yellow-900"
                            title="Reject Provider"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        )}

                        <button
                          onClick={() => handleVerify(provider.id, !provider.isVerified)}
                          className={`${provider.isVerified ? 'text-purple-600 hover:text-purple-900' : 'text-gray-400 hover:text-purple-600'}`}
                          title={provider.isVerified ? 'Remove Verification' : 'Verify Provider (Paid)'}
                        >
                          <ShieldCheckIcon className={`h-4 w-4 ${provider.isVerified ? 'fill-current' : ''}`} />
                        </button>

                        <button
                          onClick={() => handleDelete(provider.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Provider"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          )}
        </div>
      </div>

      {/* Provider Detail Modal */}
      {showProviderModal && selectedProvider && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">Provider Profile</h3>
              <button
                onClick={handleCloseModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Business Info */}
              <div>
                <h4 className="text-md font-semibold text-gray-800 mb-3">Business Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Business Name</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedProvider.businessName || selectedProvider.name || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedProvider.email || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedProvider.phoneNumber || selectedProvider.phone || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Provider ID</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedProvider.id}</p>
                  </div>
                </div>
              </div>

              {/* Location Info */}
              <div>
                <h4 className="text-md font-semibold text-gray-800 mb-3">Location</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">State</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedProvider.location?.state || selectedProvider.state || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">City</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedProvider.location?.city || selectedProvider.city || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Zipcode</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedProvider.location?.zipcode || selectedProvider.zipcode || selectedProvider.location?.zip || 'N/A'}</p>
                  </div>
                </div>
              </div>

              {/* Services */}
              {selectedProvider.services && selectedProvider.services.length > 0 && (
                <div>
                  <h4 className="text-md font-semibold text-gray-800 mb-3">Services</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {selectedProvider.services.map((service: any, index: number) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <p className="font-medium text-gray-900">
                          {typeof service === 'string' ? service : service?.name || `Service ${index + 1}`}
                        </p>
                        {typeof service === 'object' && service?.price && (
                          <p className="text-sm text-gray-600">Price: ${service.price}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Status Info */}
              <div>
                <h4 className="text-md font-semibold text-gray-800 mb-3">Status & Verification</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Approval Status</label>
                    <span className={`badge ${selectedProvider.isApproved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                      {selectedProvider.isApproved ? 'Approved' : 'Pending'}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Verification Status</label>
                    <span className={`badge ${selectedProvider.isVerified ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}`}>
                      {selectedProvider.isVerified ? 'Verified (Paid)' : 'Not Verified'}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Account Created</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedProvider.createdAt ? formatDate(selectedProvider.createdAt) : 'N/A'}</p>
                  </div>
                </div>
              </div>

              {/* Raw Data (for debugging) */}
              <div>
                <h4 className="text-md font-semibold text-gray-800 mb-3">Raw Data (Debug)</h4>
                <pre className="text-xs bg-gray-100 p-3 rounded-lg overflow-auto max-h-40">
                  {JSON.stringify(selectedProvider, null, 2)}
                </pre>
              </div>
            </div>

            <div className="flex justify-end mt-6 space-x-3">
              {!selectedProvider.isApproved && (
                <button
                  onClick={() => {
                    handleApprove(selectedProvider.id);
                    handleCloseModal();
                  }}
                  className="btn bg-green-600 text-white hover:bg-green-700"
                >
                  Approve Provider
                </button>
              )}
              <button
                onClick={() => {
                  handleVerify(selectedProvider.id, !selectedProvider.isVerified);
                  handleCloseModal();
                }}
                className={`btn ${selectedProvider.isVerified ? 'bg-gray-600 text-white hover:bg-gray-700' : 'bg-purple-600 text-white hover:bg-purple-700'}`}
              >
                {selectedProvider.isVerified ? 'Remove Verification' : 'Verify Provider'}
              </button>
              <button
                onClick={handleCloseModal}
                className="btn btn-secondary"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
