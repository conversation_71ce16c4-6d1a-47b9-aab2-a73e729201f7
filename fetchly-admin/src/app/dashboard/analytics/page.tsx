'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  ChartBarIcon,
  UsersIcon,
  CurrencyDollarIcon,
  CalendarDaysIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { formatCurrency, calculatePercentageChange } from '@/lib/utils';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

// Mock analytics data
const mockRevenueData = [
  { month: 'Jan', revenue: 12000, bookings: 45 },
  { month: 'Feb', revenue: 15000, bookings: 52 },
  { month: 'Mar', revenue: 18000, bookings: 68 },
  { month: 'Apr', revenue: 22000, bookings: 78 },
  { month: 'May', revenue: 25000, bookings: 89 },
  { month: 'Jun', revenue: 28000, bookings: 95 },
];

const mockUserGrowthData = [
  { month: 'Jan', users: 120, providers: 15 },
  { month: 'Feb', users: 145, providers: 18 },
  { month: 'Mar', users: 178, providers: 22 },
  { month: 'Apr', users: 210, providers: 28 },
  { month: 'May', users: 245, providers: 32 },
  { month: 'Jun', users: 280, providers: 38 },
];

const mockServiceDistribution = [
  { name: 'Dog Walking', value: 35, color: '#3B82F6' },
  { name: 'Pet Sitting', value: 25, color: '#10B981' },
  { name: 'Grooming', value: 20, color: '#F59E0B' },
  { name: 'Training', value: 12, color: '#EF4444' },
  { name: 'Veterinary', value: 8, color: '#8B5CF6' },
];

const mockTopProviders = [
  { name: 'PetCare Plus', bookings: 45, revenue: 1125 },
  { name: 'Happy Tails', bookings: 38, revenue: 950 },
  { name: 'Furry Friends', bookings: 32, revenue: 800 },
  { name: 'Pet Paradise', bookings: 28, revenue: 700 },
  { name: 'Loving Paws', bookings: 25, revenue: 625 },
];

export default function AnalyticsPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const currentMonthRevenue = mockRevenueData[mockRevenueData.length - 1].revenue;
  const previousMonthRevenue = mockRevenueData[mockRevenueData.length - 2].revenue;
  const revenueGrowth = calculatePercentageChange(currentMonthRevenue, previousMonthRevenue);

  const currentMonthBookings = mockRevenueData[mockRevenueData.length - 1].bookings;
  const previousMonthBookings = mockRevenueData[mockRevenueData.length - 2].bookings;
  const bookingsGrowth = calculatePercentageChange(currentMonthBookings, previousMonthBookings);

  return (
    <DashboardLayout title="Analytics">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="mt-1 text-sm text-gray-600">
            Track performance metrics and business insights
          </p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          <div className="card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Monthly Revenue
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(currentMonthRevenue)}
                    </div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {revenueGrowth >= 0 ? (
                        <ArrowUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      )}
                      {Math.abs(revenueGrowth).toFixed(1)}%
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarDaysIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Monthly Bookings
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {currentMonthBookings}
                    </div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      bookingsGrowth >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {bookingsGrowth >= 0 ? (
                        <ArrowUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                      )}
                      {Math.abs(bookingsGrowth).toFixed(1)}%
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UsersIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Users
                  </dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {mockUserGrowthData[mockUserGrowthData.length - 1].users}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Providers
                  </dt>
                  <dd className="text-2xl font-semibold text-gray-900">
                    {mockUserGrowthData[mockUserGrowthData.length - 1].providers}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Revenue Chart */}
          <div className="card p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Revenue Trend</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={mockRevenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />
                <Line type="monotone" dataKey="revenue" stroke="#3B82F6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* User Growth Chart */}
          <div className="card p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={mockUserGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="users" fill="#10B981" />
                <Bar dataKey="providers" fill="#F59E0B" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Service Distribution */}
          <div className="card p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Service Distribution</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={mockServiceDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                >
                  {mockServiceDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Top Providers */}
          <div className="card p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Providers</h3>
            <div className="space-y-4">
              {mockTopProviders.map((provider, index) => (
                <div key={provider.name} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-700">{index + 1}</span>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{provider.name}</p>
                      <p className="text-sm text-gray-500">{provider.bookings} bookings</p>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrency(provider.revenue)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
