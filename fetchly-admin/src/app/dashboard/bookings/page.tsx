'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';
import { getAllBookings, updateDocumentStatus } from '@/lib/firestore';
import { Booking } from '@/types';

// Mock data - replace with real data from Firebase
const mockBookings: Booking[] = [
  {
    id: '1',
    userId: 'user1',
    providerId: 'provider1',
    serviceId: 'service1',
    petIds: ['pet1'],
    startDate: new Date('2024-02-15T10:00:00'),
    endDate: new Date('2024-02-15T11:00:00'),
    status: 'confirmed',
    totalAmount: 25,
    paymentStatus: 'paid',
    specialInstructions: '<PERSON> loves treats and needs to be walked slowly',
    createdAt: new Date('2024-02-10'),
    updatedAt: new Date('2024-02-12')
  },
  {
    id: '2',
    userId: 'user2',
    providerId: 'provider1',
    serviceId: 'service2',
    petIds: ['pet2'],
    startDate: new Date('2024-02-16T14:00:00'),
    endDate: new Date('2024-02-16T15:00:00'),
    status: 'pending',
    totalAmount: 40,
    paymentStatus: 'pending',
    createdAt: new Date('2024-02-14'),
    updatedAt: new Date('2024-02-14')
  },
  {
    id: '3',
    userId: 'user3',
    providerId: 'provider2',
    serviceId: 'service3',
    petIds: ['pet3'],
    startDate: new Date('2024-02-12T09:00:00'),
    endDate: new Date('2024-02-12T11:00:00'),
    status: 'completed',
    totalAmount: 60,
    paymentStatus: 'paid',
    createdAt: new Date('2024-02-08'),
    updatedAt: new Date('2024-02-12')
  },
  {
    id: '4',
    userId: 'user1',
    providerId: 'provider1',
    serviceId: 'service1',
    petIds: ['pet1'],
    startDate: new Date('2024-02-20T16:00:00'),
    endDate: new Date('2024-02-20T17:00:00'),
    status: 'cancelled',
    totalAmount: 25,
    paymentStatus: 'refunded',
    createdAt: new Date('2024-02-18'),
    updatedAt: new Date('2024-02-19')
  },
];

// Mock user and provider data for display
const mockUserData: Record<string, { name: string; email: string }> = {
  user1: { name: 'Sarah Johnson', email: '<EMAIL>' },
  user2: { name: 'Mike Chen', email: '<EMAIL>' },
  user3: { name: 'Emily Davis', email: '<EMAIL>' },
};

const mockProviderData: Record<string, { name: string }> = {
  provider1: { name: 'PetCare Plus' },
  provider2: { name: 'Happy Tails Pet Services' },
};

const mockServiceData: Record<string, { name: string }> = {
  service1: { name: 'Dog Walking' },
  service2: { name: 'Pet Sitting' },
  service3: { name: 'Pet Grooming' },
};

export default function BookingsPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loadingBookings, setLoadingBookings] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'confirmed' | 'completed' | 'cancelled'>('all');

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  useEffect(() => {
    const fetchRealBookings = async () => {
      if (user && adminUser) {
        try {
          setLoadingBookings(true);
          console.log('📅 Fetching real bookings from Firebase...');

          const realBookings = await getAllBookings(100);
          setBookings(realBookings as Booking[]);

          console.log(`✅ Loaded ${realBookings.length} real bookings`);
        } catch (error) {
          console.error('💥 Error fetching bookings:', error);
          setBookings([]);
        } finally {
          setLoadingBookings(false);
        }
      }
    };

    fetchRealBookings();
  }, [user, adminUser]);

  if (loading || loadingBookings) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {loading ? 'Authenticating...' : 'Loading real bookings from Firebase...'}
          </p>
        </div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const filteredBookings = bookings.filter(booking => {
    const userData = mockUserData[booking.userId];
    const providerData = mockProviderData[booking.providerId];
    const serviceData = mockServiceData[booking.serviceId];
    
    const matchesSearch = userData?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         userData?.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         providerData?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         serviceData?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const totalBookings = bookings.length;
  const pendingBookings = bookings.filter(b => b.status === 'pending').length;
  const completedBookings = bookings.filter(b => b.status === 'completed').length;
  const totalRevenue = bookings
    .filter(b => b.paymentStatus === 'paid')
    .reduce((sum, b) => sum + b.totalAmount, 0);

  return (
    <DashboardLayout title="Bookings Management">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Bookings</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage all bookings and appointments
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn btn-primary">
              Export Bookings
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-4 mb-6">
          <div className="card p-4">
            <div className="flex items-center">
              <CalendarDaysIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <div className="text-2xl font-bold text-gray-900">{totalBookings}</div>
                <div className="text-sm text-gray-600">Total Bookings</div>
              </div>
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center">
              <CalendarDaysIcon className="h-8 w-8 text-yellow-600" />
              <div className="ml-3">
                <div className="text-2xl font-bold text-yellow-600">{pendingBookings}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center">
              <CalendarDaysIcon className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <div className="text-2xl font-bold text-green-600">{completedBookings}</div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
            </div>
          </div>
          <div className="card p-4">
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <div className="text-2xl font-bold text-purple-600">{formatCurrency(totalRevenue)}</div>
                <div className="text-sm text-gray-600">Revenue</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search bookings..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'pending' | 'confirmed' | 'completed' | 'cancelled')}
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
              <button className="btn btn-secondary">
                <FunnelIcon className="h-4 w-4 mr-2" />
                More Filters
              </button>
            </div>
          </div>
        </div>

        {/* Bookings Table */}
        <div className="card overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Booking ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Service
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredBookings.map((booking) => {
                  const userData = mockUserData[booking.userId];
                  const providerData = mockProviderData[booking.providerId];
                  const serviceData = mockServiceData[booking.serviceId];
                  
                  return (
                    <tr key={booking.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">#{booking.id}</div>
                        <div className="text-sm text-gray-500">
                          Created {formatDate(booking.createdAt)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{userData?.name}</div>
                        <div className="text-sm text-gray-500">{userData?.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{providerData?.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{serviceData?.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{formatDate(booking.startDate)}</div>
                        <div className="text-sm text-gray-500">
                          {booking.startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - 
                          {booking.endDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(booking.totalAmount)}
                        </div>
                        <div className={`text-xs ${getStatusColor(booking.paymentStatus)}`}>
                          {booking.paymentStatus}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`badge ${getStatusColor(booking.status)}`}>
                          {booking.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-900">
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          
          {filteredBookings.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No bookings found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
