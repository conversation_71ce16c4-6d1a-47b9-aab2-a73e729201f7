'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { formatDate, getStatusColor, getInitials } from '@/lib/utils';
import { getAllUsers, getAllProviders } from '@/lib/firestore';
import { User } from '@/types';

// Mock data - replace with real data from Firebase
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    displayName: '<PERSON>',
    phoneNumber: '+****************',
    createdAt: new Date('2024-01-15'),
    lastLoginAt: new Date('2024-01-30'),
    isActive: true,
    role: 'user',
    profile: {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      pets: [
        { id: '1', name: '<PERSON>', type: 'dog', breed: 'Golden Retriever', age: 3, weight: 65, specialNeeds: [], photos: [] }
      ]
    }
  },
  {
    id: '2',
    email: '<EMAIL>',
    displayName: 'Mike Chen',
    phoneNumber: '+****************',
    createdAt: new Date('2024-01-20'),
    lastLoginAt: new Date('2024-01-29'),
    isActive: true,
    role: 'user',
    profile: {
      firstName: 'Mike',
      lastName: 'Chen',
      pets: [
        { id: '2', name: 'Luna', type: 'cat', breed: 'Persian', age: 2, weight: 8, specialNeeds: [], photos: [] }
      ]
    }
  },
  {
    id: '3',
    email: '<EMAIL>',
    displayName: 'Emily Davis',
    phoneNumber: '+****************',
    createdAt: new Date('2024-01-10'),
    lastLoginAt: new Date('2024-01-25'),
    isActive: false,
    role: 'user',
    profile: {
      firstName: 'Emily',
      lastName: 'Davis',
      pets: []
    }
  },
];

export default function UsersPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  useEffect(() => {
    const fetchAllUsersAndProviders = async () => {
      if (user && adminUser) {
        try {
          setLoadingUsers(true);
          console.log('👥 Fetching real users and providers from Firebase...');

          // Fetch both users and providers
          const [realUsers, realProviders] = await Promise.all([
            getAllUsers(100),
            getAllProviders(100)
          ]);

          // Combine users and providers, marking their types
          const combinedUsers = [
            ...realUsers.map((u: any) => ({ ...u, userType: 'pet_owner' })),
            ...realProviders.map((p: any) => ({ ...p, userType: 'provider', displayName: p.businessName || p.name }))
          ];

          setUsers(combinedUsers as User[]);

          console.log(`✅ Loaded ${realUsers.length} pet owners and ${realProviders.length} providers`);
          console.log('📊 Sample user data:', combinedUsers[0]); // Debug: see actual data structure
        } catch (error) {
          console.error('💥 Error fetching users and providers:', error);
          setUsers([]);
        } finally {
          setLoadingUsers(false);
        }
      }
    };

    fetchAllUsersAndProviders();
  }, [user, adminUser]);

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setShowUserModal(true);
  };

  const handleCloseModal = () => {
    setSelectedUser(null);
    setShowUserModal(false);
  };

  if (loading || loadingUsers) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {loading ? 'Authenticating...' : 'Loading real users from Firestore...'}
          </p>
        </div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && user.isActive) ||
                         (statusFilter === 'inactive' && !user.isActive);
    return matchesSearch && matchesStatus;
  });

  return (
    <DashboardLayout title="Users Management">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Users</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage all registered users on the platform
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn btn-primary">
              Export Users
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
              <button className="btn btn-secondary">
                <FunnelIcon className="h-4 w-4 mr-2" />
                More Filters
              </button>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="card overflow-hidden">
          {filteredUsers.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {users.length === 0 ? 'No Users Found' : 'No Users Match Your Search'}
              </h3>
              <p className="text-gray-600 mb-4">
                {users.length === 0
                  ? 'No users have been registered yet. Users will appear here once they sign up.'
                  : 'Try adjusting your search terms or filters to find users.'
                }
              </p>
              {users.length === 0 && (
                <p className="text-sm text-blue-600">
                  📊 Total users in database: {users.length}
                </p>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pets
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Login
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          {user.photoURL ? (
                            <img className="h-10 w-10 rounded-full" src={user.photoURL} alt="" />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {getInitials(user.displayName)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.displayName || user.name || user.firstName || 'User'}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {user.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`badge ${(user as any).userType === 'provider' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}>
                        {(user as any).userType === 'provider' ? 'Provider' : 'Pet Owner'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{user.email}</div>
                      <div className="text-sm text-gray-500">{user.phoneNumber || 'No phone'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {(user as any).userType === 'provider' ? (
                        <div>
                          <div className="text-sm text-gray-900">
                            {(user as any).services?.length || 0} services
                          </div>
                          <div className="text-sm text-gray-500">
                            {(user as any).services?.length > 0
                              ? (user as any).services.slice(0, 2).map((s: any) => typeof s === 'string' ? s : s?.name || 'Service').join(', ')
                              : 'No services listed'
                            }
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div className="text-sm text-gray-900">
                            {user.profile?.pets?.length || user.pets?.length || 0} pets
                          </div>
                          {(user.profile?.pets?.length > 0 || user.pets?.length > 0) && (
                            <div className="text-sm text-gray-500">
                              {(() => {
                                const pets = user.profile?.pets || user.pets || [];
                                const firstPet = pets[0];
                                if (firstPet) {
                                  return `${firstPet.name || 'Pet'} (${firstPet.type || firstPet.breed || 'Pet'})${pets.length > 1 ? ` +${pets.length - 1} more` : ''}`;
                                }
                                return 'No pets listed';
                              })()}
                            </div>
                          )}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`badge ${getStatusColor((user.isActive !== false) ? 'active' : 'inactive')}`}>
                        {(user.isActive !== false) ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.createdAt ? formatDate(user.createdAt) : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleViewUser(user)}
                          className="text-blue-600 hover:text-blue-900"
                          title="View User Details"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          )}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredUsers.length}</span> of{' '}
            <span className="font-medium">{users.length}</span> results
          </div>
          <div className="flex space-x-2">
            <button className="btn btn-secondary" disabled>
              Previous
            </button>
            <button className="btn btn-secondary" disabled>
              Next
            </button>
          </div>
        </div>
      </div>

      {/* User Detail Modal */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900">User Details</h3>
              <button
                onClick={handleCloseModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Basic Info */}
              <div>
                <h4 className="text-md font-semibold text-gray-800 mb-3">Basic Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.displayName || selectedUser.name || selectedUser.firstName || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.email || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.phoneNumber || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">User ID</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.id}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Role</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.role || 'user'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <span className={`badge ${(selectedUser.isActive !== false) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {(selectedUser.isActive !== false) ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Account Info */}
              <div>
                <h4 className="text-md font-semibold text-gray-800 mb-3">Account Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created At</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.createdAt ? formatDate(selectedUser.createdAt) : 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Last Login</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.lastLoginAt ? formatDate(selectedUser.lastLoginAt) : 'Never'}</p>
                  </div>
                </div>
              </div>

              {/* Pets Info */}
              {(selectedUser.profile?.pets?.length > 0 || selectedUser.pets?.length > 0) && (
                <div>
                  <h4 className="text-md font-semibold text-gray-800 mb-3">Pets</h4>
                  <div className="space-y-2">
                    {(selectedUser.profile?.pets || selectedUser.pets || []).map((pet: any, index: number) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">{pet.name || `Pet ${index + 1}`}</p>
                            <p className="text-sm text-gray-600">{pet.type || pet.breed || 'Unknown type'}</p>
                            {pet.age && <p className="text-sm text-gray-600">Age: {pet.age}</p>}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Raw Data (for debugging) */}
              <div>
                <h4 className="text-md font-semibold text-gray-800 mb-3">Raw Data (Debug)</h4>
                <pre className="text-xs bg-gray-100 p-3 rounded-lg overflow-auto max-h-40">
                  {JSON.stringify(selectedUser, null, 2)}
                </pre>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={handleCloseModal}
                className="btn btn-secondary"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
