'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  UsersIcon,
  UserGroupIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ClockIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { formatCurrency } from '@/lib/utils';
import { getDashboardStats } from '@/lib/firestore';
import { DashboardStats } from '@/types';

export default function DashboardPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loadingStats, setLoadingStats] = useState(true);
  const [recentActivities, setRecentActivities] = useState<any[]>([]);

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  useEffect(() => {
    const fetchRealData = async () => {
      if (user && adminUser) {
        try {
          setLoadingStats(true);
          console.log('🔄 Fetching real dashboard data...');

          // Fetch real stats from Firestore
          const dashboardStats = await getDashboardStats();
          setStats(dashboardStats);

          // Mock recent activities for now - you can replace with real data later
          setRecentActivities([
            {
              id: 1,
              type: 'booking',
              message: 'New booking request received',
              time: '2 minutes ago',
              status: 'pending',
            },
            {
              id: 2,
              type: 'provider',
              message: 'Provider application submitted',
              time: '15 minutes ago',
              status: 'pending',
            },
            {
              id: 3,
              type: 'payment',
              message: 'Payment processed successfully',
              time: '1 hour ago',
              status: 'completed',
            },
            {
              id: 4,
              type: 'user',
              message: 'New user registration',
              time: '2 hours ago',
              status: 'active',
            },
          ]);

          console.log('✅ Dashboard data loaded successfully');
        } catch (error) {
          console.error('💥 Error fetching dashboard data:', error);
          // Set fallback data
          setStats({
            totalUsers: 0,
            totalProviders: 0,
            totalBookings: 0,
            totalRevenue: 0,
            pendingApprovals: 0,
            activeDisputes: 0,
            monthlyGrowth: { users: 0, providers: 0, bookings: 0, revenue: 0 }
          });
        } finally {
          setLoadingStats(false);
        }
      }
    };

    fetchRealData();
  }, [user, adminUser]);

  if (loading || loadingStats) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-white/20 border-t-white mx-auto mb-6"></div>
          <p className="text-white/80 text-lg font-medium">
            {loading ? 'Authenticating...' : 'Loading dashboard analytics...'}
          </p>
          <p className="text-white/60 text-sm mt-2">
            Fetching real-time data from Firebase...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push('/login');
    return null;
  }

  if (!adminUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 p-8 max-w-md mx-auto">
          <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <span className="text-white text-2xl">⚠️</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-white/70 mb-6">You don&apos;t have admin privileges to access this portal.</p>
          <button
            onClick={() => router.push('/login')}
            className="btn btn-primary"
          >
            Back to Login
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-gray-900 mb-4">Loading Dashboard...</h1>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  const statCards = [
    {
      name: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      change: stats.monthlyGrowth.users,
      icon: UsersIcon,
      color: 'blue',
    },
    {
      name: 'Active Providers',
      value: stats.totalProviders.toLocaleString(),
      change: stats.monthlyGrowth.providers,
      icon: UserGroupIcon,
      color: 'green',
    },
    {
      name: 'Total Bookings',
      value: stats.totalBookings.toLocaleString(),
      change: stats.monthlyGrowth.bookings,
      icon: CalendarDaysIcon,
      color: 'purple',
    },
    {
      name: 'Revenue',
      value: formatCurrency(stats.totalRevenue),
      change: stats.monthlyGrowth.revenue,
      icon: CurrencyDollarIcon,
      color: 'yellow',
    },
  ];

  return (
    <DashboardLayout title="Dashboard">
      <div className="space-y-8">
        {/* Modern Welcome Section */}
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 p-8 text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse-glow">
            <span className="text-white text-3xl font-bold">F</span>
          </div>
          <h1 className="text-3xl font-bold gradient-text mb-3">
            Welcome to Fetchly Admin Portal
          </h1>
          <p className="text-gray-600 text-lg">
            Managing your pet services ecosystem with real-time analytics
          </p>
          <div className="mt-6 flex items-center justify-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
              Live Data Connected
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
              Firebase Synced
            </div>
          </div>
        </div>

        {/* Modern Stats Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {statCards.map((stat, index) => (
            <div key={stat.name} className="bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 p-6" style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${
                  stat.color === 'blue' ? 'from-blue-500 to-indigo-600' :
                  stat.color === 'green' ? 'from-emerald-500 to-teal-600' :
                  stat.color === 'purple' ? 'from-purple-500 to-indigo-600' :
                  'from-amber-500 to-orange-600'
                } rounded-xl flex items-center justify-center shadow-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className={`text-xs font-medium px-2 py-1 rounded-full ${
                  stat.change >= 0 ? 'bg-emerald-100 text-emerald-800' : 'bg-red-100 text-red-800'
                }`}>
                  {stat.change >= 0 ? '+' : ''}{stat.change}%
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  {stat.name}
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {stat.value}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Modern Quick Actions & Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 p-8">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                <span className="text-white text-lg">⚡</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900">Quick Actions</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl border border-amber-200">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                    <ClockIcon className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-sm font-semibold text-gray-900">
                    Pending Provider Approvals
                  </span>
                </div>
                <span className="fetchly-badge-warning">
                  {stats.pendingApprovals}
                </span>
              </div>

              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-xl border border-red-200">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg flex items-center justify-center mr-3">
                    <ClockIcon className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-sm font-semibold text-gray-900">
                    Active Disputes
                  </span>
                </div>
                <span className="fetchly-badge-danger">
                  {stats.activeDisputes}
                </span>
              </div>

              <div className="pt-4">
                <button className="w-full btn btn-primary">
                  View All Pending Items
                </button>
              </div>
            </div>
          </div>

          {/* Modern Recent Activity */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 p-8">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center mr-4">
                <span className="text-white text-lg">📊</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900">Platform Insights</h3>
            </div>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
                  <div className="text-2xl font-bold text-indigo-600">{stats.totalUsers}</div>
                  <div className="text-sm text-gray-600">Total Users</div>
                </div>
                <div className="text-center p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl">
                  <div className="text-2xl font-bold text-emerald-600">{stats.totalProviders}</div>
                  <div className="text-sm text-gray-600">Active Providers</div>
                </div>
              </div>

              <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Platform Growth</span>
                  <span className="text-xs text-gray-500">This Month</span>
                </div>
                <div className="flex items-center">
                  <div className="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                    <div className="bg-gradient-to-r from-purple-500 to-pink-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                  <span className="text-sm font-semibold text-purple-600">75%</span>
                </div>
              </div>

              <div className="pt-4">
                <button className="w-full btn btn-secondary">
                  View Detailed Analytics
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Footer Section */}
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 p-8 text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-2xl">🚀</span>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Ready to Scale?</h3>
          <p className="text-gray-600 mb-6">Your Fetchly platform is growing! Explore advanced features to enhance user experience.</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn btn-primary">
              Upgrade Features
            </button>
            <button className="btn btn-secondary">
              View Documentation
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
