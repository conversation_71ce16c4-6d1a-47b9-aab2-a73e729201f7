'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  UsersIcon,
  UserGroupIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ClockIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { formatCurrency } from '@/lib/utils';

// Mock data - replace with real data from Firebase
const mockStats = {
  totalUsers: 1247,
  totalProviders: 89,
  totalBookings: 456,
  totalRevenue: 23450,
  pendingApprovals: 12,
  activeDisputes: 3,
  monthlyGrowth: {
    users: 15.2,
    providers: 8.7,
    bookings: 22.1,
    revenue: 18.9,
  },
};

const mockRecentActivities = [
  {
    id: 1,
    type: 'booking',
    message: 'New booking request from <PERSON>',
    time: '2 minutes ago',
    status: 'pending',
  },
  {
    id: 2,
    type: 'provider',
    message: 'Provider application approved for PetCare Plus',
    time: '15 minutes ago',
    status: 'approved',
  },
  {
    id: 3,
    type: 'payment',
    message: 'Payment of $125.00 processed successfully',
    time: '1 hour ago',
    status: 'completed',
  },
  {
    id: 4,
    type: 'user',
    message: 'New user registration: Mike Chen',
    time: '2 hours ago',
    status: 'active',
  },
];

export default function DashboardPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState(mockStats);
  const [recentActivities, setRecentActivities] = useState(mockRecentActivities);

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const statCards = [
    {
      name: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      change: stats.monthlyGrowth.users,
      icon: UsersIcon,
      color: 'blue',
    },
    {
      name: 'Active Providers',
      value: stats.totalProviders.toLocaleString(),
      change: stats.monthlyGrowth.providers,
      icon: UserGroupIcon,
      color: 'green',
    },
    {
      name: 'Total Bookings',
      value: stats.totalBookings.toLocaleString(),
      change: stats.monthlyGrowth.bookings,
      icon: CalendarDaysIcon,
      color: 'purple',
    },
    {
      name: 'Revenue',
      value: formatCurrency(stats.totalRevenue),
      change: stats.monthlyGrowth.revenue,
      icon: CurrencyDollarIcon,
      color: 'yellow',
    },
  ];

  return (
    <DashboardLayout title="Dashboard">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">
            Welcome back, {adminUser.displayName}!
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Here&apos;s what&apos;s happening with your Fetchly platform today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          {statCards.map((stat) => (
            <div key={stat.name} className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className={`h-8 w-8 text-${stat.color}-600`} />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                      </div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.change >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change >= 0 ? (
                          <ArrowUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                        ) : (
                          <ArrowDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                        )}
                        <span className="sr-only">
                          {stat.change >= 0 ? 'Increased' : 'Decreased'} by
                        </span>
                        {Math.abs(stat.change)}%
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions & Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <div className="card p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center">
                  <ClockIcon className="h-5 w-5 text-yellow-600 mr-3" />
                  <span className="text-sm font-medium text-gray-900">
                    Pending Provider Approvals
                  </span>
                </div>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  {stats.pendingApprovals}
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center">
                  <ClockIcon className="h-5 w-5 text-red-600 mr-3" />
                  <span className="text-sm font-medium text-gray-900">
                    Active Disputes
                  </span>
                </div>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  {stats.activeDisputes}
                </span>
              </div>
              
              <div className="pt-3">
                <button className="w-full btn btn-primary">
                  View All Pending Items
                </button>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="card p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
              <div className="pt-3">
                <button className="w-full btn btn-secondary text-sm">
                  View All Activity
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
