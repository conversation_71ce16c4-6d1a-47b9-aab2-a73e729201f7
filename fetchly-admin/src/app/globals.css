@import "tailwindcss";

:root {
  --primary: #3B82F6;
  --primary-dark: #2563EB;
  --secondary: #10B981;
  --accent: #F59E0B;
  --danger: #EF4444;
  --warning: #F59E0B;
  --success: #10B981;
  --info: #3B82F6;
  --background: #F8FAFC;
  --surface: #FFFFFF;
  --text-primary: #1E293B;
  --text-secondary: #64748B;
  --border: #E2E8F0;
}

@layer base {
  * {
    @apply border-slate-200;
  }

  body {
    @apply bg-slate-50 text-slate-900 antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-slate-900;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-slate-200 text-slate-900 hover:bg-slate-300 focus:ring-slate-500;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .card {
    @apply bg-white rounded-lg border border-slate-200 shadow-sm;
  }

  .input {
    @apply block w-full rounded-lg border border-slate-300 px-3 py-2 text-sm placeholder-slate-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500;
  }

  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .sidebar-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors;
  }

  .sidebar-link-active {
    @apply bg-blue-50 text-blue-700 border-r-2 border-blue-700;
  }

  .sidebar-link-inactive {
    @apply text-slate-600 hover:bg-slate-50 hover:text-slate-900;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
