@import "tailwindcss";

:root {
  /* Fetchly Brand Colors - Modern Purple/Blue Gradient Theme */
  --fetchly-primary: #6366f1;
  --fetchly-primary-dark: #4f46e5;
  --fetchly-secondary: #8b5cf6;
  --fetchly-accent: #06b6d4;
  --fetchly-success: #10b981;
  --fetchly-warning: #f59e0b;
  --fetchly-danger: #ef4444;
  --fetchly-info: #3b82f6;

  /* Neutral Colors */
  --fetchly-gray-50: #f8fafc;
  --fetchly-gray-100: #f1f5f9;
  --fetchly-gray-200: #e2e8f0;
  --fetchly-gray-300: #cbd5e1;
  --fetchly-gray-400: #94a3b8;
  --fetchly-gray-500: #64748b;
  --fetchly-gray-600: #475569;
  --fetchly-gray-700: #334155;
  --fetchly-gray-800: #1e293b;
  --fetchly-gray-900: #0f172a;

  /* Surface Colors */
  --fetchly-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --fetchly-surface: #ffffff;
  --fetchly-surface-elevated: #ffffff;
}

@layer base {
  * {
    @apply border-slate-200;
  }

  body {
    @apply text-slate-900 antialiased;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-slate-900;
  }
}

@layer components {
  /* Fetchly Modern Button Styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-xl px-6 py-3 text-sm font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg hover:shadow-xl;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700 focus:ring-indigo-500 hover:scale-105;
  }

  .btn-secondary {
    @apply bg-white text-indigo-600 border-2 border-indigo-600 hover:bg-indigo-50 focus:ring-indigo-500;
  }

  .btn-success {
    @apply bg-gradient-to-r from-emerald-500 to-teal-600 text-white hover:from-emerald-600 hover:to-teal-700 focus:ring-emerald-500;
  }

  .btn-danger {
    @apply bg-gradient-to-r from-red-500 to-pink-600 text-white hover:from-red-600 hover:to-pink-700 focus:ring-red-500;
  }

  .btn-warning {
    @apply bg-gradient-to-r from-amber-500 to-orange-600 text-white hover:from-amber-600 hover:to-orange-700 focus:ring-amber-500;
  }

  /* Fetchly Modern Card Styles - Use inline classes instead */

  /* Fetchly Modern Input Styles */
  .input {
    @apply block w-full rounded-xl border border-slate-200 px-4 py-3 text-sm placeholder-slate-400 focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500/20 bg-white/80 backdrop-blur-sm transition-all duration-300;
  }

  /* Fetchly Modern Badge Styles */
  .badge {
    @apply inline-flex items-center rounded-full px-3 py-1 text-xs font-medium;
  }

  /* Fetchly Modern Sidebar Styles */
  .sidebar-link {
    @apply flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 mx-2 my-1;
  }

  .sidebar-link-active {
    @apply bg-white/20 text-white shadow-lg backdrop-blur-sm;
  }

  .sidebar-link-inactive {
    @apply text-white/80 hover:text-white hover:bg-white/10;
  }

  /* Fetchly Stats Cards - Use inline classes instead */

  /* Fetchly Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Fetchly Custom Animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-bounce-gentle {
    animation: bounce-gentle 2s ease-in-out infinite;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Fetchly Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Fetchly Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
