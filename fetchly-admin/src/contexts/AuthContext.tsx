'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  User as FirebaseUser, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged 
} from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { AdminUser } from '@/types';

interface AuthContextType {
  user: FirebaseUser | null;
  adminUser: AdminUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      console.log('Auth state changed:', firebaseUser?.email);
      setUser(firebaseUser);

      if (firebaseUser) {
        try {
          console.log('Checking admin status for user:', firebaseUser.uid);

          // First check if user is an admin in the admins collection
          const adminDoc = await getDoc(doc(db, 'admins', firebaseUser.uid));
          console.log('Admin doc exists:', adminDoc.exists());

          if (adminDoc.exists()) {
            console.log('Found admin in admins collection');
            setAdminUser({
              id: adminDoc.id,
              ...adminDoc.data()
            } as AdminUser);
          } else {
            // If not in admins collection, check users collection for admin role
            console.log('Checking users collection for admin role');
            const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
            console.log('User doc exists:', userDoc.exists());
            console.log('User data:', userDoc.exists() ? userDoc.data() : 'No data');

            if (userDoc.exists() && userDoc.data()?.role === 'admin') {
              console.log('Found admin in users collection');
              // Create admin user object from user data
              const userData = userDoc.data();
              setAdminUser({
                id: userDoc.id,
                email: userData.email || firebaseUser.email || '',
                displayName: userData.displayName || firebaseUser.displayName || 'Admin',
                role: userData.superAdmin ? 'super_admin' : 'admin',
                permissions: [
                  'manage_users',
                  'manage_providers',
                  'manage_bookings',
                  'manage_payments',
                  'manage_content',
                  'view_analytics',
                  ...(userData.superAdmin ? ['manage_admins'] : [])
                ],
                createdAt: userData.createdAt?.toDate() || new Date(),
                lastLoginAt: userData.lastLoginAt?.toDate() || null
              } as AdminUser);
            } else {
              // User is not an admin, don't sign them out immediately
              console.log('User is not an admin');
              setAdminUser(null);
            }
          }
        } catch (error) {
          console.error('Error fetching admin user:', error);
          // Don't sign out on error, just set adminUser to null
          setAdminUser(null);
        }
      } else {
        setAdminUser(null);
      }
      
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
      setAdminUser(null);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  };

  const value = {
    user,
    adminUser,
    loading,
    signIn,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
