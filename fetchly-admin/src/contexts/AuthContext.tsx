'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  User as FirebaseUser, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged 
} from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { AdminUser } from '@/types';

interface AuthContextType {
  user: FirebaseUser | null;
  adminUser: AdminUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async (firebaseUser: FirebaseUser) => {
      setLoading(true);
      try {
        console.log('Checking admin status for user:', firebaseUser.uid);

        // First check if user is an admin in the admins collection
        try {
          const adminDoc = await getDoc(doc(db, 'admins', firebaseUser.uid));
          console.log('Admin doc exists:', adminDoc.exists());

          if (adminDoc.exists()) {
            console.log('Found admin in admins collection');
            const adminData = adminDoc.data();
            const adminUser: AdminUser = {
              id: adminDoc.id,
              email: adminData.email || firebaseUser.email || '',
              displayName: adminData.displayName || firebaseUser.displayName || 'Admin',
              role: adminData.superAdmin ? 'super_admin' : 'admin',
              permissions: adminData.permissions || [
                'manage_users',
                'manage_providers',
                'manage_bookings',
                'manage_payments',
                'manage_content',
                'view_analytics',
                ...(adminData.superAdmin ? ['manage_admins'] : [])
              ],
              createdAt: adminData.createdAt?.toDate() || new Date(),
              lastLoginAt: adminData.lastLoginAt?.toDate()
            };
            setAdminUser(adminUser);
            return;
          }
        } catch (adminError) {
          console.error('Error checking admins collection:', adminError);
        }

        // If not in admins collection, check users collection for admin role
        try {
          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
          console.log('User doc exists:', userDoc.exists());

          if (userDoc.exists()) {
            const userData = userDoc.data();
            console.log('User role:', userData.role);
            
            if (userData.role === 'admin' || userData.role === 'super_admin') {
              console.log('Found admin in users collection');
              const adminUser: AdminUser = {
                id: userDoc.id,
                email: userData.email || firebaseUser.email || '',
                displayName: userData.displayName || firebaseUser.displayName || 'Admin',
                role: userData.role,
                permissions: userData.permissions || [
                  'manage_users',
                  'manage_providers',
                  'manage_bookings',
                  ...(userData.role === 'super_admin' ? ['manage_admins'] : [])
                ],
                createdAt: userData.createdAt?.toDate() || new Date(),
                lastLoginAt: userData.lastLoginAt?.toDate()
              };
              setAdminUser(adminUser);
              return;
            }
          }
        } catch (userError) {
          console.error('Error checking users collection:', userError);
        }

        // If we get here, user is not an admin
        console.log('User is not an admin');
        setAdminUser(null);
      } catch (error) {
        console.error('Error checking admin status:', error);
        setAdminUser(null);
      } finally {
        setLoading(false);
      }
    };

    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      console.log('Auth state changed:', firebaseUser?.email);
      setUser(firebaseUser);

      if (firebaseUser) {
        checkAdminStatus(firebaseUser);
      } else {
        setAdminUser(null);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
      setAdminUser(null);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  };

  const value = {
    user,
    adminUser,
    loading,
    signIn,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
