'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User as FirebaseUser,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged
} from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { AdminUser } from '@/types';

interface AuthContextType {
  user: FirebaseUser | null;
  adminUser: AdminUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      console.log('🔥 Auth state changed:', firebaseUser?.email);
      setUser(firebaseUser);

      if (firebaseUser) {
        try {
          console.log('🔍 Checking ONLY admins collection for user:', firebaseUser.uid);

          // ONLY check the admins collection - NOTHING ELSE!
          const adminDoc = await getDoc(doc(db, 'admins', firebaseUser.uid));
          console.log('📄 Admin doc exists:', adminDoc.exists());

          if (adminDoc.exists()) {
            console.log('✅ SUCCESS: User found in admins collection');
            const adminData = adminDoc.data();
            console.log('📊 Admin data:', adminData);

            setAdminUser({
              id: adminDoc.id,
              email: adminData?.email || firebaseUser.email || '',
              displayName: adminData?.displayName || firebaseUser.displayName || 'Admin',
              role: adminData?.role || 'admin',
              permissions: adminData?.permissions || [],
              createdAt: adminData?.createdAt?.toDate() || new Date(),
              lastLoginAt: adminData?.lastLoginAt?.toDate() || null
            } as AdminUser);
          } else {
            console.log('❌ DENIED: User NOT found in admins collection');
            setAdminUser(null);
          }
        } catch (error) {
          console.error('💥 Error checking admins collection:', error);
          setAdminUser(null);
        }
      } else {
        console.log('👤 No user signed in');
        setAdminUser(null);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
      setAdminUser(null);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  };

  const value = {
    user,
    adminUser,
    loading,
    signIn,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
