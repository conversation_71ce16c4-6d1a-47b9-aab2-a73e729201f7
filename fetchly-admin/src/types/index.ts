// User Types
export interface User {
  id: string;
  email: string;
  displayName: string;
  photoURL?: string;
  phoneNumber?: string;
  createdAt: Date;
  lastLoginAt?: Date;
  isActive: boolean;
  role: 'user' | 'provider' | 'admin';
  profile?: UserProfile;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  address?: Address;
  pets?: Pet[];
  preferences?: UserPreferences;
}

export interface Pet {
  id: string;
  name: string;
  type: 'dog' | 'cat' | 'bird' | 'other';
  breed?: string;
  age?: number;
  weight?: number;
  specialNeeds?: string[];
  photos?: string[];
}

// Provider Types
export interface Provider {
  id: string;
  userId: string;
  businessName: string;
  description: string;
  services: Service[];
  location: Address;
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  isApproved: boolean;
  documents: Document[];
  availability: Availability[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  category: ServiceCategory;
  price: number;
  duration: number; // in minutes
  isActive: boolean;
}

export type ServiceCategory = 
  | 'pet-sitting'
  | 'dog-walking'
  | 'grooming'
  | 'veterinary'
  | 'training'
  | 'boarding'
  | 'daycare';

// Booking Types
export interface Booking {
  id: string;
  userId: string;
  providerId: string;
  serviceId: string;
  petIds: string[];
  startDate: Date;
  endDate: Date;
  status: BookingStatus;
  totalAmount: number;
  paymentStatus: PaymentStatus;
  specialInstructions?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type BookingStatus = 
  | 'pending'
  | 'confirmed'
  | 'in-progress'
  | 'completed'
  | 'cancelled'
  | 'disputed';

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'refunded'
  | 'failed';

// Financial Types
export interface Transaction {
  id: string;
  bookingId: string;
  amount: number;
  type: 'payment' | 'refund' | 'payout';
  status: 'pending' | 'completed' | 'failed';
  stripePaymentIntentId?: string;
  createdAt: Date;
}

export interface WithdrawalRequest {
  id: string;
  providerId: string;
  amount: number;
  status: 'pending' | 'approved' | 'rejected' | 'processed';
  requestedAt: Date;
  processedAt?: Date;
  bankDetails: BankDetails;
}

export interface BankDetails {
  accountNumber: string;
  routingNumber: string;
  accountHolderName: string;
  bankName: string;
}

// Common Types
export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface Document {
  id: string;
  type: 'license' | 'insurance' | 'certification' | 'id';
  url: string;
  status: 'pending' | 'approved' | 'rejected';
  uploadedAt: Date;
  reviewedAt?: Date;
}

export interface Availability {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  isAvailable: boolean;
}

export interface UserPreferences {
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy: {
    showProfile: boolean;
    shareLocation: boolean;
  };
}

// Admin Dashboard Types
export interface DashboardStats {
  totalUsers: number;
  totalProviders: number;
  totalBookings: number;
  totalRevenue: number;
  pendingApprovals: number;
  activeDisputes: number;
  monthlyGrowth: {
    users: number;
    providers: number;
    bookings: number;
    revenue: number;
  };
}

export interface AdminUser {
  id: string;
  email: string;
  displayName: string;
  role: 'super_admin' | 'admin' | 'moderator';
  permissions: Permission[];
  createdAt: Date;
  lastLoginAt?: Date;
}

export type Permission = 
  | 'manage_users'
  | 'manage_providers'
  | 'manage_bookings'
  | 'manage_payments'
  | 'manage_content'
  | 'view_analytics'
  | 'manage_admins';
