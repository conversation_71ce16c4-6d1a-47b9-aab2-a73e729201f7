[{"/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/analytics/page.tsx": "1", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/bookings/page.tsx": "2", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/page.tsx": "3", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/payments/page.tsx": "4", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/providers/page.tsx": "5", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/settings/page.tsx": "6", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/users/page.tsx": "7", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/layout.tsx": "8", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/login/page.tsx": "9", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/page.tsx": "10", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/components/layout/DashboardLayout.tsx": "11", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/contexts/AuthContext.tsx": "12", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/lib/firebase.ts": "13", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/lib/utils.ts": "14", "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/types/index.ts": "15"}, {"size": 10921, "mtime": 1754154692182, "results": "16", "hashOfConfig": "17"}, {"size": 13546, "mtime": 1754154663976, "results": "18", "hashOfConfig": "17"}, {"size": 7738, "mtime": 1754154701242, "results": "19", "hashOfConfig": "17"}, {"size": 16621, "mtime": 1754154711100, "results": "20", "hashOfConfig": "17"}, {"size": 12829, "mtime": 1754154673400, "results": "21", "hashOfConfig": "17"}, {"size": 17156, "mtime": 1754154721389, "results": "22", "hashOfConfig": "17"}, {"size": 11038, "mtime": 1754154259618, "results": "23", "hashOfConfig": "17"}, {"size": 628, "mtime": 1754154058750, "results": "24", "hashOfConfig": "17"}, {"size": 4596, "mtime": 1754154632919, "results": "25", "hashOfConfig": "17"}, {"size": 840, "mtime": 1754154184092, "results": "26", "hashOfConfig": "17"}, {"size": 6380, "mtime": 1754154141976, "results": "27", "hashOfConfig": "17"}, {"size": 2582, "mtime": 1754154038517, "results": "28", "hashOfConfig": "17"}, {"size": 856, "mtime": 1754154010205, "results": "29", "hashOfConfig": "17"}, {"size": 3487, "mtime": 1754154642535, "results": "30", "hashOfConfig": "17"}, {"size": 3902, "mtime": 1754154003004, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17pcbxn", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/analytics/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/bookings/page.tsx", ["77"], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/page.tsx", ["78", "79"], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/payments/page.tsx", ["80"], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/providers/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/settings/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/users/page.tsx", ["81", "82"], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/login/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/page.tsx", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/components/layout/DashboardLayout.tsx", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/lib/firebase.ts", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/types/index.ts", [], [], {"ruleId": "83", "severity": 1, "message": "84", "line": 99, "column": 20, "nodeType": null, "messageId": "85", "endLine": 99, "endColumn": 31}, {"ruleId": "83", "severity": 1, "message": "86", "line": 69, "column": 17, "nodeType": null, "messageId": "85", "endLine": 69, "endColumn": 25}, {"ruleId": "83", "severity": 1, "message": "87", "line": 70, "column": 28, "nodeType": null, "messageId": "85", "endLine": 70, "endColumn": 47}, {"ruleId": "83", "severity": 1, "message": "88", "line": 97, "column": 24, "nodeType": null, "messageId": "85", "endLine": 97, "endColumn": 39}, {"ruleId": "83", "severity": 1, "message": "89", "line": 73, "column": 17, "nodeType": null, "messageId": "85", "endLine": 73, "endColumn": 25}, {"ruleId": "90", "severity": 1, "message": "91", "line": 191, "column": 29, "nodeType": "92", "endLine": 191, "endColumn": 98}, "@typescript-eslint/no-unused-vars", "'setBookings' is assigned a value but never used.", "unusedVar", "'setStats' is assigned a value but never used.", "'setRecentActivities' is assigned a value but never used.", "'setTransactions' is assigned a value but never used.", "'setUsers' is assigned a value but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]