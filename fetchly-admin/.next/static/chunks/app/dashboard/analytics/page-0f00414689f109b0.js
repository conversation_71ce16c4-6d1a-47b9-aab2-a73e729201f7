(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{1652:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>K});var a=l(5155),t=l(844),r=l(5695),i=l(2115),n=l(858),d=l(8960),c=l(5713),m=l(6784),x=l(6884),o=l(2513),h=l(5500),j=l(9434),u=l(3540),v=l(3504),g=l(4754),f=l(6025),N=l(2071),b=l(4021),p=l(2366),y=l(3401),k=l(6690),w=l(170),A=l(8357),F=l(4811);let P=[{month:"Jan",revenue:12e3,bookings:45},{month:"Feb",revenue:15e3,bookings:52},{month:"Mar",revenue:18e3,bookings:68},{month:"Apr",revenue:22e3,bookings:78},{month:"May",revenue:25e3,bookings:89},{month:"Jun",revenue:28e3,bookings:95}],B=[{month:"Jan",users:120,providers:15},{month:"Feb",users:145,providers:18},{month:"Mar",users:178,providers:22},{month:"Apr",users:210,providers:28},{month:"May",users:245,providers:32},{month:"Jun",users:280,providers:38}],E=[{name:"Dog Walking",value:35,color:"#3B82F6"},{name:"Pet Sitting",value:25,color:"#10B981"},{name:"Grooming",value:20,color:"#F59E0B"},{name:"Training",value:12,color:"#EF4444"},{name:"Veterinary",value:8,color:"#8B5CF6"}],M=[{name:"PetCare Plus",bookings:45,revenue:1125},{name:"Happy Tails",bookings:38,revenue:950},{name:"Furry Friends",bookings:32,revenue:800},{name:"Pet Paradise",bookings:28,revenue:700},{name:"Loving Paws",bookings:25,revenue:625}];function K(){let{user:e,adminUser:s,loading:l}=(0,t.A)(),K=(0,r.useRouter)();if((0,i.useEffect)(()=>{l||e&&s||K.push("/login")},[e,s,l,K]),l)return(0,a.jsx)("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!e||!s)return null;let _=P[P.length-1].revenue,D=P[P.length-2].revenue,R=(0,j.tP)(_,D),T=P[P.length-1].bookings,C=P[P.length-2].bookings,J=(0,j.tP)(T,C);return(0,a.jsx)(n.A,{title:"Analytics",children:(0,a.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Analytics Dashboard"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Track performance metrics and business insights"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8",children:[(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(d.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Monthly Revenue"}),(0,a.jsxs)("dd",{className:"flex items-baseline",children:[(0,a.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:(0,j.vv)(_)}),(0,a.jsxs)("div",{className:"ml-2 flex items-baseline text-sm font-semibold ".concat(R>=0?"text-green-600":"text-red-600"),children:[R>=0?(0,a.jsx)(c.A,{className:"h-4 w-4 flex-shrink-0 self-center"}):(0,a.jsx)(m.A,{className:"h-4 w-4 flex-shrink-0 self-center"}),Math.abs(R).toFixed(1),"%"]})]})]})})]})}),(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Monthly Bookings"}),(0,a.jsxs)("dd",{className:"flex items-baseline",children:[(0,a.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:T}),(0,a.jsxs)("div",{className:"ml-2 flex items-baseline text-sm font-semibold ".concat(J>=0?"text-green-600":"text-red-600"),children:[J>=0?(0,a.jsx)(c.A,{className:"h-4 w-4 flex-shrink-0 self-center"}):(0,a.jsx)(m.A,{className:"h-4 w-4 flex-shrink-0 self-center"}),Math.abs(J).toFixed(1),"%"]})]})]})})]})}),(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(o.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active Users"}),(0,a.jsx)("dd",{className:"text-2xl font-semibold text-gray-900",children:B[B.length-1].users})]})})]})}),(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(h.A,{className:"h-8 w-8 text-yellow-600"})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active Providers"}),(0,a.jsx)("dd",{className:"text-2xl font-semibold text-gray-900",children:B[B.length-1].providers})]})})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Revenue Trend"}),(0,a.jsx)(u.u,{width:"100%",height:300,children:(0,a.jsxs)(v.b,{data:P,children:[(0,a.jsx)(g.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"month"}),(0,a.jsx)(N.h,{}),(0,a.jsx)(b.m,{formatter:e=>[(0,j.vv)(Number(e)),"Revenue"]}),(0,a.jsx)(p.N,{type:"monotone",dataKey:"revenue",stroke:"#3B82F6",strokeWidth:2})]})})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"User Growth"}),(0,a.jsx)(u.u,{width:"100%",height:300,children:(0,a.jsxs)(y.E,{data:B,children:[(0,a.jsx)(g.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"month"}),(0,a.jsx)(N.h,{}),(0,a.jsx)(b.m,{}),(0,a.jsx)(k.y,{dataKey:"users",fill:"#10B981"}),(0,a.jsx)(k.y,{dataKey:"providers",fill:"#F59E0B"})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Service Distribution"}),(0,a.jsx)(u.u,{width:"100%",height:300,children:(0,a.jsxs)(w.r,{children:[(0,a.jsx)(A.F,{data:E,cx:"50%",cy:"50%",outerRadius:80,fill:"#8884d8",dataKey:"value",label:e=>{let{name:s,percent:l}=e;return"".concat(s," ").concat((100*l).toFixed(0),"%")},children:E.map((e,s)=>(0,a.jsx)(F.f,{fill:e.color},"cell-".concat(s)))}),(0,a.jsx)(b.m,{})]})})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Top Providers"}),(0,a.jsx)("div",{className:"space-y-4",children:M.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:s+1})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.bookings," bookings"]})]})]}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(0,j.vv)(e.revenue)})]},e.name))})]})]})]})})}},9191:(e,s,l)=>{Promise.resolve().then(l.bind(l,1652))}},e=>{e.O(0,[135,965,72,556,158,477,441,964,358],()=>e(e.s=9191)),_N_E=e.O()}]);