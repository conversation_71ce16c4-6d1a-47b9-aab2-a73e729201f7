(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[508],{184:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},1541:(e,t,s)=>{Promise.resolve().then(s.bind(s,6533))},5233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},6533:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(5155),a=s(844),i=s(5695),l=s(2115),n=s(858),d=s(8046),c=s(5233);let o=l.forwardRef(function(e,t){let{title:s,titleId:r,...a}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},a),s?l.createElement("title",{id:r},s):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))});var x=s(184),p=s(9337),m=s(4500),u=s(9434);let h=[{id:"1",userId:"user1",businessName:"PetCare Plus",description:"Professional pet sitting and dog walking services",services:[{id:"1",name:"Dog Walking",description:"30-60 minute walks",category:"dog-walking",price:25,duration:30,isActive:!0},{id:"2",name:"Pet Sitting",description:"In-home pet care",category:"pet-sitting",price:40,duration:60,isActive:!0}],location:{street:"123 Main St",city:"San Francisco",state:"CA",zipCode:"94102",country:"USA"},rating:4.8,reviewCount:127,isVerified:!0,isApproved:!0,documents:[{id:"1",type:"license",url:"/docs/license1.pdf",status:"approved",uploadedAt:new Date("2024-01-10"),reviewedAt:new Date("2024-01-12")}],availability:[],createdAt:new Date("2024-01-10"),updatedAt:new Date("2024-01-25")},{id:"2",userId:"user2",businessName:"Happy Tails Pet Services",description:"Loving care for your furry friends",services:[{id:"3",name:"Pet Grooming",description:"Full grooming service",category:"grooming",price:60,duration:120,isActive:!0}],location:{street:"456 Oak Ave",city:"San Francisco",state:"CA",zipCode:"94103",country:"USA"},rating:4.5,reviewCount:89,isVerified:!1,isApproved:!1,documents:[{id:"2",type:"license",url:"/docs/license2.pdf",status:"pending",uploadedAt:new Date("2024-01-28")}],availability:[],createdAt:new Date("2024-01-28"),updatedAt:new Date("2024-01-28")}];function v(){let{user:e,adminUser:t,loading:s}=(0,a.A)(),v=(0,i.useRouter)(),[g,j]=(0,l.useState)(h),[f,w]=(0,l.useState)(""),[N,y]=(0,l.useState)("all");if((0,l.useEffect)(()=>{s||e&&t||v.push("/login")},[e,t,s,v]),s)return(0,r.jsx)("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!e||!t)return null;let b=g.filter(e=>{let t=e.businessName.toLowerCase().includes(f.toLowerCase())||e.description.toLowerCase().includes(f.toLowerCase()),s="all"===N||"approved"===N&&e.isApproved||"pending"===N&&!e.isApproved||"rejected"===N&&!e.isApproved;return t&&s});return(0,r.jsx)(n.A,{title:"Providers Management",children:(0,r.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Providers"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Manage service providers and their applications"})]}),(0,r.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,r.jsx)("button",{className:"btn btn-primary",children:"Export Providers"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6",children:[(0,r.jsxs)("div",{className:"card p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:g.filter(e=>e.isApproved).length}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Approved Providers"})]}),(0,r.jsxs)("div",{className:"card p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:g.filter(e=>!e.isApproved).length}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Pending Approval"})]}),(0,r.jsxs)("div",{className:"card p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:g.filter(e=>e.isVerified).length}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Verified Providers"})]})]}),(0,r.jsx)("div",{className:"card p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search providers...",className:"input pl-10",value:f,onChange:e=>w(e.target.value)})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{className:"input",value:N,onChange:e=>y(e.target.value),children:[(0,r.jsx)("option",{value:"all",children:"All Status"}),(0,r.jsx)("option",{value:"approved",children:"Approved"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"rejected",children:"Rejected"})]}),(0,r.jsxs)("button",{className:"btn btn-secondary",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"More Filters"]})]})]})}),(0,r.jsxs)("div",{className:"card overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Provider"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Services"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rating"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Applied"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-blue-700",children:(0,u.IM)(e.businessName)})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.businessName}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.location.city,", ",e.location.state]})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[e.services.length," services"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.services.slice(0,2).map(e=>e.name).join(", "),e.services.length>2&&"..."]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o,{className:"h-4 w-4 text-yellow-400 mr-1"}),(0,r.jsx)("span",{className:"text-sm text-gray-900",children:e.rating}),(0,r.jsxs)("span",{className:"text-sm text-gray-500 ml-1",children:["(",e.reviewCount,")"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("span",{className:"badge ".concat((0,u.qY)(e.isApproved?"approved":"pending")),children:e.isApproved?"Approved":"Pending"}),e.isVerified&&(0,r.jsx)("span",{className:"badge bg-blue-100 text-blue-800",children:"Verified"})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,u.Yq)(e.createdAt)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})}),!e.isApproved&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>{var t;return t=e.id,void j(e=>e.map(e=>e.id===t?{...e,isApproved:!0,isVerified:!0}:e))},className:"text-green-600 hover:text-green-900",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>{var t;return t=e.id,void j(e=>e.map(e=>e.id===t?{...e,isApproved:!1,isVerified:!1}:e))},className:"text-red-600 hover:text-red-900",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})]})})]},e.id))})]})}),0===b.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No providers found matching your criteria."})})]})]})})}},8046:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},9337:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(2115);let a=r.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})}},e=>{e.O(0,[135,965,72,556,477,441,964,358],()=>e(e.s=1541)),_N_E=e.O()}]);