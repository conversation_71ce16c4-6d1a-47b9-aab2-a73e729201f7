(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{250:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var l=t(5155),a=t(844),r=t(5695),n=t(2115),i=t(858),c=t(2513),o=t(4219),d=t(6884),m=t(8960),x=t(5713),h=t(6784);let u=n.forwardRef(function(e,s){let{title:t,titleId:l,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":l},a),t?n.createElement("title",{id:l},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}),g=n.forwardRef(function(e,s){let{title:t,titleId:l,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":l},a),t?n.createElement("title",{id:l},t):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var p=t(9434);let v={totalUsers:1247,totalProviders:89,totalBookings:456,totalRevenue:23450,pendingApprovals:12,activeDisputes:3,monthlyGrowth:{users:15.2,providers:8.7,bookings:22.1,revenue:18.9}},f=[{id:1,type:"booking",message:"New booking request from Sarah Johnson",time:"2 minutes ago",status:"pending"},{id:2,type:"provider",message:"Provider application approved for PetCare Plus",time:"15 minutes ago",status:"approved"},{id:3,type:"payment",message:"Payment of $125.00 processed successfully",time:"1 hour ago",status:"completed"},{id:4,type:"user",message:"New user registration: Mike Chen",time:"2 hours ago",status:"active"}];function j(){let{user:e,adminUser:s,loading:t}=(0,a.A)(),j=(0,r.useRouter)(),[w,N]=(0,n.useState)(v),[b,y]=(0,n.useState)(f);if((0,n.useEffect)(()=>{t||e&&s||j.push("/login")},[e,s,t,j]),t)return(0,l.jsx)("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!e||!s)return null;let k=[{name:"Total Users",value:w.totalUsers.toLocaleString(),change:w.monthlyGrowth.users,icon:c.A,color:"blue"},{name:"Active Providers",value:w.totalProviders.toLocaleString(),change:w.monthlyGrowth.providers,icon:o.A,color:"green"},{name:"Total Bookings",value:w.totalBookings.toLocaleString(),change:w.monthlyGrowth.bookings,icon:d.A,color:"purple"},{name:"Revenue",value:(0,p.vv)(w.totalRevenue),change:w.monthlyGrowth.revenue,icon:m.A,color:"yellow"}];return(0,l.jsx)(i.A,{title:"Dashboard",children:(0,l.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Welcome back, ",s.displayName,"!"]}),(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Here's what's happening with your Fetchly platform today."})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8",children:k.map(e=>(0,l.jsx)("div",{className:"card p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)(e.icon,{className:"h-8 w-8 text-".concat(e.color,"-600")})}),(0,l.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,l.jsxs)("dl",{children:[(0,l.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e.name}),(0,l.jsxs)("dd",{className:"flex items-baseline",children:[(0,l.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:e.value}),(0,l.jsxs)("div",{className:"ml-2 flex items-baseline text-sm font-semibold ".concat(e.change>=0?"text-green-600":"text-red-600"),children:[e.change>=0?(0,l.jsx)(x.A,{className:"h-4 w-4 flex-shrink-0 self-center"}):(0,l.jsx)(h.A,{className:"h-4 w-4 flex-shrink-0 self-center"}),(0,l.jsxs)("span",{className:"sr-only",children:[e.change>=0?"Increased":"Decreased"," by"]}),Math.abs(e.change),"%"]})]})]})})]})},e.name))}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,l.jsxs)("div",{className:"card p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-yellow-50 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(u,{className:"h-5 w-5 text-yellow-600 mr-3"}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Pending Provider Approvals"})]}),(0,l.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:w.pendingApprovals})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(u,{className:"h-5 w-5 text-red-600 mr-3"}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Active Disputes"})]}),(0,l.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:w.activeDisputes})]}),(0,l.jsx)("div",{className:"pt-3",children:(0,l.jsx)("button",{className:"w-full btn btn-primary",children:"View All Pending Items"})})]})]}),(0,l.jsxs)("div",{className:"card p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Activity"}),(0,l.jsxs)("div",{className:"space-y-4",children:[b.map(e=>(0,l.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)(g,{className:"h-5 w-5 text-green-500"})}),(0,l.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,l.jsx)("p",{className:"text-sm text-gray-900",children:e.message}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:e.time})]})]},e.id)),(0,l.jsx)("div",{className:"pt-3",children:(0,l.jsx)("button",{className:"w-full btn btn-secondary text-sm",children:"View All Activity"})})]})]})]})]})})}},5713:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var l=t(2115);let a=l.forwardRef(function(e,s){let{title:t,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?l.createElement("title",{id:a},t):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},6784:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var l=t(2115);let a=l.forwardRef(function(e,s){let{title:t,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?l.createElement("title",{id:a},t):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))})},7576:(e,s,t)=>{Promise.resolve().then(t.bind(t,250))}},e=>{e.O(0,[135,965,72,556,477,441,964,358],()=>e(e.s=7576)),_N_E=e.O()}]);