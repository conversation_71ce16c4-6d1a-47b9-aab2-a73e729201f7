(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[242],{184:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},2059:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(5155),r=s(844),l=s(5695),i=s(2115),n=s(858),c=s(8046),d=s(5233),o=s(184);let m=i.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?i.createElement("title",{id:a},s):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}),x=i.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?i.createElement("title",{id:a},s):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))});var h=s(9434);let p=[{id:"1",email:"<EMAIL>",displayName:"Sarah Johnson",phoneNumber:"+****************",createdAt:new Date("2024-01-15"),lastLoginAt:new Date("2024-01-30"),isActive:!0,role:"user",profile:{firstName:"Sarah",lastName:"Johnson",pets:[{id:"1",name:"Max",type:"dog",breed:"Golden Retriever",age:3,weight:65,specialNeeds:[],photos:[]}]}},{id:"2",email:"<EMAIL>",displayName:"Mike Chen",phoneNumber:"+****************",createdAt:new Date("2024-01-20"),lastLoginAt:new Date("2024-01-29"),isActive:!0,role:"user",profile:{firstName:"Mike",lastName:"Chen",pets:[{id:"2",name:"Luna",type:"cat",breed:"Persian",age:2,weight:8,specialNeeds:[],photos:[]}]}},{id:"3",email:"<EMAIL>",displayName:"Emily Davis",phoneNumber:"+****************",createdAt:new Date("2024-01-10"),lastLoginAt:new Date("2024-01-25"),isActive:!1,role:"user",profile:{firstName:"Emily",lastName:"Davis",pets:[]}}];function u(){let{user:e,adminUser:t,loading:s}=(0,r.A)(),u=(0,l.useRouter)(),[v,f]=(0,i.useState)(p),[g,N]=(0,i.useState)(""),[j,w]=(0,i.useState)("all");if((0,i.useEffect)(()=>{s||e&&t||u.push("/login")},[e,t,s,u]),s)return(0,a.jsx)("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!e||!t)return null;let y=v.filter(e=>{let t=e.displayName.toLowerCase().includes(g.toLowerCase())||e.email.toLowerCase().includes(g.toLowerCase()),s="all"===j||"active"===j&&e.isActive||"inactive"===j&&!e.isActive;return t&&s});return(0,a.jsx)(n.A,{title:"Users Management",children:(0,a.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Users"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Manage all registered users on the platform"})]}),(0,a.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,a.jsx)("button",{className:"btn btn-primary",children:"Export Users"})})]}),(0,a.jsx)("div",{className:"card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search users...",className:"input pl-10",value:g,onChange:e=>N(e.target.value)})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{className:"input",value:j,onChange:e=>w(e.target.value),children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"})]}),(0,a.jsxs)("button",{className:"btn btn-secondary",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"More Filters"]})]})]})}),(0,a.jsxs)("div",{className:"card overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pets"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Login"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:y.map(e=>{var t,s,r;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:e.photoURL?(0,a.jsx)("img",{className:"h-10 w-10 rounded-full",src:e.photoURL,alt:""}):(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:(0,h.IM)(e.displayName)})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.displayName}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.email}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.phoneNumber||"No phone"})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[(null==(s=e.profile)||null==(t=s.pets)?void 0:t.length)||0," pets"]}),(null==(r=e.profile)?void 0:r.pets)&&e.profile.pets.length>0&&(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.profile.pets[0].name," (",e.profile.pets[0].type,")",e.profile.pets.length>1&&" +".concat(e.profile.pets.length-1," more")]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"badge ".concat((0,h.qY)(e.isActive?"active":"inactive")),children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,h.Yq)(e.createdAt)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.lastLoginAt?(0,h.Yq)(e.lastLoginAt):"Never"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{className:"text-gray-600 hover:text-gray-900",children:(0,a.jsx)(m,{className:"h-4 w-4"})}),(0,a.jsx)("button",{className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(x,{className:"h-4 w-4"})})]})})]},e.id)})})]})}),0===y.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No users found matching your criteria."})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",(0,a.jsx)("span",{className:"font-medium",children:"1"})," to ",(0,a.jsx)("span",{className:"font-medium",children:y.length})," of"," ",(0,a.jsx)("span",{className:"font-medium",children:v.length})," results"]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"btn btn-secondary",disabled:!0,children:"Previous"}),(0,a.jsx)("button",{className:"btn btn-secondary",disabled:!0,children:"Next"})]})]})]})})}},5233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},8046:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},8343:(e,t,s)=>{Promise.resolve().then(s.bind(s,2059))}},e=>{e.O(0,[135,965,72,556,477,441,964,358],()=>e(e.s=8343)),_N_E=e.O()}]);