(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[274],{184:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},3940:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(5155),r=s(844),l=s(5695),i=s(2115),n=s(858),d=s(6884),c=s(8960),o=s(8046),m=s(5233),x=s(184),p=s(9434);let u=[{id:"1",userId:"user1",providerId:"provider1",serviceId:"service1",petIds:["pet1"],startDate:new Date("2024-02-15T10:00:00"),endDate:new Date("2024-02-15T11:00:00"),status:"confirmed",totalAmount:25,paymentStatus:"paid",specialInstructions:"Max loves treats and needs to be walked slowly",createdAt:new Date("2024-02-10"),updatedAt:new Date("2024-02-12")},{id:"2",userId:"user2",providerId:"provider1",serviceId:"service2",petIds:["pet2"],startDate:new Date("2024-02-16T14:00:00"),endDate:new Date("2024-02-16T15:00:00"),status:"pending",totalAmount:40,paymentStatus:"pending",createdAt:new Date("2024-02-14"),updatedAt:new Date("2024-02-14")},{id:"3",userId:"user3",providerId:"provider2",serviceId:"service3",petIds:["pet3"],startDate:new Date("2024-02-12T09:00:00"),endDate:new Date("2024-02-12T11:00:00"),status:"completed",totalAmount:60,paymentStatus:"paid",createdAt:new Date("2024-02-08"),updatedAt:new Date("2024-02-12")},{id:"4",userId:"user1",providerId:"provider1",serviceId:"service1",petIds:["pet1"],startDate:new Date("2024-02-20T16:00:00"),endDate:new Date("2024-02-20T17:00:00"),status:"cancelled",totalAmount:25,paymentStatus:"refunded",createdAt:new Date("2024-02-18"),updatedAt:new Date("2024-02-19")}],h={user1:{name:"Sarah Johnson",email:"<EMAIL>"},user2:{name:"Mike Chen",email:"<EMAIL>"},user3:{name:"Emily Davis",email:"<EMAIL>"}},v={provider1:{name:"PetCare Plus"},provider2:{name:"Happy Tails Pet Services"}},g={service1:{name:"Dog Walking"},service2:{name:"Pet Sitting"},service3:{name:"Pet Grooming"}};function j(){let{user:e,adminUser:t,loading:s}=(0,r.A)(),j=(0,l.useRouter)(),[w,N]=(0,i.useState)(u),[f,y]=(0,i.useState)(""),[b,k]=(0,i.useState)("all");if((0,i.useEffect)(()=>{s||e&&t||j.push("/login")},[e,t,s,j]),s)return(0,a.jsx)("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!e||!t)return null;let A=w.filter(e=>{let t=h[e.userId],s=v[e.providerId],a=g[e.serviceId],r=(null==t?void 0:t.name.toLowerCase().includes(f.toLowerCase()))||(null==t?void 0:t.email.toLowerCase().includes(f.toLowerCase()))||(null==s?void 0:s.name.toLowerCase().includes(f.toLowerCase()))||(null==a?void 0:a.name.toLowerCase().includes(f.toLowerCase()))||e.id.toLowerCase().includes(f.toLowerCase()),l="all"===b||e.status===b;return r&&l}),D=w.length,C=w.filter(e=>"pending"===e.status).length,I=w.filter(e=>"completed"===e.status).length,L=w.filter(e=>"paid"===e.paymentStatus).reduce((e,t)=>e+t.totalAmount,0);return(0,a.jsx)(n.A,{title:"Bookings Management",children:(0,a.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Bookings"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Manage all bookings and appointments"})]}),(0,a.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,a.jsx)("button",{className:"btn btn-primary",children:"Export Bookings"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-4 mb-6",children:[(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:D}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Bookings"})]})]})}),(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-yellow-600"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:C}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Pending"})]})]})}),(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:I}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Completed"})]})]})}),(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 text-purple-600"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:(0,p.vv)(L)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Revenue"})]})]})})]}),(0,a.jsx)("div",{className:"card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search bookings...",className:"input pl-10",value:f,onChange:e=>y(e.target.value)})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{className:"input",value:b,onChange:e=>k(e.target.value),children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"confirmed",children:"Confirmed"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,a.jsxs)("button",{className:"btn btn-secondary",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"More Filters"]})]})]})}),(0,a.jsxs)("div",{className:"card overflow-hidden",children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Booking ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Provider"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Service"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date & Time"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:A.map(e=>{let t=h[e.userId],s=v[e.providerId],r=g[e.serviceId];return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["#",e.id]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Created ",(0,p.Yq)(e.createdAt)]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:null==t?void 0:t.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:null==t?void 0:t.email})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:null==s?void 0:s.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:null==r?void 0:r.name})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:(0,p.Yq)(e.startDate)}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.startDate.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})," -",e.endDate.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(0,p.vv)(e.totalAmount)}),(0,a.jsx)("div",{className:"text-xs ".concat((0,p.qY)(e.paymentStatus)),children:e.paymentStatus})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"badge ".concat((0,p.qY)(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})})]},e.id)})})]})}),0===A.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No bookings found matching your criteria."})})]})]})})}},5233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},6077:(e,t,s)=>{Promise.resolve().then(s.bind(s,3940))},8046:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})}},e=>{e.O(0,[135,965,72,556,477,441,964,358],()=>e(e.s=6077)),_N_E=e.O()}]);