(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[631],{5608:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(5155),i=s(844),n=s(5695),l=s(2115),r=s(858),c=s(5246),o=s(8828),m=s(8960);let d=l.forwardRef(function(e,t){let{title:s,titleId:a,...i}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?l.createElement("title",{id:a},s):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))});function x(){let{user:e,adminUser:t,loading:s}=(0,i.A)(),x=(0,n.useRouter)(),[u,h]=(0,l.useState)("general"),[g,p]=(0,l.useState)({general:{platformName:"Fetchly",supportEmail:"<EMAIL>",maintenanceMode:!1,allowNewRegistrations:!0},notifications:{emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,weeklyReports:!0,monthlyReports:!0},payments:{stripePublishableKey:"pk_test_...",stripeSecretKey:"sk_test_...",platformFeePercentage:10,minimumWithdrawal:50,payoutSchedule:"weekly"},security:{requireTwoFactor:!1,sessionTimeout:30,passwordMinLength:8,requirePasswordChange:90}});if((0,l.useEffect)(()=>{s||e&&t||x.push("/login")},[e,t,s,x]),s)return(0,a.jsx)("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!e||!t)return null;let y=[{id:"general",name:"General",icon:c.A},{id:"notifications",name:"Notifications",icon:o.A},{id:"payments",name:"Payments",icon:m.A},{id:"security",name:"Security",icon:d}],f=(e,t,s)=>{p(a=>({...a,[e]:{...a[e],[t]:s}}))};return(0,a.jsx)(r.A,{title:"Settings",children:(0,a.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Settings"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Manage platform configuration and preferences"})]}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsx)("div",{className:"lg:w-64",children:(0,a.jsx)("nav",{className:"space-y-1",children:y.map(e=>(0,a.jsxs)("button",{onClick:()=>h(e.id),className:"w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ".concat(u===e.id?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,a.jsx)(e.icon,{className:"h-5 w-5 mr-3"}),e.name]},e.id))})}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"card p-6",children:["general"===u&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"General Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Platform Name"}),(0,a.jsx)("input",{type:"text",className:"input",value:g.general.platformName,onChange:e=>f("general","platformName",e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Support Email"}),(0,a.jsx)("input",{type:"email",className:"input",value:g.general.supportEmail,onChange:e=>f("general","supportEmail",e.target.value)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Maintenance Mode"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Temporarily disable the platform for maintenance"})]}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:g.general.maintenanceMode,onChange:e=>f("general","maintenanceMode",e.target.checked)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Allow New Registrations"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Allow new users to register on the platform"})]}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:g.general.allowNewRegistrations,onChange:e=>f("general","allowNewRegistrations",e.target.checked)})]})]})]}),"notifications"===u&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Notification Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Receive notifications via email"})]}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:g.notifications.emailNotifications,onChange:e=>f("notifications","emailNotifications",e.target.checked)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Push Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Receive push notifications in browser"})]}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:g.notifications.pushNotifications,onChange:e=>f("notifications","pushNotifications",e.target.checked)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"SMS Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Receive notifications via SMS"})]}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:g.notifications.smsNotifications,onChange:e=>f("notifications","smsNotifications",e.target.checked)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Weekly Reports"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Receive weekly analytics reports"})]}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:g.notifications.weeklyReports,onChange:e=>f("notifications","weeklyReports",e.target.checked)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Monthly Reports"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Receive monthly analytics reports"})]}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:g.notifications.monthlyReports,onChange:e=>f("notifications","monthlyReports",e.target.checked)})]})]})]}),"payments"===u&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Payment Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Platform Fee Percentage"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"number",className:"input pr-8",value:g.payments.platformFeePercentage,onChange:e=>f("payments","platformFeePercentage",Number(e.target.value))}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"text-gray-500 sm:text-sm",children:"%"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minimum Withdrawal Amount"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"text-gray-500 sm:text-sm",children:"$"})}),(0,a.jsx)("input",{type:"number",className:"input pl-8",value:g.payments.minimumWithdrawal,onChange:e=>f("payments","minimumWithdrawal",Number(e.target.value))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payout Schedule"}),(0,a.jsxs)("select",{className:"input",value:g.payments.payoutSchedule,onChange:e=>f("payments","payoutSchedule",e.target.value),children:[(0,a.jsx)("option",{value:"daily",children:"Daily"}),(0,a.jsx)("option",{value:"weekly",children:"Weekly"}),(0,a.jsx)("option",{value:"monthly",children:"Monthly"})]})]})]})]}),"security"===u&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Security Settings"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Require Two-Factor Authentication"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Require 2FA for all admin accounts"})]}),(0,a.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",checked:g.security.requireTwoFactor,onChange:e=>f("security","requireTwoFactor",e.target.checked)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Session Timeout (minutes)"}),(0,a.jsx)("input",{type:"number",className:"input",value:g.security.sessionTimeout,onChange:e=>f("security","sessionTimeout",Number(e.target.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Minimum Password Length"}),(0,a.jsx)("input",{type:"number",className:"input",value:g.security.passwordMinLength,onChange:e=>f("security","passwordMinLength",Number(e.target.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password Change Required (days)"}),(0,a.jsx)("input",{type:"number",className:"input",value:g.security.requirePasswordChange,onChange:e=>f("security","requirePasswordChange",Number(e.target.value))})]})]})]}),(0,a.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{onClick:()=>{console.log("Saving settings:",g)},className:"btn btn-primary",children:"Save Changes"})})})]})})]})]})})}},5754:(e,t,s)=>{Promise.resolve().then(s.bind(s,5608))}},e=>{e.O(0,[135,965,72,556,477,441,964,358],()=>e(e.s=5754)),_N_E=e.O()}]);