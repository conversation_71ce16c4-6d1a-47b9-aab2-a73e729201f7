(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[889],{184:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},2724:(e,t,s)=>{Promise.resolve().then(s.bind(s,8023))},5713:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},6784:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))})},8023:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(5155),r=s(844),n=s(5695),i=s(2115),l=s(858),d=s(5713),c=s(6784),x=s(8960),o=s(8046),m=s(184),p=s(9337),u=s(4500),h=s(9434);let g=[{id:"1",bookingId:"booking1",amount:25,type:"payment",status:"completed",stripePaymentIntentId:"pi_1234567890",createdAt:new Date("2024-02-15T10:30:00")},{id:"2",bookingId:"booking2",amount:40,type:"payment",status:"pending",stripePaymentIntentId:"pi_0987654321",createdAt:new Date("2024-02-16T14:15:00")},{id:"3",bookingId:"booking3",amount:60,type:"payment",status:"completed",stripePaymentIntentId:"pi_1122334455",createdAt:new Date("2024-02-12T09:45:00")},{id:"4",bookingId:"booking4",amount:25,type:"refund",status:"completed",stripePaymentIntentId:"pi_5544332211",createdAt:new Date("2024-02-19T16:20:00")}],y=[{id:"1",providerId:"provider1",amount:150,status:"pending",requestedAt:new Date("2024-02-18T10:00:00"),bankDetails:{accountNumber:"****1234",routingNumber:"*********",accountHolderName:"PetCare Plus LLC",bankName:"Chase Bank"}},{id:"2",providerId:"provider2",amount:89.5,status:"approved",requestedAt:new Date("2024-02-16T14:30:00"),processedAt:new Date("2024-02-17T09:15:00"),bankDetails:{accountNumber:"****5678",routingNumber:"*********",accountHolderName:"Happy Tails Pet Services",bankName:"Wells Fargo"}}],v={provider1:{name:"PetCare Plus"},provider2:{name:"Happy Tails Pet Services"}};function j(){let{user:e,adminUser:t,loading:s}=(0,r.A)(),j=(0,n.useRouter)(),[N,w]=(0,i.useState)(g),[b,f]=(0,i.useState)(y),[k,A]=(0,i.useState)("transactions"),[I,E]=(0,i.useState)("");if((0,i.useEffect)(()=>{s||e&&t||j.push("/login")},[e,t,s,j]),s)return(0,a.jsx)("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!e||!t)return null;let D=N.filter(e=>"payment"===e.type&&"completed"===e.status).reduce((e,t)=>e+t.amount,0),C=N.filter(e=>"refund"===e.type&&"completed"===e.status).reduce((e,t)=>e+t.amount,0),P=b.filter(e=>"pending"===e.status).length,L=b.filter(e=>"pending"===e.status).reduce((e,t)=>e+t.amount,0);return(0,a.jsx)(l.A,{title:"Payments Management",children:(0,a.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Payments"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Manage transactions, refunds, and provider withdrawals"})]}),(0,a.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,a.jsx)("button",{className:"btn btn-primary",children:"Export Financial Data"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-4 mb-6",children:[(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,h.vv)(D)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Revenue"})]})]})}),(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)(c.A,{className:"h-6 w-6 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,h.vv)(C)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Total Refunds"})]})]})}),(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(x.A,{className:"h-6 w-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:P}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Pending Withdrawals"})]})]})}),(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(x.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,h.vv)(L)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Pending Amount"})]})]})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200 mb-6",children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsx)("button",{onClick:()=>A("transactions"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("transactions"===k?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Transactions"}),(0,a.jsx)("button",{onClick:()=>A("withdrawals"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("withdrawals"===k?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Withdrawal Requests"})]})}),(0,a.jsx)("div",{className:"card p-4 mb-6",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search ".concat(k,"..."),className:"input pl-10",value:I,onChange:e=>E(e.target.value)})]})}),"transactions"===k?(0,a.jsx)("div",{className:"card overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Transaction ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Booking ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["#",e.id]}),e.stripePaymentIntentId&&(0,a.jsx)("div",{className:"text-xs text-gray-500",children:e.stripePaymentIntentId})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["#",e.bookingId]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"badge ".concat("payment"===e.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.type})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:(0,h.vv)(e.amount)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"badge ".concat((0,h.qY)(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,h.Yq)(e.createdAt)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})})]},e.id))})]})})}):(0,a.jsx)("div",{className:"card overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Request ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Provider"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Bank Details"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Requested"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(e=>{var t;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",e.id]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:null==(t=v[e.providerId])?void 0:t.name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:(0,h.vv)(e.amount)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.bankDetails.bankName}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.bankDetails.accountNumber})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"badge ".concat((0,h.qY)(e.status)),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,h.Yq)(e.requestedAt)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),"pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>{var t;return t=e.id,void f(e=>e.map(e=>e.id===t?{...e,status:"approved",processedAt:new Date}:e))},className:"text-green-600 hover:text-green-900",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>{var t;return t=e.id,void f(e=>e.map(e=>e.id===t?{...e,status:"rejected",processedAt:new Date}:e))},className:"text-red-600 hover:text-red-900",children:(0,a.jsx)(u.A,{className:"h-4 w-4"})})]})]})})]},e.id)})})]})})})]})})}},8046:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},9337:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})}},e=>{e.O(0,[135,965,72,556,477,441,964,358],()=>e(e.s=2724)),_N_E=e.O()}]);