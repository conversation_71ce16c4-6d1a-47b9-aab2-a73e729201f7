(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{184:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(2115);let a=s.forwardRef(function(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},844:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>x,A:()=>h});var s=r(5155),a=r(2115),n=r(5404),i=r(5317),l=r(3915),o=r(7505);let d=0===(0,l.Dk)().length?(0,l.Wp)({apiKey:"your_firebase_api_key",authDomain:"your_project.firebaseapp.com",projectId:"your_project_id",storageBucket:"your_project.appspot.com",messagingSenderId:"your_sender_id",appId:"your_app_id"}):(0,l.Dk)()[0],c=(0,n.xI)(d),u=(0,i.aU)(d);(0,o.c7)(d);let m=(0,a.createContext)(void 0);function h(){let e=(0,a.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function x(e){let{children:t}=e,[r,l]=(0,a.useState)(null),[o,d]=(0,a.useState)(null),[h,x]=(0,a.useState)(!0);(0,a.useEffect)(()=>(0,n.hg)(c,async e=>{if(l(e),e)try{let t=await (0,i.x7)((0,i.H9)(u,"admins",e.uid));t.exists()?d({id:t.id,...t.data()}):(await (0,n.CI)(c),d(null))}catch(e){console.error("Error fetching admin user:",e),d(null)}else d(null);x(!1)}),[]);let p=async(e,t)=>{try{await (0,n.x9)(c,e,t)}catch(e){throw console.error("Sign in error:",e),e}},y=async()=>{try{await (0,n.CI)(c),d(null)}catch(e){throw console.error("Sign out error:",e),e}};return(0,s.jsx)(m.Provider,{value:{user:r,adminUser:o,loading:h,signIn:p,signOut:y},children:t})}},2967:(e,t,r)=>{Promise.resolve().then(r.bind(r,3335))},3335:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(5155),a=r(2115),n=r(5695),i=r(844);let l=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});var o=r(184);function d(){let[e,t]=(0,a.useState)(""),[r,d]=(0,a.useState)(""),[c,u]=(0,a.useState)(!1),[m,h]=(0,a.useState)(!1),[x,p]=(0,a.useState)(""),{signIn:y}=(0,i.A)(),f=(0,n.useRouter)(),g=async t=>{t.preventDefault(),h(!0),p("");try{await y(e,r),f.push("/dashboard")}catch(e){p(e.message||"Failed to sign in")}finally{h(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,s.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Fetchly Admin"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Sign in to your administrator account"})]}),(0,s.jsx)("form",{className:"mt-8 space-y-6",onSubmit:g,children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 space-y-4",children:[x&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:x}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"input",placeholder:"Enter your email",value:e,onChange:e=>t(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:c?"text":"password",autoComplete:"current-password",required:!0,className:"input pr-10",placeholder:"Enter your password",value:r,onChange:e=>d(e.target.value)}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>u(!c),children:c?(0,s.jsx)(l,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(o.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:m,className:"w-full btn btn-primary py-3 text-base font-medium",children:m?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})]})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Fetchly Admin Dashboard - Secure Access Only"})})]})})}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})}},e=>{e.O(0,[135,965,72,441,964,358],()=>e(e.s=2967)),_N_E=e.O()}]);