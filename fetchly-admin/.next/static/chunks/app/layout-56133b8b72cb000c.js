(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},844:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>_,A:()=>p});var o=t(5155),a=t(2115),n=t(5404),s=t(5317),i=t(3915),l=t(7505);let u=0===(0,i.Dk)().length?(0,i.Wp)({apiKey:"your_firebase_api_key",authDomain:"your_project.firebaseapp.com",projectId:"your_project_id",storageBucket:"your_project.appspot.com",messagingSenderId:"your_sender_id",appId:"your_app_id"}):(0,i.Dk)()[0],c=(0,n.xI)(u),d=(0,s.aU)(u);(0,l.c7)(u);let h=(0,a.createContext)(void 0);function p(){let e=(0,a.useContext)(h);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function _(e){let{children:r}=e,[t,i]=(0,a.useState)(null),[l,u]=(0,a.useState)(null),[p,_]=(0,a.useState)(!0);(0,a.useEffect)(()=>(0,n.hg)(c,async e=>{if(i(e),e)try{let r=await (0,s.x7)((0,s.H9)(d,"admins",e.uid));r.exists()?u({id:r.id,...r.data()}):(await (0,n.CI)(c),u(null))}catch(e){console.error("Error fetching admin user:",e),u(null)}else u(null);_(!1)}),[]);let y=async(e,r)=>{try{await (0,n.x9)(c,e,r)}catch(e){throw console.error("Sign in error:",e),e}},m=async()=>{try{await (0,n.CI)(c),u(null)}catch(e){throw console.error("Sign out error:",e),e}};return(0,o.jsx)(h.Provider,{value:{user:t,adminUser:l,loading:p,signIn:y,signOut:m},children:r})}},4551:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,8346,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,844))},8346:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}},e=>{e.O(0,[838,135,965,72,441,964,358],()=>e(e.s=4551)),_N_E=e.O()}]);