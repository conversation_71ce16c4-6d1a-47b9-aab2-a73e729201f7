"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[477],{844:(e,s,r)=>{r.d(s,{AuthProvider:()=>g,A:()=>h});var t=r(5155),a=r(2115),l=r(5404),n=r(5317),i=r(3915),c=r(7505);let d=0===(0,i.Dk)().length?(0,i.Wp)({apiKey:"your_firebase_api_key",authDomain:"your_project.firebaseapp.com",projectId:"your_project_id",storageBucket:"your_project.appspot.com",messagingSenderId:"your_sender_id",appId:"your_app_id"}):(0,i.Dk)()[0],o=(0,l.xI)(d),x=(0,n.aU)(d);(0,c.c7)(d);let m=(0,a.createContext)(void 0);function h(){let e=(0,a.useContext)(m);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function g(e){let{children:s}=e,[r,i]=(0,a.useState)(null),[c,d]=(0,a.useState)(null),[h,g]=(0,a.useState)(!0);(0,a.useEffect)(()=>(0,l.hg)(o,async e=>{if(i(e),e)try{let s=await (0,n.x7)((0,n.H9)(x,"admins",e.uid));s.exists()?d({id:s.id,...s.data()}):(await (0,l.CI)(o),d(null))}catch(e){console.error("Error fetching admin user:",e),d(null)}else d(null);g(!1)}),[]);let u=async(e,s)=>{try{await (0,l.x9)(o,e,s)}catch(e){throw console.error("Sign in error:",e),e}},y=async()=>{try{await (0,l.CI)(o),d(null)}catch(e){throw console.error("Sign out error:",e),e}};return(0,t.jsx)(m.Provider,{value:{user:r,adminUser:c,loading:h,signIn:u,signOut:y},children:s})}},858:(e,s,r)=>{r.d(s,{A:()=>j});var t=r(5155),a=r(2115),l=r(844),n=r(5695),i=r(9994),c=r(2513),d=r(4219),o=r(6884),x=r(8960),m=r(5500),h=r(5246),g=r(4500),u=r(5596),y=r(9598),f=r(8828),b=r(9434);let p=[{name:"Dashboard",href:"/dashboard",icon:i.A},{name:"Users",href:"/dashboard/users",icon:c.A},{name:"Providers",href:"/dashboard/providers",icon:d.A},{name:"Bookings",href:"/dashboard/bookings",icon:o.A},{name:"Payments",href:"/dashboard/payments",icon:x.A},{name:"Analytics",href:"/dashboard/analytics",icon:m.A},{name:"Settings",href:"/dashboard/settings",icon:h.A}];function j(e){var s,r;let{children:i,title:c}=e,[d,o]=(0,a.useState)(!1),{adminUser:x,signOut:m}=(0,l.A)(),h=(0,n.useRouter)(),j=async()=>{try{await m(),h.push("/login")}catch(e){console.error("Sign out error:",e)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-slate-50",children:[(0,t.jsxs)("div",{className:(0,b.cn)("fixed inset-0 z-50 lg:hidden",d?"block":"hidden"),children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>o(!1)}),(0,t.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl",children:[(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-sm",children:"F"})}),(0,t.jsx)("span",{className:"ml-2 text-lg font-semibold text-gray-900",children:"Fetchly Admin"})]}),(0,t.jsx)("button",{onClick:()=>o(!1),className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(g.A,{className:"h-6 w-6"})})]}),(0,t.jsx)("nav",{className:"flex-1 px-4 py-4 space-y-1",children:p.map(e=>(0,t.jsxs)("a",{href:e.href,className:"sidebar-link sidebar-link-inactive",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5 mr-3"}),e.name]},e.name))})]})]}),(0,t.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,t.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm",children:[(0,t.jsx)("div",{className:"flex h-16 items-center px-4 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-sm",children:"F"})}),(0,t.jsx)("span",{className:"ml-2 text-lg font-semibold text-gray-900",children:"Fetchly Admin"})]})}),(0,t.jsx)("nav",{className:"flex-1 px-4 py-4 space-y-1",children:p.map(e=>(0,t.jsxs)("a",{href:e.href,className:"sidebar-link sidebar-link-inactive",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5 mr-3"}),e.name]},e.name))}),(0,t.jsxs)("div",{className:"p-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-gray-600 font-medium text-sm",children:(null==x||null==(s=x.displayName)?void 0:s.charAt(0))||"A"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:null==x?void 0:x.displayName}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:null==x?void 0:x.role})]})]}),(0,t.jsxs)("button",{onClick:j,className:"flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})}),(0,t.jsxs)("div",{className:"lg:pl-64",children:[(0,t.jsx)("div",{className:"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm",children:(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("button",{onClick:()=>o(!0),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,t.jsx)(y.A,{className:"h-6 w-6"})}),c&&(0,t.jsx)("h1",{className:"ml-4 lg:ml-0 text-xl font-semibold text-gray-900",children:c})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,t.jsx)(f.A,{className:"h-6 w-6"})}),(0,t.jsx)("div",{className:"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center lg:hidden",children:(0,t.jsx)("span",{className:"text-gray-600 font-medium text-sm",children:(null==x||null==(r=x.displayName)?void 0:r.charAt(0))||"A"})})]})]})}),(0,t.jsx)("main",{className:"flex-1",children:(0,t.jsx)("div",{className:"py-6",children:i})})]})]})}},9434:(e,s,r)=>{r.d(s,{IM:()=>x,Yq:()=>c,cn:()=>n,qY:()=>d,tP:()=>o,vv:()=>i});var t=r(2596),a=r(9688),l=r(3319);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}function c(e){let s="string"==typeof e?new Date(e):e;return(0,l.GP)(s,"MMM dd, yyyy")}function d(e){return({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800","in-progress":"bg-purple-100 text-purple-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800",disputed:"bg-orange-100 text-orange-800",paid:"bg-green-100 text-green-800",refunded:"bg-gray-100 text-gray-800",failed:"bg-red-100 text-red-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",verified:"bg-blue-100 text-blue-800",unverified:"bg-yellow-100 text-yellow-800"})[e.toLowerCase()]||"bg-gray-100 text-gray-800"}function o(e,s){return 0===s?100*(e>0):(e-s)/s*100}function x(e){return e.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)}}}]);