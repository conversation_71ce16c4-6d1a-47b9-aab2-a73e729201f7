{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, formatDistanceToNow } from 'date-fns';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy');\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy HH:mm');\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return formatDistanceToNow(dateObj, { addSuffix: true });\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    // Booking statuses\n    pending: 'bg-yellow-100 text-yellow-800',\n    confirmed: 'bg-blue-100 text-blue-800',\n    'in-progress': 'bg-purple-100 text-purple-800',\n    completed: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    disputed: 'bg-orange-100 text-orange-800',\n    \n    // Payment statuses\n    paid: 'bg-green-100 text-green-800',\n    refunded: 'bg-gray-100 text-gray-800',\n    failed: 'bg-red-100 text-red-800',\n    \n    // Provider statuses\n    approved: 'bg-green-100 text-green-800',\n    rejected: 'bg-red-100 text-red-800',\n    \n    // General statuses\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    verified: 'bg-blue-100 text-blue-800',\n    unverified: 'bg-yellow-100 text-yellow-800',\n  };\n  \n  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '');\n  if (cleaned.length === 10) {\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n  }\n  return phone;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return current > 0 ? 100 : 0;\n  return ((current - previous) / previous) * 100;\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;QAAE,WAAW;IAAK;AACxD;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,mBAAmB;QACnB,SAAS;QACT,WAAW;QACX,eAAe;QACf,WAAW;QACX,WAAW;QACX,UAAU;QAEV,mBAAmB;QACnB,MAAM;QACN,UAAU;QACV,QAAQ;QAER,oBAAoB;QACpB,UAAU;QACV,UAAU;QAEV,mBAAmB;QACnB,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;IACd;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,oBAAoB,KAAa;IAC/C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,AAAC,IAA2B,OAAxB,QAAQ,KAAK,CAAC,GAAG,IAAG,MAA2B,OAAvB,QAAQ,KAAK,CAAC,GAAG,IAAG,KAAoB,OAAjB,QAAQ,KAAK,CAAC;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO,UAAU,IAAI,MAAM;IAC/C,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  HomeIcon,\n  UsersIcon,\n  UserGroupIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  BellIcon,\n} from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Users', href: '/dashboard/users', icon: UsersIcon },\n  { name: 'Providers', href: '/dashboard/providers', icon: UserGroupIcon },\n  { name: 'Bookings', href: '/dashboard/bookings', icon: CalendarDaysIcon },\n  { name: 'Payments', href: '/dashboard/payments', icon: CurrencyDollarIcon },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },\n  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },\n];\n\nexport default function DashboardLayout({ children, title }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { adminUser, signOut } = useAuth();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/login');\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-slate-50\">\n      {/* Mobile sidebar */}\n      <div className={cn(\n        \"fixed inset-0 z-50 lg:hidden\",\n        sidebarOpen ? \"block\" : \"hidden\"\n      )}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex items-center mb-3\">\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900\">{adminUser?.displayName}</p>\n                <p className=\"text-xs text-gray-500\">{adminUser?.role}</p>\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-2\" />\n              Sign out\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n              {title && (\n                <h1 className=\"ml-4 lg:ml-0 text-xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"text-gray-400 hover:text-gray-600\">\n                <BellIcon className=\"h-6 w-6\" />\n              </button>\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center lg:hidden\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAlBA;;;;;;AAyBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,oNAAA,CAAA,YAAS;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,4NAAA,CAAA,gBAAa;IAAC;IACvE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACxE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,sOAAA,CAAA,qBAAkB;IAAC;IAC1E;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,0NAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,gNAAA,CAAA,UAAO;IAAC;CAChE;AAEc,SAAS,gBAAgB,KAAyC;QAAzC,EAAE,QAAQ,EAAE,KAAK,EAAwB,GAAzC;QA+ErB,wBAyCA;;IAvHjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gCACA,cAAc,UAAU;;kCAExB,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAaxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAG/D,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCALL,KAAK,IAAI;;;;;;;;;;sCASpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,sBAAA,iCAAA,yBAAA,UAAW,WAAW,cAAtB,6CAAA,uBAAwB,MAAM,CAAC,OAAM;;;;;;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,sBAAA,gCAAA,UAAW,WAAW;;;;;;8DACxE,6LAAC;oDAAE,WAAU;8DAAyB,sBAAA,gCAAA,UAAW,IAAI;;;;;;;;;;;;;;;;;;8CAGzD,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,oPAAA,CAAA,4BAAyB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;wCAEtB,uBACC,6LAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,sBAAA,iCAAA,0BAAA,UAAW,WAAW,cAAtB,8CAAA,wBAAwB,MAAM,CAAC,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAxIwB;;QAES,kIAAA,CAAA,UAAO;QACvB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/app/dashboard/providers/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport {\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  EyeIcon,\n  CheckIcon,\n  XMarkIcon,\n  StarIcon,\n} from '@heroicons/react/24/outline';\nimport { formatDate, getStatusColor, getInitials } from '@/lib/utils';\nimport { Provider } from '@/types';\n\n// Mock data - replace with real data from Firebase\nconst mockProviders: Provider[] = [\n  {\n    id: '1',\n    userId: 'user1',\n    businessName: 'PetCare Plus',\n    description: 'Professional pet sitting and dog walking services',\n    services: [\n      { id: '1', name: 'Dog Walking', description: '30-60 minute walks', category: 'dog-walking', price: 25, duration: 30, isActive: true },\n      { id: '2', name: 'Pet Sitting', description: 'In-home pet care', category: 'pet-sitting', price: 40, duration: 60, isActive: true }\n    ],\n    location: {\n      street: '123 Main St',\n      city: 'San Francisco',\n      state: 'CA',\n      zipCode: '94102',\n      country: 'USA'\n    },\n    rating: 4.8,\n    reviewCount: 127,\n    isVerified: true,\n    isApproved: true,\n    documents: [\n      { id: '1', type: 'license', url: '/docs/license1.pdf', status: 'approved', uploadedAt: new Date('2024-01-10'), reviewedAt: new Date('2024-01-12') }\n    ],\n    availability: [],\n    createdAt: new Date('2024-01-10'),\n    updatedAt: new Date('2024-01-25')\n  },\n  {\n    id: '2',\n    userId: 'user2',\n    businessName: 'Happy Tails Pet Services',\n    description: 'Loving care for your furry friends',\n    services: [\n      { id: '3', name: 'Pet Grooming', description: 'Full grooming service', category: 'grooming', price: 60, duration: 120, isActive: true }\n    ],\n    location: {\n      street: '456 Oak Ave',\n      city: 'San Francisco',\n      state: 'CA',\n      zipCode: '94103',\n      country: 'USA'\n    },\n    rating: 4.5,\n    reviewCount: 89,\n    isVerified: false,\n    isApproved: false,\n    documents: [\n      { id: '2', type: 'license', url: '/docs/license2.pdf', status: 'pending', uploadedAt: new Date('2024-01-28') }\n    ],\n    availability: [],\n    createdAt: new Date('2024-01-28'),\n    updatedAt: new Date('2024-01-28')\n  },\n];\n\nexport default function ProvidersPage() {\n  const { user, adminUser, loading } = useAuth();\n  const router = useRouter();\n  const [providers, setProviders] = useState<Provider[]>(mockProviders);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<'all' | 'approved' | 'pending' | 'rejected'>('all');\n\n  useEffect(() => {\n    if (!loading && (!user || !adminUser)) {\n      router.push('/login');\n    }\n  }, [user, adminUser, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-slate-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!user || !adminUser) {\n    return null;\n  }\n\n  const filteredProviders = providers.filter(provider => {\n    const matchesSearch = provider.businessName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         provider.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || \n                         (statusFilter === 'approved' && provider.isApproved) ||\n                         (statusFilter === 'pending' && !provider.isApproved) ||\n                         (statusFilter === 'rejected' && !provider.isApproved);\n    return matchesSearch && matchesStatus;\n  });\n\n  const handleApprove = (providerId: string) => {\n    setProviders(prev => prev.map(provider => \n      provider.id === providerId \n        ? { ...provider, isApproved: true, isVerified: true }\n        : provider\n    ));\n  };\n\n  const handleReject = (providerId: string) => {\n    setProviders(prev => prev.map(provider => \n      provider.id === providerId \n        ? { ...provider, isApproved: false, isVerified: false }\n        : provider\n    ));\n  };\n\n  return (\n    <DashboardLayout title=\"Providers Management\">\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between mb-6\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Providers</h1>\n            <p className=\"mt-1 text-sm text-gray-600\">\n              Manage service providers and their applications\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button className=\"btn btn-primary\">\n              Export Providers\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6\">\n          <div className=\"card p-4\">\n            <div className=\"text-2xl font-bold text-gray-900\">\n              {providers.filter(p => p.isApproved).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">Approved Providers</div>\n          </div>\n          <div className=\"card p-4\">\n            <div className=\"text-2xl font-bold text-yellow-600\">\n              {providers.filter(p => !p.isApproved).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">Pending Approval</div>\n          </div>\n          <div className=\"card p-4\">\n            <div className=\"text-2xl font-bold text-green-600\">\n              {providers.filter(p => p.isVerified).length}\n            </div>\n            <div className=\"text-sm text-gray-600\">Verified Providers</div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"card p-4 mb-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search providers...\"\n                  className=\"input pl-10\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <select\n                className=\"input\"\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'approved' | 'pending' | 'rejected')}\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"approved\">Approved</option>\n                <option value=\"pending\">Pending</option>\n                <option value=\"rejected\">Rejected</option>\n              </select>\n              <button className=\"btn btn-secondary\">\n                <FunnelIcon className=\"h-4 w-4 mr-2\" />\n                More Filters\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Providers Table */}\n        <div className=\"card overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Provider\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Services\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Rating\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Applied\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredProviders.map((provider) => (\n                  <tr key={provider.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"h-10 w-10 flex-shrink-0\">\n                          <div className=\"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\">\n                            <span className=\"text-sm font-medium text-blue-700\">\n                              {getInitials(provider.businessName)}\n                            </span>\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {provider.businessName}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            {provider.location.city}, {provider.location.state}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">\n                        {provider.services.length} services\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {provider.services.slice(0, 2).map(s => s.name).join(', ')}\n                        {provider.services.length > 2 && '...'}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <StarIcon className=\"h-4 w-4 text-yellow-400 mr-1\" />\n                        <span className=\"text-sm text-gray-900\">{provider.rating}</span>\n                        <span className=\"text-sm text-gray-500 ml-1\">({provider.reviewCount})</span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex flex-col space-y-1\">\n                        <span className={`badge ${getStatusColor(provider.isApproved ? 'approved' : 'pending')}`}>\n                          {provider.isApproved ? 'Approved' : 'Pending'}\n                        </span>\n                        {provider.isVerified && (\n                          <span className=\"badge bg-blue-100 text-blue-800\">\n                            Verified\n                          </span>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {formatDate(provider.createdAt)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                      <div className=\"flex justify-end space-x-2\">\n                        <button className=\"text-blue-600 hover:text-blue-900\">\n                          <EyeIcon className=\"h-4 w-4\" />\n                        </button>\n                        {!provider.isApproved && (\n                          <>\n                            <button \n                              onClick={() => handleApprove(provider.id)}\n                              className=\"text-green-600 hover:text-green-900\"\n                            >\n                              <CheckIcon className=\"h-4 w-4\" />\n                            </button>\n                            <button \n                              onClick={() => handleReject(provider.id)}\n                              className=\"text-red-600 hover:text-red-900\"\n                            >\n                              <XMarkIcon className=\"h-4 w-4\" />\n                            </button>\n                          </>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n          \n          {filteredProviders.length === 0 && (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-500\">No providers found matching your criteria.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAdA;;;;;;;AAiBA,mDAAmD;AACnD,MAAM,gBAA4B;IAChC;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;QACd,aAAa;QACb,UAAU;YACR;gBAAE,IAAI;gBAAK,MAAM;gBAAe,aAAa;gBAAsB,UAAU;gBAAe,OAAO;gBAAI,UAAU;gBAAI,UAAU;YAAK;YACpI;gBAAE,IAAI;gBAAK,MAAM;gBAAe,aAAa;gBAAoB,UAAU;gBAAe,OAAO;gBAAI,UAAU;gBAAI,UAAU;YAAK;SACnI;QACD,UAAU;YACR,QAAQ;YACR,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;QACX;QACA,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,WAAW;YACT;gBAAE,IAAI;gBAAK,MAAM;gBAAW,KAAK;gBAAsB,QAAQ;gBAAY,YAAY,IAAI,KAAK;gBAAe,YAAY,IAAI,KAAK;YAAc;SACnJ;QACD,cAAc,EAAE;QAChB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;QACd,aAAa;QACb,UAAU;YACR;gBAAE,IAAI;gBAAK,MAAM;gBAAgB,aAAa;gBAAyB,UAAU;gBAAY,OAAO;gBAAI,UAAU;gBAAK,UAAU;YAAK;SACvI;QACD,UAAU;YACR,QAAQ;YACR,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;QACX;QACA,QAAQ;QACR,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,WAAW;YACT;gBAAE,IAAI;gBAAK,MAAM;gBAAW,KAAK;gBAAsB,QAAQ;gBAAW,YAAY,IAAI,KAAK;YAAc;SAC9G;QACD,cAAc,EAAE;QAChB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;IAE9F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,GAAG;gBACrC,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAW;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,QAAQ,CAAC,WAAW;QACvB,OAAO;IACT;IAEA,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,SAAS,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpE,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACvF,MAAM,gBAAgB,iBAAiB,SACjB,iBAAiB,cAAc,SAAS,UAAU,IAClD,iBAAiB,aAAa,CAAC,SAAS,UAAU,IAClD,iBAAiB,cAAc,CAAC,SAAS,UAAU;QACzE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,gBAAgB,CAAC;QACrB,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,WAC5B,SAAS,EAAE,KAAK,aACZ;oBAAE,GAAG,QAAQ;oBAAE,YAAY;oBAAM,YAAY;gBAAK,IAClD;IAER;IAEA,MAAM,eAAe,CAAC;QACpB,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,WAC5B,SAAS,EAAE,KAAK,aACZ;oBAAE,GAAG,QAAQ;oBAAE,YAAY;oBAAO,YAAY;gBAAM,IACpD;IAER;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;QAAC,OAAM;kBACrB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,WAAU;0CAAkB;;;;;;;;;;;;;;;;;8BAOxC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;8CAE7C,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,UAAU,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;8CAE7C,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAK3C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;sDAC/B,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;0CAInD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;0DAE/C,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;kDAE3B,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC,sNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;;;;;;;kDAKpG,6LAAC;wCAAM,WAAU;kDACd,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;gDAAqB,WAAU;;kEAC9B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,SAAS,YAAY;;;;;;;;;;;;;;;;8EAIxC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,SAAS,YAAY;;;;;;sFAExB,6LAAC;4EAAI,WAAU;;gFACZ,SAAS,QAAQ,CAAC,IAAI;gFAAC;gFAAG,SAAS,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kEAK1D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;oEACZ,SAAS,QAAQ,CAAC,MAAM;oEAAC;;;;;;;0EAE5B,6LAAC;gEAAI,WAAU;;oEACZ,SAAS,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;oEACpD,SAAS,QAAQ,CAAC,MAAM,GAAG,KAAK;;;;;;;;;;;;;kEAGrC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAK,WAAU;8EAAyB,SAAS,MAAM;;;;;;8EACxD,6LAAC;oEAAK,WAAU;;wEAA6B;wEAAE,SAAS,WAAW;wEAAC;;;;;;;;;;;;;;;;;;kEAGxE,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAW,AAAC,SAAqE,OAA7D,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,UAAU,GAAG,aAAa;8EACzE,SAAS,UAAU,GAAG,aAAa;;;;;;gEAErC,SAAS,UAAU,kBAClB,6LAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;;;;;;kEAMxD,6LAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;;;;;;kEAEhC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,gNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;gEAEpB,CAAC,SAAS,UAAU,kBACnB;;sFACE,6LAAC;4EACC,SAAS,IAAM,cAAc,SAAS,EAAE;4EACxC,WAAU;sFAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;;;;;;sFAEvB,6LAAC;4EACC,SAAS,IAAM,aAAa,SAAS,EAAE;4EACvC,WAAU;sFAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;+CApExB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;wBAgF3B,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GAlPwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}