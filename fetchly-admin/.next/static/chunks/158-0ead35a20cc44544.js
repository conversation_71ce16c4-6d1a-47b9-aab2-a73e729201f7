(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[158],{52:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>u,Qd:()=>l,Tw:()=>f,Zz:()=>s,ve:()=>d,y$:()=>c});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function c(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(c)(e,t)}let a=e,u=t,s=new Map,f=s,d=0,p=!1;function h(){f===s&&(f=new Map,s.forEach((e,t)=>{f.set(t,e)}))}function y(){if(p)throw Error(n(3));return u}function v(e){if("function"!=typeof e)throw Error(n(4));if(p)throw Error(n(5));let t=!0;h();let r=d++;return f.set(r,e),function(){if(t){if(p)throw Error(n(6));t=!1,h(),f.delete(r),s=null}}}function g(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(p)throw Error(n(9));try{p=!0,u=a(u,e)}finally{p=!1}return(s=f).forEach(e=>{e()}),e}return g({type:o.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));a=e,g({type:o.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(y())}return t(),{unsubscribe:v(t)}},[i](){return this}}}}}function u(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let a=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let o=!1,l={};for(let t=0;t<a.length;t++){let c=a[t],u=i[c],s=e[c],f=u(s,r);if(void 0===f)throw r&&r.type,Error(n(14));l[c]=f,o=o||f!==s}return(o=o||a.length!==Object.keys(e).length)?l:e}}function s(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let a=t(r,i),o=()=>{throw Error(n(15))},l={getState:a.getState,dispatch:(e,...t)=>o(e,...t)};return o=s(...e.map(e=>e(l)))(a.dispatch),{...a,dispatch:o}}}function d(e){return l(e)&&"type"in e&&"string"==typeof e.type}},170:(e,t,r)=>{"use strict";r.d(t,{r:()=>w});var n=r(2115),i=r(6641),a=r(9972),o=r(9068),l=r(5932),c=r(3433),u=r(1971),s=r(2267);function f(e){var t=(0,u.j)();return(0,n.useEffect)(()=>{t((0,s.U)(e))},[t,e]),null}var d=r(3725),p=r(3389),h=r(8892),y=["width","height","layout"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},m=(0,n.forwardRef)(function(e,t){var r,i=(0,p.e)(e.categoricalChartProps,g),{width:u,height:s,layout:m}=i,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,y);if(!(0,h.F)(u)||!(0,h.F)(s))return null;var{chartName:x,defaultTooltipEventType:w,validateTooltipEventTypes:O,tooltipPayloadSearcher:j}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:x,defaultTooltipEventType:w,validateTooltipEventTypes:O,tooltipPayloadSearcher:j,eventEmitter:void 0}},reduxStoreName:null!=(r=i.id)?r:x},n.createElement(o.TK,{chartData:i.data}),n.createElement(l.s,{width:u,height:s,layout:m,margin:i.margin}),n.createElement(c.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(f,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(d.L,v({width:u,height:s},b,{ref:t})))}),b=["item"],x={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},w=(0,n.forwardRef)((e,t)=>{var r=(0,p.e)(e,x);return n.createElement(m,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:i.uN,categoricalChartProps:r,ref:t})})},177:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5160);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},215:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>ea,eE:()=>eu,Xb:()=>eo,JG:()=>ed,A2:()=>ei,yn:()=>es,Dn:()=>P,gL:()=>q,fl:()=>Y,R4:()=>Q,Re:()=>O,n4:()=>_});var n=r(8924),i=r(4299),a=r(7238),o=r(9827),l=r(356),c=r(8478),u=r(6377),s=r(8190),f=r(6523),d=r(530),p=r(1928),h=r(841),y=r(4968),v=r(2589),g=r(8573),m=r(5146),b=r(6670),x=r(5714),w=r(4013),O=e=>{var t=(0,a.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},j=e=>e.tooltip.settings.axisId,P=e=>{var t=O(e),r=j(e);return(0,i.Hd)(e,t,r)},E=(0,n.Mz)([P,a.fz,i.um,c.iO,O],i.sr),A=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),M=(0,n.Mz)([O,j],i.eo),S=(0,n.Mz)([A,P,M],i.ec),k=(0,n.Mz)([S],i.rj),_=(0,n.Mz)([k,l.LF],i.Nk),T=(0,n.Mz)([_,P,S],i.fb),C=(0,n.Mz)([P],i.S5),D=(0,n.Mz)([_,S,c.eC],i.MK),N=(0,n.Mz)([D,l.LF,O],i.pM),I=(0,n.Mz)([S],i.IO),z=(0,n.Mz)([_,P,I,O],i.kz),L=(0,n.Mz)([i.Kr,O,j],i.P9),R=(0,n.Mz)([L,O],i.Oz),$=(0,n.Mz)([i.gT,O,j],i.P9),B=(0,n.Mz)([$,O],i.q),U=(0,n.Mz)([i.$X,O,j],i.P9),F=(0,n.Mz)([U,O],i.bb),K=(0,n.Mz)([R,F,B],i.yi),H=(0,n.Mz)([P,C,N,z,K],i.wL),G=(0,n.Mz)([P,a.fz,_,T,c.eC,O,H],i.tP),Z=(0,n.Mz)([G,P,E],i.xp),W=(0,n.Mz)([P,G,Z,O],i.g1),V=e=>{var t=O(e),r=j(e);return(0,i.D5)(e,t,r,!1)},q=(0,n.Mz)([P,V],s.I),Y=(0,n.Mz)([P,E,W,q],i.Qn),J=(0,n.Mz)([a.fz,T,P,O],i.tF),X=(0,n.Mz)([a.fz,T,P,O],i.iv),Q=(0,n.Mz)([a.fz,P,E,Y,V,J,X,O],(e,t,r,n,i,a,l,c)=>{if(t){var{type:s}=t,f=(0,o._L)(e,c);if(n){var d="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/d:0;return(p="angleAxis"===c&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,u.sA)(i[0]-i[1])*p:p,f&&l)?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:a?a[e]:e,index:t,offset:p}))}}}),ee=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),et=e=>e.tooltip.settings.trigger,er=e=>e.tooltip.settings.defaultIndex,en=(0,n.Mz)([x.J,ee,et,er],p.i),ei=(0,n.Mz)([en,_],h.P),ea=(0,n.Mz)([Q,ei],d.E),eo=(0,n.Mz)([en],e=>{if(e)return e.dataKey}),el=(0,n.Mz)([x.J,ee,et,er],m.q),ec=(0,n.Mz)([v.Lp,v.A$,a.fz,g.HZ,Q,er,el,b.x],y.o),eu=(0,n.Mz)([en,ec],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),es=(0,n.Mz)([en],e=>e.active),ef=(0,n.Mz)([el,ei,l.LF,P,ea,b.x,ee],w.N),ed=(0,n.Mz)([ef],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},220:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},241:(e,t,r)=>{e.exports=r(2434).sortBy},294:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,h=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case f:case a:case l:case o:case p:return e;default:switch(e=e&&e.$$typeof){case u:case d:case y:case h:case c:return e;default:return t}}case i:return t}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");t.isFragment=function(e){return v(e)===a}},330:(e,t,r)=>{"use strict";e.exports=r(294)},356:(e,t,r)=>{"use strict";r.d(t,{HS:()=>o,LF:()=>i,z3:()=>a});var n=r(8924),i=e=>e.chartData,a=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},379:(e,t,r)=>{"use strict";r.d(t,{J:()=>g,Z:()=>v});var n=r(2115),i=r(2596),a=r(9095),o=r(788),l=r(6377),c=r(5641),u=r(7238),s=["offset"],f=["labelRef"];function d(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v=e=>null!=e&&"function"==typeof e;function g(e){var t,{offset:r=5}=e,p=h({offset:r},d(e,s)),{viewBox:v,position:g,value:m,children:b,content:x,className:w="",textBreakAll:O,labelRef:j}=p,P=(0,u.sk)(),E=v||P;if(!E||(0,l.uy)(m)&&(0,l.uy)(b)&&!(0,n.isValidElement)(x)&&"function"!=typeof x)return null;if((0,n.isValidElement)(x)){var{labelRef:A}=p,M=d(p,f);return(0,n.cloneElement)(x,M)}if("function"==typeof x){if(t=(0,n.createElement)(x,p),(0,n.isValidElement)(t))return t}else t=(e=>{var{value:t,formatter:r}=e,n=(0,l.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n})(p);var S="cx"in E&&(0,l.Et)(E.cx),k=(0,o.J9)(p,!0);if(S&&("insideStart"===g||"insideEnd"===g||"end"===g))return((e,t,r)=>{let a,o;var u,s,{position:f,viewBox:d,offset:p,className:h}=e,{cx:v,cy:g,innerRadius:m,outerRadius:b,startAngle:x,endAngle:w,clockWise:O}=d,j=(m+b)/2,P=(a=x,o=w,(0,l.sA)(o-a)*Math.min(Math.abs(o-a),360)),E=P>=0?1:-1;"insideStart"===f?(u=x+E*p,s=O):"insideEnd"===f?(u=w-E*p,s=!O):"end"===f&&(u=w+E*p,s=O),s=P<=0?s:!s;var A=(0,c.IZ)(v,g,j,u),M=(0,c.IZ)(v,g,j,u+(s?1:-1)*359),S="M".concat(A.x,",").concat(A.y,"\n    A").concat(j,",").concat(j,",0,1,").concat(+!s,",\n    ").concat(M.x,",").concat(M.y),k=(0,l.uy)(e.id)?(0,l.NF)("recharts-radial-line-"):e.id;return n.createElement("text",y({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",h)}),n.createElement("defs",null,n.createElement("path",{id:k,d:S})),n.createElement("textPath",{xlinkHref:"#".concat(k)},t))})(p,t,k);var _=S?(e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:u,endAngle:s}=t,f=(u+s)/2;if("outside"===n){var{x:d,y:p}=(0,c.IZ)(i,a,l+r,f);return{x:d,y:p,textAnchor:d>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:h,y}=(0,c.IZ)(i,a,(o+l)/2,f);return{x:h,y,textAnchor:"middle",verticalAnchor:"middle"}})(p):((e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:c,height:u}=t,s=u>=0?1:-1,f=s*n,d=s>0?"end":"start",p=s>0?"start":"end",y=c>=0?1:-1,v=y*n,g=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return h(h({},{x:a+c/2,y:o-s*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(o-r.y,0),width:c}:{});if("bottom"===i)return h(h({},{x:a+c/2,y:o+u+f,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(o+u),0),width:c}:{});if("left"===i){var b={x:a-v,y:o+u/2,textAnchor:g,verticalAnchor:"middle"};return h(h({},b),r?{width:Math.max(b.x-r.x,0),height:u}:{})}if("right"===i){var x={x:a+c+v,y:o+u/2,textAnchor:m,verticalAnchor:"middle"};return h(h({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:u}:{})}var w=r?{width:c,height:u}:{};return"insideLeft"===i?h({x:a+v,y:o+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===i?h({x:a+c-v,y:o+u/2,textAnchor:g,verticalAnchor:"middle"},w):"insideTop"===i?h({x:a+c/2,y:o+f,textAnchor:"middle",verticalAnchor:p},w):"insideBottom"===i?h({x:a+c/2,y:o+u-f,textAnchor:"middle",verticalAnchor:d},w):"insideTopLeft"===i?h({x:a+v,y:o+f,textAnchor:m,verticalAnchor:p},w):"insideTopRight"===i?h({x:a+c-v,y:o+f,textAnchor:g,verticalAnchor:p},w):"insideBottomLeft"===i?h({x:a+v,y:o+u-f,textAnchor:m,verticalAnchor:d},w):"insideBottomRight"===i?h({x:a+c-v,y:o+u-f,textAnchor:g,verticalAnchor:d},w):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?h({x:a+(0,l.F4)(i.x,c),y:o+(0,l.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},w):h({x:a+c/2,y:o+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)})(p,E);return n.createElement(a.E,y({ref:j,className:(0,i.$)("recharts-label",w)},k,_,{breakAll:O}),t)}g.displayName="Label";var m=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:c,innerRadius:u,outerRadius:s,x:f,y:d,top:p,left:h,width:y,height:v,clockWise:g,labelViewBox:m}=e;if(m)return m;if((0,l.Et)(y)&&(0,l.Et)(v)){if((0,l.Et)(f)&&(0,l.Et)(d))return{x:f,y:d,width:y,height:v};if((0,l.Et)(p)&&(0,l.Et)(h))return{x:p,y:h,width:y,height:v}}return(0,l.Et)(f)&&(0,l.Et)(d)?{x:f,y:d,width:0,height:0}:(0,l.Et)(t)&&(0,l.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:u||0,outerRadius:s||c||o||0,clockWise:g}:e.viewBox?e.viewBox:void 0};g.parseViewBox=m,g.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,c=m(e),u=(0,o.aS)(i,g).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||c,key:"label-".concat(r)}));return r?[((e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(g,y({key:"label-implicit"},i)):(0,l.vh)(e)?n.createElement(g,y({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===g?(0,n.cloneElement)(e,h({key:"label-implicit"},i)):n.createElement(g,y({key:"label-implicit",content:e},i)):v(e)?n.createElement(g,y({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(g,y({},e,{key:"label-implicit"},i)):null})(e.label,t||c,a),...u]:u}},400:(e,t,r)=>{e.exports=r(2962).throttle},429:(e,t,r)=>{"use strict";r.d(t,{mZ:()=>l,vE:()=>o});var n=r(5710),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},a=(0,n.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),o=a.reducer,{updateOptions:l}=a.actions},512:(e,t,r)=>{e.exports=r(7547).uniqBy},530:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(6377),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},564:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i,a=null,o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),c=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)},u=null,s=()=>{null!=u&&clearTimeout(u),u=setTimeout(()=>{u=null,l&&c(),f()},t)},f=()=>{null!==u&&(clearTimeout(u),u=null),i=void 0,a=null},d=function(...e){if(r?.aborted)return;i=this,a=e;let t=null==u;s(),o&&t&&c()};return d.schedule=s,d.cancel=f,d.flush=()=>{c()},r?.addEventListener("abort",f,{once:!0}),d}},656:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8179),i=r(9279);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},668:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},675:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},688:(e,t,r)=>{"use strict";r.d(t,{I:()=>U});var n=r(2115);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function l(e){this._context=e}function c(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class u{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function d(e){return new f(e)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function p(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function h(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function v(e){this._context=e}function g(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function x(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function w(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,h(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,h(this,r=p(this,e,t)),r);break;default:y(this,this._t0,r=p(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(e,t){v.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=x(e),i=x(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(9819),j=r(5654),P=r(1847);function E(e){return e[0]}function A(e){return e[1]}function M(e,t){var r=(0,j.A)(!0),n=null,i=d,a=null,o=(0,P.i)(l);function l(l){var c,u,s,f=(l=(0,O.A)(l)).length,d=!1;for(null==n&&(a=i(s=o())),c=0;c<=f;++c)!(c<f&&r(u=l[c],c,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(u,c,l),+t(u,c,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?E:(0,j.A)(e),t="function"==typeof t?t:void 0===t?A:(0,j.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,j.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function S(e,t,r){var n=null,i=(0,j.A)(!0),a=null,o=d,l=null,c=(0,P.i)(u);function u(u){var s,f,d,p,h,y=(u=(0,O.A)(u)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(h=c())),s=0;s<=y;++s){if(!(s<y&&i(p=u[s],s,u))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=s-1;d>=f;--d)l.point(g[d],m[d]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+e(p,s,u),m[s]=+t(p,s,u),l.point(n?+n(p,s,u):g[s],r?+r(p,s,u):m[s]))}if(h)return l=null,h+""||null}function s(){return M().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?E:(0,j.A)(+e),t="function"==typeof t?t:void 0===t?(0,j.A)(0):(0,j.A)(+t),r="function"==typeof r?r:void 0===r?A:(0,j.A)(+r),u.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),n=null,u):e},u.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),u):e},u.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,j.A)(+e),u):n},u.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),r=null,u):t},u.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),u):t},u.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,j.A)(+e),u):r},u.lineX0=u.lineY0=function(){return s().x(e).y(t)},u.lineY1=function(){return s().x(e).y(r)},u.lineX1=function(){return s().x(n).y(t)},u.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,j.A)(!!e),u):i},u.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),u):o},u.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),u):a},u}var k=r(2596),_=r(3597),T=r(788),C=r(6377),D=r(8892);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new c(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new u(e,!0)},curveBumpY:function(e){return new u(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:d,curveMonotoneX:function(e){return new v(e)},curveMonotoneY:function(e){return new g(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new w(e,.5)},curveStepAfter:function(e){return new w(e,1)},curveStepBefore:function(e){return new w(e,0)}},R=e=>(0,D.H)(e.x)&&(0,D.H)(e.y),$=e=>e.x,B=e=>e.y,U=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?(e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,C.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?L["".concat(r).concat("vertical"===t?"Y":"X")]:L[r]||d})(r,a),c=o?n.filter(R):n;if(Array.isArray(i)){var u=o?i.filter(e=>R(e)):i,s=c.map((e,t)=>z(z({},e),{},{base:u[t]}));return(t="vertical"===a?S().y(B).x1($).x0(e=>e.base.x):S().x($).y1(B).y0(e=>e.base.y)).defined(R).curve(l),t(s)}return(t="vertical"===a&&(0,C.Et)(i)?S().y(B).x1($).x0(i):(0,C.Et)(i)?S().x($).y1(B).y0(i):M().x($).y(B)).defined(R).curve(l),t(c)})(e):i;return n.createElement("path",N({},(0,T.J9)(e,!1),(0,_._U)(e),{className:(0,k.$)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},788:(e,t,r)=>{"use strict";r.d(t,{J9:()=>y,aS:()=>p,y$:()=>h});var n=r(5672),i=r.n(n),a=r(2115),o=r(330),l=r(6377),c=r(3597),u=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",s=null,f=null,d=e=>{if(e===s&&Array.isArray(f))return f;var t=[];return a.Children.forEach(e,e=>{(0,l.uy)(e)||((0,o.isFragment)(e)?t=t.concat(d(e.props.children)):t.push(e))}),f=t,s=e,t};function p(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>u(e)):[u(t)],d(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var h=e=>!e||"object"!=typeof e||!("clipDot"in e)||!!e.clipDot,y=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;((e,t,r,n)=>{var i,a=null!=(i=n&&(null===c.VU||void 0===c.VU?void 0:c.VU[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||c.QQ.includes(t))||r&&c.j2.includes(t)})(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},841:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(8892),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},885:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},921:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7040),i=r(4545),a=r(6200),o=r(4072);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var c=t,u=r,s=l;if(0===u.length)return s;let e=c;for(let t=0;t<u.length;t++){if(null==e||n.isUnsafeProperty(u[t]))return s;e=e[u[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},931:(e,t,r)=>{e.exports=r(6006).isPlainObject},972:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>c});var n=r(8924),i=r(8573),a=r(2589),o=r(6377),l=e=>e.brush,c=(0,n.Mz)([l,i.HZ,a.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},1147:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},1165:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},1551:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(668),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},1571:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2465),i=r(2194),a=r(4804),o=r(4517);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},1643:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)}},1807:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(2115),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},1847:(e,t,r)=>{"use strict";r.d(t,{i:()=>c});let n=Math.PI,i=2*n,a=i-1e-6;function o(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?o:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return o;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,a){if(e*=1,t*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,l=this._y1,c=r-e,u=i-t,s=o-e,f=l-t,d=s*s+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(d>1e-6)if(Math.abs(f*c-u*s)>1e-6&&a){let p=r-o,h=i-l,y=c*c+u*u,v=Math.sqrt(y),g=Math.sqrt(d),m=a*Math.tan((n-Math.acos((y+d-(p*p+h*h))/(2*v*g)))/2),b=m/g,x=m/v;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*f}`,this._append`A${a},${a},0,0,${+(f*p>s*h)},${this._x1=e+x*c},${this._y1=t+x*u}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,o,l,c){if(e*=1,t*=1,r*=1,c=!!c,r<0)throw Error(`negative radius: ${r}`);let u=r*Math.cos(o),s=r*Math.sin(o),f=e+u,d=t+s,p=1^c,h=c?o-l:l-o;null===this._x1?this._append`M${f},${d}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-d)>1e-6)&&this._append`L${f},${d}`,r&&(h<0&&(h=h%i+i),h>a?this._append`A${r},${r},0,1,${p},${e-u},${t-s}A${r},${r},0,1,${p},${this._x1=f},${this._y1=d}`:h>1e-6&&this._append`A${r},${r},0,${+(h>=n)},${p},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function c(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},1928:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(4890);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return n.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return n.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},1971:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(5643),i=r(2115),a=r(5064),o=e=>e,l=()=>{var e=(0,i.useContext)(a.E);return e?e.store.dispatch:o},c=()=>{},u=()=>c,s=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:u,t?t.store.getState:c,t?t.store.getState:c,t?e:c,s)}},1992:(e,t,r)=>{"use strict";r(4993)},2071:(e,t,r)=>{"use strict";r.d(t,{h:()=>b});var n=r(2115),i=r(2596),a=r(9584),o=r(5306),l=r(1971),c=r(4299),u=r(8573),s=r(1807),f=r(379),d=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e){var t=(0,l.j)();return(0,n.useEffect)(()=>(t((0,o.cU)(e)),()=>{t((0,o.fR)(e))}),[e,t]),null}var v=e=>{var t,{yAxisId:r,className:p,width:y,label:v}=e,g=(0,n.useRef)(null),m=(0,n.useRef)(null),b=(0,l.G)(u.c2),x=(0,s.r)(),w=(0,l.j)(),O="yAxis",j=(0,l.G)(e=>(0,c.iV)(e,O,r,x)),P=(0,l.G)(e=>(0,c.wP)(e,r)),E=(0,l.G)(e=>(0,c.KR)(e,r)),A=(0,l.G)(e=>(0,c.Zi)(e,O,r,x));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==y||!P||(0,f.Z)(v)||(0,n.isValidElement)(v))){var e,t=g.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:a,tickMargin:l}=t.props,c=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0})({ticks:i,label:m.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(P.width)!==Math.round(c)&&w((0,o.QG)({id:r,width:c}))}},[g,null==g||null==(t=g.current)||null==(t=t.tickRefs)?void 0:t.current,null==P?void 0:P.width,P,w,v,r,y]),null==P||null==E)return null;var{dangerouslySetInnerHTML:M,ticks:S}=e,k=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,d);return n.createElement(a.u,h({},k,{ref:g,labelRef:m,scale:j,x:E.x,y:E.y,width:P.width,height:P.height,className:(0,i.$)("recharts-".concat(O," ").concat(O),p),viewBox:b,ticks:A}))},g=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(y,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(v,e))},m={allowDataOverflow:c.cd.allowDataOverflow,allowDecimals:c.cd.allowDecimals,allowDuplicatedCategory:c.cd.allowDuplicatedCategory,hide:!1,mirror:c.cd.mirror,orientation:c.cd.orientation,padding:c.cd.padding,reversed:c.cd.reversed,scale:c.cd.scale,tickCount:c.cd.tickCount,type:c.cd.type,width:c.cd.width,yAxisId:0};class b extends n.Component{render(){return n.createElement(g,this.props)}}p(b,"displayName","YAxis"),p(b,"defaultProps",m)},2188:(e,t,r)=>{e.exports=r(5252).isEqual},2194:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(921);t.property=function(e){return function(t){return n.get(t,e)}}},2248:(e,t,r)=>{"use strict";r.d(t,{As:()=>f,Ch:()=>l,TK:()=>d,Vi:()=>s,ZF:()=>u,g5:()=>c,iZ:()=>p,lm:()=>o});var n=r(5710),i=r(4532),a=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,a=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(r));a>-1&&(e.cartesianItems[a]=(0,i.h4)(n))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:o,removeBar:l,addCartesianGraphicalItem:c,replaceCartesianGraphicalItem:u,removeCartesianGraphicalItem:s,addPolarGraphicalItem:f,removePolarGraphicalItem:d}=a.actions,p=a.reducer},2267:(e,t,r)=>{"use strict";r.d(t,{J:()=>a,U:()=>i});var n=(0,r(5710).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=n.actions,a=n.reducer},2287:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(e,t,r)=>r},2348:(e,t,r)=>{"use strict";r.d(t,{W:()=>c});var n=r(2115),i=r(2596),a=r(788),o=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=n.forwardRef((e,t)=>{var{children:r,className:c}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),s=(0,i.$)("recharts-layer",c);return n.createElement("g",l({className:s},(0,a.J9)(u,!0),{ref:t}),r)})},2366:(e,t,r)=>{"use strict";r.d(t,{N:()=>ea,l:()=>ei});var n=r(2115),i=r(2596),a=r(688),o=r(3597),l=r(788);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=e=>{var{cx:t,cy:r,r:a,className:u}=e,s=(0,i.$)("recharts-dot",u);return t===+t&&r===+r&&a===+a?n.createElement("circle",c({},(0,l.J9)(e,!1),(0,o._U)(e),{className:s,cx:t,cy:r,r:a})):null},s=r(2348),f=r(8460),d=r(4011),p=r(6377),h=r(1643),y=r(9827),v=r(1971),g=r(215),m=r(8234);function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(e){var{points:t,mainColor:r,activeDot:i,itemDataKey:a}=e,c=(0,v.G)(g.A2),f=(0,m.EI)();if(null==t||null==f)return null;var d=t.find(e=>f.includes(e.payload));return(0,p.uy)(d)?null:(e=>{var t,{point:r,childIndex:i,mainColor:a,activeDot:c,dataKey:f}=e;if(!1===c||null==r.x||null==r.y)return null;var d=x(x({index:i,dataKey:f,cx:r.x,cy:r.y,r:4,fill:null!=a?a:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,l.J9)(c,!1)),(0,o._U)(c));return t=(0,n.isValidElement)(c)?(0,n.cloneElement)(c,d):"function"==typeof c?c(d):n.createElement(u,d),n.createElement(s.W,{className:"recharts-active-dot"},t)})({point:d,childIndex:Number(c),mainColor:r,dataKey:a,activeDot:i})}var O=r(6091),j=r(9226),P=r(7195),E=r(7238),A=r(1807),M=r(8924),S=r(356),k=r(4299),_=(e,t,r,n)=>(0,k.Gx)(e,"xAxis",t,n),T=(e,t,r,n)=>(0,k.CR)(e,"xAxis",t,n),C=(e,t,r,n)=>(0,k.Gx)(e,"yAxis",r,n),D=(e,t,r,n)=>(0,k.CR)(e,"yAxis",r,n),N=(0,M.Mz)([E.fz,_,C,T,D],(e,t,r,n,i)=>(0,y._L)(e,"xAxis")?(0,y.Hj)(t,n,!1):(0,y.Hj)(r,i,!1)),I=(0,M.Mz)([k.ld,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),z=(0,M.Mz)([E.fz,_,C,T,D,I,N,S.HS],(e,t,r,n,i,a,o,l)=>{var c,{chartData:u,dataStartIndex:s,dataEndIndex:f}=l;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var{dataKey:d,data:p}=a;if(null!=(c=null!=p&&p.length>0?p:null==u?void 0:u.slice(s,f+1)))return ei({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:d,bandSize:o,displayedData:c})}}),L=r(9020),R=r(9426),$=r(3389),B=r(4460),U=["type","layout","connectNulls","needClip"],F=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function K(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function G(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach(function(t){Z(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Z(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function W(){return(W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function V(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:(0,y.uM)(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:c}}}var q=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function Y(e){var{clipPathId:t,points:r,props:a}=e,{dot:o,dataKey:c,needClip:f}=a;if(null==r||!o&&1!==r.length)return null;var d=(0,l.y$)(o),p=(0,l.J9)(a,!1),h=(0,l.J9)(o,!0),y=r.map((e,t)=>{var a,l=G(G(G({key:"dot-".concat(t),r:3},p),h),{},{index:t,cx:e.x,cy:e.y,dataKey:c,value:e.value,payload:e.payload,points:r});if(n.isValidElement(o))a=n.cloneElement(o,l);else if("function"==typeof o)a=o(l);else{var s=(0,i.$)("recharts-line-dot","boolean"!=typeof o?o.className:"");a=n.createElement(u,W({},l,{className:s}))}return a}),v={clipPath:f?"url(#clipPath-".concat(d?"":"dots-").concat(t,")"):null};return n.createElement(s.W,W({className:"recharts-line-dots",key:"dots"},v),y)}function J(e){var{clipPathId:t,pathRef:r,points:i,strokeDasharray:o,props:c,showLabels:u}=e,{type:s,layout:d,connectNulls:p,needClip:h}=c,y=K(c,U),v=G(G({},(0,l.J9)(y,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:h?"url(#clipPath-".concat(t,")"):null,points:i,type:s,layout:d,connectNulls:p,strokeDasharray:null!=o?o:c.strokeDasharray});return n.createElement(n.Fragment,null,(null==i?void 0:i.length)>1&&n.createElement(a.I,W({},v,{pathRef:r})),n.createElement(Y,{points:i,clipPathId:t,props:c}),u&&f.Z.renderCallByParent(c,i))}function X(e){var{clipPathId:t,props:r,pathRef:i,previousPointsRef:a,longestAnimatedLengthRef:o}=e,{points:l,strokeDasharray:c,isAnimationActive:u,animationBegin:s,animationDuration:f,animationEasing:d,animateNewValues:h,width:y,height:v,onAnimationEnd:g,onAnimationStart:m}=r,b=a.current,x=(0,R.n)(r,"recharts-line-"),[w,O]=(0,n.useState)(!1),j=(0,n.useCallback)(()=>{"function"==typeof g&&g(),O(!1)},[g]),P=(0,n.useCallback)(()=>{"function"==typeof m&&m(),O(!0)},[m]),E=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(i.current),A=o.current;return n.createElement(B.i,{begin:s,duration:f,isActive:u,easing:d,from:{t:0},to:{t:1},onAnimationEnd:j,onAnimationStart:P,key:x},e=>{var u,{t:s}=e,f=Math.min((0,p.Dj)(A,E+A)(s),E);if(u=c?((e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return q(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,l=[],c=0,u=0;c<r.length;u+=r[c],++c)if(u+r[c]>a){l=[...r.slice(0,c),a-u];break}var s=l.length%2==0?[0,o]:[o];return[...function(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}(r,i),...l,...s].map(e=>"".concat(e,"px")).join(", ")})(f,E,"".concat(c).split(/[,\s]+/gim).map(e=>parseFloat(e))):q(E,f),b){var d=b.length/l.length,g=1===s?l:l.map((e,t)=>{var r=Math.floor(t*d);if(b[r]){var n=b[r],i=(0,p.Dj)(n.x,e.x),a=(0,p.Dj)(n.y,e.y);return G(G({},e),{},{x:i(s),y:a(s)})}if(h){var o=(0,p.Dj)(2*y,e.x),l=(0,p.Dj)(v/2,e.y);return G(G({},e),{},{x:o(s),y:l(s)})}return G(G({},e),{},{x:e.x,y:e.y})});return a.current=g,n.createElement(J,{props:r,points:g,clipPathId:t,pathRef:i,showLabels:!w,strokeDasharray:u})}return s>0&&E>0&&(a.current=l,o.current=f),n.createElement(J,{props:r,points:l,clipPathId:t,pathRef:i,showLabels:!w,strokeDasharray:u})})}function Q(e){var{clipPathId:t,props:r}=e,{points:i,isAnimationActive:a}=r,o=(0,n.useRef)(null),l=(0,n.useRef)(0),c=(0,n.useRef)(null),u=o.current;return a&&i&&i.length&&u!==i?n.createElement(X,{props:r,clipPathId:t,previousPointsRef:o,longestAnimatedLengthRef:l,pathRef:c}):n.createElement(J,{props:r,points:i,clipPathId:t,pathRef:c,showLabels:!0})}var ee=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:(0,y.kr)(e.payload,t)});class et extends n.Component{render(){var e,{hide:t,dot:r,points:a,className:o,xAxisId:c,yAxisId:u,top:f,left:h,width:y,height:v,id:g,needClip:m,layout:b}=this.props;if(t)return null;var x=(0,i.$)("recharts-line",o),O=(0,p.uy)(g)?this.id:g,{r:E=3,strokeWidth:A=2}=null!=(e=(0,l.J9)(r,!1))?e:{r:3,strokeWidth:2},M=(0,l.y$)(r),S=2*E+A;return n.createElement(n.Fragment,null,n.createElement(s.W,{className:x},m&&n.createElement("defs",null,n.createElement(P.Q,{clipPathId:O,xAxisId:c,yAxisId:u}),!M&&n.createElement("clipPath",{id:"clipPath-dots-".concat(O)},n.createElement("rect",{x:h-S/2,y:f-S/2,width:y+S,height:v+S}))),n.createElement(Q,{props:this.props,clipPathId:O}),n.createElement(d._,{direction:"horizontal"===b?"y":"x"},n.createElement(j.zk,{xAxisId:c,yAxisId:u,data:a,dataPointFormatter:ee,errorBarOffset:0},this.props.children))),n.createElement(w,{activeDot:this.props.activeDot,points:a,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}constructor(){super(...arguments),Z(this,"id",(0,p.NF)("recharts-line-"))}}var er={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!h.m.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function en(e){var t=(0,$.e)(e,er),{activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,connectNulls:c,dot:u,hide:s,isAnimationActive:f,label:d,legendType:p,xAxisId:h,yAxisId:y}=t,g=K(t,F),{needClip:b}=(0,P.l)(h,y),{height:x,width:w,x:O,y:j}=(0,m.oM)(),M=(0,E.WX)(),S=(0,A.r)(),k=(0,n.useMemo)(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),_=(0,v.G)(e=>z(e,h,y,S,k));return"horizontal"!==M&&"vertical"!==M?null:n.createElement(et,W({},g,{connectNulls:c,dot:u,activeDot:r,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,isAnimationActive:f,hide:s,label:d,legendType:p,xAxisId:h,yAxisId:y,points:_,layout:M,height:x,width:w,left:O,top:j,needClip:b}))}function ei(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:c}=e;return c.map((e,c)=>{var u=(0,y.kr)(e,o);return"horizontal"===t?{x:(0,y.nb)({axis:r,ticks:i,bandSize:l,entry:e,index:c}),y:(0,p.uy)(u)?null:n.scale(u),value:u,payload:e}:{x:(0,p.uy)(u)?null:r.scale(u),y:(0,y.nb)({axis:n,ticks:a,bandSize:l,entry:e,index:c}),value:u,payload:e}})}class ea extends n.PureComponent{render(){return n.createElement(j._S,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},n.createElement(L.A,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,y.uM)(r,t),payload:e}]})(this.props)}),n.createElement(O.r,{fn:V,args:this.props}),n.createElement(en,this.props))}}Z(ea,"displayName","Line"),Z(ea,"defaultProps",er)},2384:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},2396:(e,t,r)=>{"use strict";r.d(t,{P:()=>h});var n=r(2115),i=r(9972),a=r(9068),o=r(5932),l=r(3433),c=r(3725),u=r(3389),s=r(8892),f=["width","height"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var p={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},h=(0,n.forwardRef)(function(e,t){var r,h=(0,u.e)(e.categoricalChartProps,p),{width:y,height:v}=h,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(h,f);if(!(0,s.F)(y)||!(0,s.F)(v))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,categoricalChartProps:O}=e;return n.createElement(i.J,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!=(r=O.id)?r:m},n.createElement(a.TK,{chartData:O.data}),n.createElement(o.s,{width:y,height:v,layout:h.layout,margin:h.margin}),n.createElement(l.p,{accessibilityLayer:h.accessibilityLayer,barCategoryGap:h.barCategoryGap,maxBarSize:h.maxBarSize,stackOffset:h.stackOffset,barGap:h.barGap,barSize:h.barSize,syncId:h.syncId,syncMethod:h.syncMethod,className:h.className}),n.createElement(c.L,d({},g,{width:y,height:v,ref:t})))})},2429:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4117);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},2434:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7064),i=r(5998),a=r(4373);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},2436:(e,t,r)=>{"use strict";var n=r(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,c=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=t,u(i)&&s({inst:i})},[e,r,t]),o(function(){return u(i)&&s({inst:i}),e(function(){u(i)&&s({inst:i})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},2465:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},2520:(e,t,r)=>{"use strict";var n=r(9641).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(1147),a=r(8221),o=r(5160),l=r(2721),c=r(3616);t.isEqualWith=function(e,t,r){return function e(t,r,u,s,f,d,p){let h=p(t,r,u,s,f,d);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,u,s,f){if(Object.is(r,u))return!0;let d=o.getTag(r),p=o.getTag(u);if(d===l.argumentsTag&&(d=l.objectTag),p===l.argumentsTag&&(p=l.objectTag),d!==p)return!1;switch(d){case l.stringTag:return r.toString()===u.toString();case l.numberTag:{let e=r.valueOf(),t=u.valueOf();return c.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),u.valueOf());case l.regexpTag:return r.source===u.source&&r.flags===u.flags;case l.functionTag:return r===u}let h=(s=s??new Map).get(r),y=s.get(u);if(null!=h&&null!=y)return h===u;s.set(r,u),s.set(u,r);try{switch(d){case l.mapTag:if(r.size!==u.size)return!1;for(let[t,n]of r.entries())if(!u.has(t)||!e(n,u.get(t),t,r,u,s,f))return!1;return!0;case l.setTag:{if(r.size!==u.size)return!1;let t=Array.from(r.values()),n=Array.from(u.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,u,s,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(u)||r.length!==u.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],u[t],t,r,u,s,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==u.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(u),s,f);case l.dataViewTag:if(r.byteLength!==u.byteLength||r.byteOffset!==u.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(u),s,f);case l.errorTag:return r.name===u.name&&r.message===u.message;case l.objectTag:{if(!(t(r.constructor,u.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(u)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(u),...a.getSymbols(u)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],a=r[i];if(!Object.hasOwn(u,i))return!1;let o=u[i];if(!e(a,o,i,r,u,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(u)}}(t,r,d,p)}(e,t,void 0,void 0,void 0,void 0,r)}},2589:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},2634:(e,t,r)=>{"use strict";r.d(t,{CU:()=>s,Lx:()=>c,u3:()=>u});var n=r(5710),i=r(4532),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:c,removeLegendPayload:u}=a.actions,s=a.reducer},2661:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,u,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(u=1,c=Array(f-1);u<f;u++)c[u-1]=arguments[u];s.fn.apply(s.context,c)}else{var d,p=s.length;for(u=0;u<p;u++)switch(s[u].once&&this.removeListener(e,s[u].fn,void 0,!0),f){case 1:s[u].fn.call(s[u].context);break;case 2:s[u].fn.call(s[u].context,t);break;case 3:s[u].fn.call(s[u].context,t,n);break;case 4:s[u].fn.call(s[u].context,t,n,i);break;default:if(!c)for(d=1,c=Array(f-1);d<f;d++)c[d-1]=arguments[d];s[u].fn.apply(s[u].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,u=[],s=l.length;c<s;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&u.push(l[c]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},2694:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(668);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},2721:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},2744:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(2384),a=r(6633),o=r(3616);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return u(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let u=e[c],s=!1;if(r(u,o,a,e,t,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function u(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,c){let u=r(t,n,i,a,o,c);return void 0!==u?!!u:l(t,n,e,c)},new Map)},t.isSetMatch=u},2767:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},2962:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8673);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},3027:(e,t,r)=>{"use strict";r.d(t,{g:()=>u});var n=r(8924),i=r(7238),a=r(215),o=r(8573),l=r(4732),c=r(7062),u=(0,n.Mz)([(e,t)=>t,i.fz,c.D0,a.Re,a.gL,a.R4,l.r1,o.HZ],l.aX)},3205:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4545),i=r(8412),a=r(177),o=r(4072);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},3222:(e,t,r)=>{"use strict";r.d(t,{$7:()=>f,Ru:()=>s,uZ:()=>u});var n=r(5710),i=r(4890),a=r(215),o=r(4732),l=r(4299),c=r(841),u=(0,n.VP)("keyDown"),s=(0,n.VP)("focus"),f=(0,n.Nc)();f.startListening({actionCreator:u,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,u=e.payload;if("ArrowRight"===u||"ArrowLeft"===u||"Enter"===u){var s=Number((0,c.P)(n,(0,a.n4)(r))),f=(0,a.R4)(r);if("Enter"===u){var d=(0,o.pg)(r,"axis","hover",String(n.index));t.dispatch((0,i.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:d}));return}var p=s+("ArrowRight"===u?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=f&&!(p>=f.length)&&!(p<0)){var h=(0,o.pg)(r,"axis","hover",String(p));t.dispatch((0,i.o4)({active:!0,activeIndex:p.toString(),activeDataKey:void 0,activeCoordinate:h}))}}}}}),f.startListening({actionCreator:s,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=(0,o.pg)(r,"axis","hover",String("0"));t.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}})},3389:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},3401:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var n=r(2115),i=r(6641),a=r(2396),o=["axis","item"],l=(0,n.forwardRef)((e,t)=>n.createElement(a.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:o,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},3433:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var n=r(2115),i=r(429),a=r(1971);function o(e){var t=(0,a.j)();return(0,n.useEffect)(()=>{t((0,i.mZ)(e))},[t,e]),null}},3504:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var n=r(2115),i=r(6641),a=r(2396),o=["axis"],l=(0,n.forwardRef)((e,t)=>n.createElement(a.P,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:o,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},3540:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(2596),i=r(2115),a=r(400),o=r.n(a),l=r(6377),c=r(675);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:u="100%",height:f="100%",minWidth:d=0,minHeight:p,maxHeight:h,children:y,debounce:v=0,id:g,className:m,onResize:b,style:x={}}=e,w=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>w.current);var[j,P]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),E=(0,i.useCallback)((e,t)=>{P(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;E(r,n),null==(t=O.current)||t.call(O,r,n)};v>0&&(e=o()(e,v,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=w.current.getBoundingClientRect();return E(r,n),t.observe(w.current),()=>{t.disconnect()}},[E,v]);var A=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=j;if(e<0||t<0)return null;(0,c.R)((0,l._3)(u)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),(0,c.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(u)?e:u,a=(0,l._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),h&&a>h&&(a=h)),(0,c.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,u,f,d,p,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({width:n,height:a},e.props.style)}))},[r,y,f,h,p,d,j,u]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},x),{},{width:u,height:f,minWidth:d,minHeight:p,maxHeight:h}),ref:w},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},A))})},3597:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>i,VU:()=>o,XC:()=>u,_U:()=>c,j2:()=>l});var n=r(2115),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],c=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(e=>{l.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},u=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];l.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=e=>(a(t,r,e),null))}),n}},3616:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},3676:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},3725:(e,t,r)=>{"use strict";r.d(t,{L:()=>R});var n=r(2115),i=r(788),a=r(7238),o=r(6752),l=r(1807),c=r(2596),u=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(0,n.forwardRef)((e,t)=>{var{children:r,width:a,height:o,viewBox:l,className:f,style:d,title:p,desc:h}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u),v=l||{width:a,height:o,x:0,y:0},g=(0,c.$)("recharts-surface",f);return n.createElement("svg",s({},(0,i.J9)(y,!0,"svg"),{className:g,width:a,height:o,style:d,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height),ref:t}),n.createElement("title",null,p),n.createElement("desc",null,h),r)}),d=r(1971),p=r(972),h=r(8892),y=["children"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={width:"100%",height:"100%"},m=(0,n.forwardRef)((e,t)=>{var r,i,l=(0,a.yi)(),c=(0,a.rY)(),u=(0,o.$)();if(!(0,h.F)(l)||!(0,h.F)(c))return null;var{children:s,otherAttributes:d,title:p,desc:y}=e;return r="number"==typeof d.tabIndex?d.tabIndex:u?0:void 0,i="string"==typeof d.role?d.role:u?"application":void 0,n.createElement(f,v({},d,{title:p,desc:y,role:i,tabIndex:r,width:l,height:c,style:g,ref:t}),s)}),b=e=>{var{children:t}=e,r=(0,d.G)(p.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(f,{width:i,height:a,x:l,y:o},t)},x=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y);return(0,l.r)()?n.createElement(b,null,r):n.createElement(m,v({ref:t},i),r)}),w=r(4890),O=r(4685),j=r(6850),P=r(3222),E=r(2589),A=r(8627),M=r(7335),S=r(6144),k=r(5115),_=(0,n.createContext)(null);function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var C=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:l,onDoubleClick:u,onMouseDown:s,onMouseEnter:f,onMouseLeave:p,onMouseMove:y,onMouseUp:v,onTouchEnd:g,onTouchMove:m,onTouchStart:b,style:x,width:C}=e,D=(0,d.j)(),[N,I]=(0,n.useState)(null),[z,L]=(0,n.useState)(null);(0,j.l3)();var R=function(){var e=(0,d.j)(),[t,r]=(0,n.useState)(null),i=(0,d.G)(E.et);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,h.H)(r)&&r!==i&&e((0,A.hF)(r))}},[t,e,i]),r}(),$=(0,n.useCallback)(e=>{R(e),"function"==typeof t&&t(e),I(e),L(e)},[R,t,I,L]),B=(0,n.useCallback)(e=>{D((0,O.ky)(e)),D((0,M.y)({handler:o,reactEvent:e}))},[D,o]),U=(0,n.useCallback)(e=>{D((0,O.dj)(e)),D((0,M.y)({handler:f,reactEvent:e}))},[D,f]),F=(0,n.useCallback)(e=>{D((0,w.xS)()),D((0,M.y)({handler:p,reactEvent:e}))},[D,p]),K=(0,n.useCallback)(e=>{D((0,O.dj)(e)),D((0,M.y)({handler:y,reactEvent:e}))},[D,y]),H=(0,n.useCallback)(()=>{D((0,P.Ru)())},[D]),G=(0,n.useCallback)(e=>{D((0,P.uZ)(e.key))},[D]),Z=(0,n.useCallback)(e=>{D((0,M.y)({handler:l,reactEvent:e}))},[D,l]),W=(0,n.useCallback)(e=>{D((0,M.y)({handler:u,reactEvent:e}))},[D,u]),V=(0,n.useCallback)(e=>{D((0,M.y)({handler:s,reactEvent:e}))},[D,s]),q=(0,n.useCallback)(e=>{D((0,M.y)({handler:v,reactEvent:e}))},[D,v]),Y=(0,n.useCallback)(e=>{D((0,M.y)({handler:b,reactEvent:e}))},[D,b]),J=(0,n.useCallback)(e=>{D((0,S.e)(e)),D((0,M.y)({handler:m,reactEvent:e}))},[D,m]),X=(0,n.useCallback)(e=>{D((0,M.y)({handler:g,reactEvent:e}))},[D,g]);return n.createElement(k.$.Provider,{value:N},n.createElement(_.Provider,{value:z},n.createElement("div",{className:(0,c.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:C,height:a},x),onClick:B,onContextMenu:Z,onDoubleClick:W,onFocus:H,onKeyDown:G,onMouseDown:V,onMouseEnter:U,onMouseLeave:F,onMouseMove:K,onMouseUp:q,onTouchEnd:X,onTouchMove:J,onTouchStart:Y,ref:$},r)))}),D=r(6377),N=r(8234),I=(0,n.createContext)(void 0),z=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,D.NF)("recharts"),"-clip")),i=(0,N.oM)();if(null==i)return null;var{x:a,y:o,width:l,height:c}=i;return n.createElement(I.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:o,height:c,width:l}))),t)},L=["children","className","width","height","style","compact","title","desc"],R=(0,n.forwardRef)((e,t)=>{var{children:r,className:a,width:o,height:l,style:c,compact:u,title:s,desc:f}=e,d=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,L),p=(0,i.J9)(d,!1);return u?n.createElement(x,{otherAttributes:p,title:s,desc:f},r):n.createElement(C,{className:a,style:c,width:o,height:l,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(x,{otherAttributes:p,title:s,desc:f,ref:t},n.createElement(z,null,r)))})},3949:(e,t,r)=>{e.exports=r(9901).range},4011:(e,t,r)=>{"use strict";r.d(t,{_:()=>y});var n=r(2115),i=r(2348),a=r(788),o=r(9226),l=r(8234),c=r(3389),u=r(4460),s=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function f(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p(e){var{direction:t,width:r,dataKey:c,isAnimationActive:f,animationBegin:p,animationDuration:h,animationEasing:y}=e,v=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,s),g=(0,a.J9)(v,!1),{data:m,dataPointFormatter:b,xAxisId:x,yAxisId:w,errorBarOffset:O}=(0,o.G9)(),j=(0,l.ZI)(x),P=(0,l.gi)(w);if((null==j?void 0:j.scale)==null||(null==P?void 0:P.scale)==null||null==m||"x"===t&&"number"!==j.type)return null;var E=m.map(e=>{var a,o,{x:l,y:s,value:v,errorVal:m}=b(e,c,t);if(!m)return null;var x=[];if(Array.isArray(m)?[a,o]=m:a=o=m,"x"===t){var{scale:w}=j,E=s+O,A=E+r,M=E-r,S=w(v-a),k=w(v+o);x.push({x1:k,y1:A,x2:k,y2:M}),x.push({x1:S,y1:E,x2:k,y2:E}),x.push({x1:S,y1:A,x2:S,y2:M})}else if("y"===t){var{scale:_}=P,T=l+O,C=T-r,D=T+r,N=_(v-a),I=_(v+o);x.push({x1:C,y1:I,x2:D,y2:I}),x.push({x1:T,y1:N,x2:T,y2:I}),x.push({x1:C,y1:N,x2:D,y2:N})}var z="".concat(l+O,"px ").concat(s+O,"px");return n.createElement(i.W,d({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},g),x.map(e=>{var t=f?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(u.i,{from:{transform:"scaleY(0)",transformOrigin:z},to:{transform:"scaleY(1)",transformOrigin:z},begin:p,easing:y,isActive:f,duration:h,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:z}},n.createElement("line",d({},e,{style:t})))}))});return n.createElement(i.W,{className:"recharts-errorBars"},E)}var h=(0,n.createContext)(void 0);function y(e){var{direction:t,children:r}=e;return n.createElement(h.Provider,{value:t},r)}var v={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function g(e){var t,r,i=(t=e.direction,r=(0,n.useContext)(h),null!=t?t:null!=r?r:"x"),{width:a,isAnimationActive:l,animationBegin:u,animationDuration:s,animationEasing:f}=(0,c.e)(e,v);return n.createElement(n.Fragment,null,n.createElement(o.pU,{dataKey:e.dataKey,direction:i}),n.createElement(p,d({},e,{direction:i,width:a,isAnimationActive:l,animationBegin:u,animationDuration:s,animationEasing:f})))}class m extends n.Component{render(){return n.createElement(g,this.props)}}f(m,"defaultProps",v),f(m,"displayName","ErrorBar")},4013:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(6377),i=r(9827);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(e,t,r,a,l,c,u)=>{if(null!=t&&null!=c){var{chartData:s,computedData:f,dataStartIndex:d,dataEndIndex:p}=r;return e.reduce((e,r)=>{var h,y,v,g,m,{dataDefinedOnItem:b,settings:x}=r,w=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((h=b,y=s,null!=h?h:y),d,p),O=null!=(v=null==x?void 0:x.dataKey)?v:null==a?void 0:a.dataKey,j=null==x?void 0:x.nameKey;return Array.isArray(g=null!=a&&a.dataKey&&Array.isArray(w)&&!Array.isArray(w[0])&&"axis"===u?(0,n.eP)(w,a.dataKey,l):c(w,t,f,j))?g.forEach(t=>{var r=o(o({},x),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:x,dataKey:O,payload:g,value:(0,i.kr)(g,O),name:null!=(m=(0,i.kr)(g,j))?m:null==x?void 0:x.name})),e},[])}}},4021:(e,t,r)=>{"use strict";r.d(t,{m:()=>en});var n=r(2115),i=r(7650),a=r(241),o=r.n(a),l=r(2596),c=r(6377);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return Array.isArray(e)&&(0,c.vh)(e[0])&&(0,c.vh)(e[1])?e.join(" ~ "):e}var p=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:p,itemSorter:h,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=e,x=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),O=!(0,c.uy)(g),j=O?g:"",P=(0,l.$)("recharts-default-tooltip",y),E=(0,l.$)("recharts-tooltip-label",v);return O&&m&&null!=s&&(j=m(g,s)),n.createElement("div",u({className:P,style:x},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:E,style:w},n.isValidElement(j)?j:"".concat(j)),(()=>{if(s&&s.length){var e=(h?o()(s,h):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||p||d,{value:o,name:l}=e,u=o,h=l;if(a){var y=a(o,l,e,r,s);if(Array.isArray(y))[u,h]=y;else{if(null==y)return null;u=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,c.vh)(h)?n.createElement("span",{className:"recharts-tooltip-item-name"},h):null,(0,c.vh)(h)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},h="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:u,viewBoxDimension:s}=e;if(a&&(0,c.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),d=r[n]+i;if(t[n])return o[n]?f:d;var p=u[n];return null==p?0:o[n]?f<p?Math.max(d,p):Math.max(f,p):null==s?0:d+l>p+s?Math.max(f,p):Math.max(d,p)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class x extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:u,isAnimationActive:s,offset:f,position:d,reverseDirection:p,useTranslate3d:g,viewBox:b,wrapperStyle:x,lastBoundingBox:w,innerRef:O,hasPortalFromProps:j}=this.props,{cssClasses:P,cssProperties:E}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:u,reverseDirection:s,tooltipBox:f,useTranslate3d:d,viewBox:p}=e;return{cssProperties:t=f.height>0&&f.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:u,reverseDirection:s,tooltipDimension:f.width,viewBox:p,viewBoxDimension:p.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:u,reverseDirection:s,tooltipDimension:f.height,viewBox:p,viewBoxDimension:p.height}),useTranslate3d:d}):y,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.$)(h,{["".concat(h,"-right")]:(0,c.Et)(r)&&t&&(0,c.Et)(t.x)&&r>=t.x,["".concat(h,"-left")]:(0,c.Et)(r)&&t&&(0,c.Et)(t.x)&&r<t.x,["".concat(h,"-bottom")]:(0,c.Et)(n)&&t&&(0,c.Et)(t.y)&&n>=t.y,["".concat(h,"-top")]:(0,c.Et)(n)&&t&&(0,c.Et)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:f,position:d,reverseDirection:p,tooltipBox:{height:w.height,width:w.width},useTranslate3d:g,viewBox:b}),A=j?{}:m(m({transition:s&&e?"transform ".concat(r,"ms ").concat(i):void 0},E),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&u?"visible":"hidden",position:"absolute",top:0,left:0}),M=m(m({},A),{},{visibility:!this.state.dismissed&&e&&u?"visible":"hidden"},x);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:P,style:M,ref:O},a)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var w=r(1643),O=r(512),j=r.n(O),P=r(7238),E=r(6752),A=r(688),M=r(788),S=["x","y","top","left","width","height","className"];function k(){return(k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:u=0,className:s}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:u},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,S));return(0,c.Et)(t)&&(0,c.Et)(r)&&(0,c.Et)(o)&&(0,c.Et)(u)&&(0,c.Et)(i)&&(0,c.Et)(a)?n.createElement("path",k({},(0,M.J9)(f,!0),{className:(0,l.$)("recharts-cross",s),d:"M".concat(t,",").concat(i,"v").concat(u,"M").concat(a,",").concat(r,"h").concat(o)})):null},C=r(4538),D=r(5641);function N(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,D.IZ)(t,r,n,i),(0,D.IZ)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}var I=r(7283),z=r(1971),L=r(9827),R=r(215);function $(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?$(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var U=r(4732);function F(){return(F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function G(e){var t,r,i,{coordinate:a,payload:o,index:c,offset:u,tooltipAxisBandSize:s,layout:f,cursor:d,tooltipEventType:p,chartName:h}=e;if(!d||!a||"ScatterChart"!==h&&"axis"!==p)return null;if("ScatterChart"===h)r=a,i=T;else if("BarChart"===h)t=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-t:u.left+.5,y:"horizontal"===f?u.top+.5:a.y-t,width:"horizontal"===f?s:u.width-1,height:"horizontal"===f?u.height-1:s},i=C.M;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=N(a);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=I.h}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return N(t);else{var{cx:l,cy:c,innerRadius:u,outerRadius:s,angle:f}=t,d=(0,D.IZ)(l,c,u,f),p=(0,D.IZ)(l,c,s,f);n=d.x,i=d.y,a=p.x,o=p.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,u)},i=A.I;var x="object"==typeof d&&"className"in d?d.className:void 0,w=H(H(H(H({stroke:"#ccc",pointerEvents:"none"},u),r),(0,M.J9)(d,!1)),{},{payload:o,payloadIndex:c,className:(0,l.$)("recharts-tooltip-cursor",x)});return(0,n.isValidElement)(d)?(0,n.cloneElement)(d,w):(0,n.createElement)(i,w)}function Z(e){var t,r,i,a=(t=(0,z.G)(R.Dn),r=(0,z.G)(R.R4),i=(0,z.G)(R.fl),(0,L.Hj)(B(B({},t),{},{scale:i}),r)),o=(0,P.W7)(),l=(0,P.WX)(),c=(0,U.fW)();return n.createElement(G,F({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:o,layout:l,tooltipAxisBandSize:a,chartName:c}))}var W=r(5115),V=r(4890),q=r(6850),Y=r(6523),J=r(3389);function X(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?X(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ee(e){return e.dataKey}var et=[],er={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!w.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function en(e){var t,r,a=(0,J.e)(e,er),{active:o,allowEscapeViewBox:l,animationDuration:c,animationEasing:u,content:s,filterNull:f,isAnimationActive:d,offset:h,payloadUniqBy:y,position:v,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:w,shared:O,trigger:A,defaultIndex:M,portal:S,axisId:k}=a,_=(0,z.j)(),T="number"==typeof M?String(M):M;(0,n.useEffect)(()=>{_((0,V.UF)({shared:O,trigger:A,axisId:k,active:o,defaultIndex:T}))},[_,O,A,k,o,T]);var C=(0,P.sk)(),D=(0,E.$)(),N=(0,Y.Td)(O),{activeIndex:I,isActive:L}=(0,z.G)(e=>(0,U.yn)(e,N,A,T)),R=(0,z.G)(e=>(0,U.u9)(e,N,A,T)),$=(0,z.G)(e=>(0,U.BZ)(e,N,A,T)),B=(0,z.G)(e=>(0,U.dS)(e,N,A,T)),F=(0,W.X)(),K=null!=o?o:L,[H,G]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([R,K]),X="axis"===N?$:void 0;(0,q.m7)(N,A,B,X,I,K);var en=null!=S?S:F;if(null==en)return null;var ei=null!=R?R:et;K||(ei=et),f&&ei.length&&(t=R.filter(e=>null!=e.value&&(!0!==e.hide||a.includeHidden)),ei=!0===y?j()(t,ee):"function"==typeof y?j()(t,y):t);var ea=ei.length>0,eo=n.createElement(x,{allowEscapeViewBox:l,animationDuration:c,animationEasing:u,isAnimationActive:d,active:K,coordinate:B,hasPayload:ea,offset:h,position:v,reverseDirection:g,useTranslate3d:m,viewBox:C,wrapperStyle:b,lastBoundingBox:H,innerRef:G,hasPortalFromProps:!!S},(r=Q(Q({},a),{},{payload:ei,label:X,active:K,coordinate:B,accessibilityLayer:D}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(p,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(eo,en),K&&n.createElement(Z,{cursor:w,tooltipEventType:N,coordinate:B,payload:R,index:I}))}},4072:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},4117:(e,t,r)=>{"use strict";var n=r(9641).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(8221),a=r(5160),o=r(2721),l=r(6633),c=r(885);function u(e,t,r,i=new Map,f){let d=f?.(e,t,r,i);if(null!=d)return d;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=u(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,a]of(i.set(e,t),e))t.set(n,u(a,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(u(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(c.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=u(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),s(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(a.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),s(t,e,r,i,f),t}return e}function s(e,t,r=e,n,a){let o=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<o.length;i++){let l=o[i],c=Object.getOwnPropertyDescriptor(e,l);(null==c||c.writable)&&(e[l]=u(t[l],l,r,n,a))}}t.cloneDeepWith=function(e,t){return u(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=u,t.copyProperties=s},4299:(e,t,r)=>{"use strict";r.d(t,{kz:()=>ir,fb:()=>n4,q:()=>im,tP:()=>iM,g1:()=>iN,iv:()=>i8,Nk:()=>n2,pM:()=>ie,Oz:()=>iv,tF:()=>i4,rj:()=>n0,ec:()=>nY,bb:()=>ix,xp:()=>iC,wL:()=>iP,sr:()=>ik,Qn:()=>iT,MK:()=>n7,IO:()=>nX,P9:()=>iu,S5:()=>io,PU:()=>nR,cd:()=>nB,eo:()=>nW,yi:()=>il,ZB:()=>i7,D5:()=>iF,iV:()=>iH,Hd:()=>nG,Gx:()=>at,DP:()=>nH,BQ:()=>i5,_y:()=>an,AV:()=>ij,um:()=>nZ,xM:()=>i_,gT:()=>id,Kr:()=>ic,$X:()=>ih,TC:()=>n9,Zi:()=>i9,CR:()=>ae,ld:()=>nV,L$:()=>i0,Rl:()=>n$,Lw:()=>iJ,KR:()=>i1,sf:()=>nU,wP:()=>i2});var n,i,a,o,l,c,u,s={};r.r(s),r.d(s,{scaleBand:()=>w,scaleDiverging:()=>function e(){var t=eG(r8()(eM));return t.copy=function(){return r5(t,e())},y.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eQ(r8()).domain([.1,1,10]);return t.copy=function(){return r5(t,e()).base(t.base())},y.apply(t,arguments)},scaleDivergingPow:()=>r6,scaleDivergingSqrt:()=>r7,scaleDivergingSymlog:()=>function e(){var t=e2(r8());return t.copy=function(){return r5(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,eE),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,eE):[0,1],eG(n)},scaleImplicit:()=>b,scaleLinear:()=>function e(){var t=eD();return t.copy=function(){return eT(t,e())},h.apply(t,arguments),eG(t)},scaleLog:()=>function e(){let t=eQ(eC()).domain([1,10]);return t.copy=()=>eT(t,e()).base(t.base()),h.apply(t,arguments),t},scaleOrdinal:()=>x,scalePoint:()=>O,scalePow:()=>e6,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=N){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[z(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(_),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},h.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[z(a,e,0,i)]:t}function c(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,c()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,c()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},h.apply(eG(l),arguments)},scaleRadial:()=>function e(){var t,r=eD(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(e9(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,eE)).map(e9)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},h.apply(a,arguments),eG(a)},scaleSequential:()=>function e(){var t=eG(r2()(eM));return t.copy=function(){return r5(t,e())},y.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eQ(r2()).domain([1,10]);return t.copy=function(){return r5(t,e()).base(t.base())},y.apply(t,arguments)},scaleSequentialPow:()=>r4,scaleSequentialQuantile:()=>function e(){var t=[],r=eM;function n(e){if(null!=e&&!isNaN(e*=1))return r((z(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(_),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return tt(e);if(t>=1)return te(e);var n,i=(n-1)*t,a=Math.floor(i),o=te((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?tr:function(e=_){if(e===_)return tr;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,c=Math.log(o),u=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*u*(o-u)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*u/o+s)),d=Math.min(i,Math.floor(r+(o-l)*u/o+s));e(t,r,f,d,a)}let o=t[r],l=n,c=i;for(tn(t,n,r),a(t[i],o)>0&&tn(t,n,i);l<c;){for(tn(t,l,c),++l,--c;0>a(t[l],o);)++l;for(;a(t[c],o)>0;)--c}0===a(t[n],o)?tn(t,n,c):tn(t,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return t})(e,a).subarray(0,a+1));return o+(tt(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},y.apply(n,arguments)},scaleSequentialSqrt:()=>r3,scaleSequentialSymlog:()=>function e(){var t=e2(r2());return t.copy=function(){return r5(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleSqrt:()=>e7,scaleSymlog:()=>function e(){var t=e2(eC());return t.copy=function(){return eT(t,e()).constant(t.constant())},h.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[z(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},h.apply(a,arguments)},scaleTime:()=>r0,scaleUtc:()=>r1,tickFormat:()=>eH});var f=r(8924),d=r(3949),p=r.n(d);function h(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function y(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class v extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(g(this,e))}has(e){return super.has(g(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function g({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function x(){var e=new v,t=[],r=[],n=b;function i(i){let a=e.get(i);if(void 0===a){if(n!==b)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new v,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return x(t,r).unknown(n)},h.apply(i,arguments),i}function w(){var e,t,r=x().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,c=0,u=0,s=.5;function f(){var r=n().length,f=o<a,d=f?o:a,p=f?a:o;e=(p-d)/Math.max(1,r-c+2*u),l&&(e=Math.floor(e)),d+=(p-d-e*(r-c))*s,t=e*(1-c),l&&(d=Math.round(d),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return d+e*t});return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,u=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(u=+e,f()):u},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return w(n(),[a,o]).round(l).paddingInner(c).paddingOuter(u).align(s)},h.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(w.apply(null,arguments).paddingInner(1))}let j=Math.sqrt(50),P=Math.sqrt(10),E=Math.sqrt(2);function A(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),c=o/Math.pow(10,l),u=c>=j?10:c>=P?5:c>=E?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/u)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*u)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?A(e,t,2*r):[n,i,a]}function M(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?A(t,e,r):A(e,t,r);if(!(a>=i))return[];let l=a-i+1,c=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)c[e]=-((a-e)/o);else for(let e=0;e<l;++e)c[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)c[e]=-((i+e)/o);else for(let e=0;e<l;++e)c[e]=(i+e)*o;return c}function S(e,t,r){return A(e*=1,t*=1,r*=1)[2]}function k(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?S(t,e,r):S(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function _(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function T(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=_,r=(t,r)=>_(e(t),r),n=(t,r)=>e(t)-r):(t=e===_||e===T?e:D,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function D(){return 0}function N(e){return null===e?NaN:+e}let I=C(_),z=I.right;function L(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function R(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function $(){}I.left,C(N).center;var B="\\s*([+-]?\\d+)\\s*",U="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",F="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",K=/^#([0-9a-f]{3,8})$/,H=RegExp(`^rgb\\(${B},${B},${B}\\)$`),G=RegExp(`^rgb\\(${F},${F},${F}\\)$`),Z=RegExp(`^rgba\\(${B},${B},${B},${U}\\)$`),W=RegExp(`^rgba\\(${F},${F},${F},${U}\\)$`),V=RegExp(`^hsl\\(${U},${F},${F}\\)$`),q=RegExp(`^hsla\\(${U},${F},${F},${U}\\)$`),Y={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function J(){return this.rgb().formatHex()}function X(){return this.rgb().formatRgb()}function Q(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=K.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ee(t):3===r?new en(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?et(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?et(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=H.exec(e))?new en(t[1],t[2],t[3],1):(t=G.exec(e))?new en(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Z.exec(e))?et(t[1],t[2],t[3],t[4]):(t=W.exec(e))?et(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=V.exec(e))?eu(t[1],t[2]/100,t[3]/100,1):(t=q.exec(e))?eu(t[1],t[2]/100,t[3]/100,t[4]):Y.hasOwnProperty(e)?ee(Y[e]):"transparent"===e?new en(NaN,NaN,NaN,0):null}function ee(e){return new en(e>>16&255,e>>8&255,255&e,1)}function et(e,t,r,n){return n<=0&&(e=t=r=NaN),new en(e,t,r,n)}function er(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof $||(i=Q(i)),i)?new en((i=i.rgb()).r,i.g,i.b,i.opacity):new en:new en(e,t,r,null==n?1:n)}function en(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function ei(){return`#${ec(this.r)}${ec(this.g)}${ec(this.b)}`}function ea(){let e=eo(this.opacity);return`${1===e?"rgb(":"rgba("}${el(this.r)}, ${el(this.g)}, ${el(this.b)}${1===e?")":`, ${e})`}`}function eo(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function el(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function ec(e){return((e=el(e))<16?"0":"")+e.toString(16)}function eu(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ef(e,t,r,n)}function es(e){if(e instanceof ef)return new ef(e.h,e.s,e.l,e.opacity);if(e instanceof $||(e=Q(e)),!e)return new ef;if(e instanceof ef)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new ef(o,l,c,e.opacity)}function ef(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ed(e){return(e=(e||0)%360)<0?e+360:e}function ep(e){return Math.max(0,Math.min(1,e||0))}function eh(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ey(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}L($,Q,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:J,formatHex:J,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return es(this).formatHsl()},formatRgb:X,toString:X}),L(en,er,R($,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new en(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new en(el(this.r),el(this.g),el(this.b),eo(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ei,formatHex:ei,formatHex8:function(){return`#${ec(this.r)}${ec(this.g)}${ec(this.b)}${ec((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:ea,toString:ea})),L(ef,function(e,t,r,n){return 1==arguments.length?es(e):new ef(e,t,r,null==n?1:n)},R($,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new en(eh(e>=240?e-240:e+120,i,n),eh(e,i,n),eh(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ef(ed(this.h),ep(this.s),ep(this.l),eo(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=eo(this.opacity);return`${1===e?"hsl(":"hsla("}${ed(this.h)}, ${100*ep(this.s)}%, ${100*ep(this.l)}%${1===e?")":`, ${e})`}`}}));let ev=e=>()=>e;function eg(e,t){var r=t-e;return r?function(t){return e+t*r}:ev(isNaN(e)?t:e)}let em=function e(t){var r,n=1==(r=+t)?eg:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):ev(isNaN(e)?t:e)};function i(e,t){var r=n((e=er(e)).r,(t=er(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=eg(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function eb(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=er(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}function ex(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}eb(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return ey((r-n/t)*t,o,i,a,l)}}),eb(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return ey((r-n/t)*t,i,a,o,l)}});var ew=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eO=RegExp(ew.source,"g");function ej(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ev(t):("number"===i?ex:"string"===i?(n=Q(t))?(t=n,em):function(e,t){var r,n,i,a,o,l=ew.lastIndex=eO.lastIndex=0,c=-1,u=[],s=[];for(e+="",t+="";(i=ew.exec(e))&&(a=eO.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),u[c]?u[c]+=o:u[++c]=o),(i=i[0])===(a=a[0])?u[c]?u[c]+=a:u[++c]=a:(u[++c]=null,s.push({i:c,x:ex(i,a)})),l=eO.lastIndex;return l<t.length&&(o=t.slice(l),u[c]?u[c]+=o:u[++c]=o),u.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)u[(r=s[n]).i]=r.x(e);return u.join("")})}:t instanceof Q?em:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=ej(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ej(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ex:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function eP(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function eE(e){return+e}var eA=[0,1];function eM(e){return e}function eS(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function ek(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=eS(i,n),a=r(o,a)):(n=eS(n,i),a=r(a,o)),function(e){return a(n(e))}}function e_(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=eS(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=z(e,t,1,n)-1;return a[r](i[r](t))}}function eT(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eC(){var e,t,r,n,i,a,o=eA,l=eA,c=ej,u=eM;function s(){var e,t,r,c=Math.min(o.length,l.length);return u!==eM&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),u=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?e_:ek,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,c)))(e(u(t)))}return f.invert=function(r){return u(t((a||(a=n(l,o.map(e),ex)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,eE),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=eP,s()},f.clamp=function(e){return arguments.length?(u=!!e||eM,s()):u!==eM},f.interpolate=function(e){return arguments.length?(c=e,s()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function eD(){return eC()(eM,eM)}var eN=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eI(e){var t;if(!(t=eN.exec(e)))throw Error("invalid format: "+e);return new ez({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ez(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function eL(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function eR(e){return(e=eL(Math.abs(e)))?e[1]:NaN}function e$(e,t){var r=eL(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}eI.prototype=ez.prototype,ez.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eB={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>e$(100*e,t),r:e$,s:function(e,t){var r=eL(e,t);if(!r)return e+"";var i=r[0],a=r[1],o=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=i.length;return o===l?i:o>l?i+Array(o-l+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+eL(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eU(e){return e}var eF=Array.prototype.map,eK=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eH(e,t,r,n){var i,l,c=k(e,t,r);switch((n=eI(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(l=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eR(u)/3)))-eR(Math.abs(c))))||(n.precision=l),o(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(l=Math.max(0,eR(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=c)))-eR(i))+1)||(n.precision=l-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(l=Math.max(0,-eR(Math.abs(c))))||(n.precision=l-("%"===n.type)*2)}return a(n)}function eG(e){var t=e.domain;return e.ticks=function(e){var r=t();return M(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eH(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],u=a[l],s=10;for(u<c&&(i=c,c=u,u=i,i=o,o=l,l=i);s-- >0;){if((i=S(c,u,r))===n)return a[o]=c,a[l]=u,t(a);if(i>0)c=Math.floor(c/i)*i,u=Math.ceil(u/i)*i;else if(i<0)c=Math.ceil(c*i)/i,u=Math.floor(u*i)/i;else break;n=i}return e},e}function eZ(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function eW(e){return Math.log(e)}function eV(e){return Math.exp(e)}function eq(e){return-Math.log(-e)}function eY(e){return-Math.exp(-e)}function eJ(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eX(e){return(t,r)=>-e(-t,r)}function eQ(e){let t,r,n=e(eW,eV),i=n.domain,o=10;function l(){var a,l;return t=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(l=o)?eJ:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eX(t),r=eX(r),e(eq,eY)):e(eW,eV),n}return n.base=function(e){return arguments.length?(o=+e,l()):o},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,a,l=i(),c=l[0],u=l[l.length-1],s=u<c;s&&([c,u]=[u,c]);let f=t(c),d=t(u),p=null==e?10:+e,h=[];if(!(o%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),c>0){for(;f<=d;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<c)){if(a>u)break;h.push(a)}}else for(;f<=d;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<c)){if(a>u)break;h.push(a)}2*h.length<p&&(h=M(c,u,p))}else h=M(f,d,Math.min(d-f,p)).map(r);return s?h.reverse():h},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=eI(i)).precision||(i.trim=!0),i=a(i)),e===1/0)return i;let l=Math.max(1,o*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*o<o-.5&&(n*=o),n<=l?i(e):""}},n.nice=()=>i(eZ(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function e0(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function e1(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function e2(e){var t=1,r=e(e0(1),e1(t));return r.constant=function(r){return arguments.length?e(e0(t=+r),e1(t)):t},eG(r)}function e5(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function e4(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function e3(e){return e<0?-e*e:e*e}function e8(e){var t=e(eM,eM),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(eM,eM):.5===r?e(e4,e3):e(e5(r),e5(1/r)):r},eG(t)}function e6(){var e=e8(eC());return e.copy=function(){return eT(e,e6()).exponent(e.exponent())},h.apply(e,arguments),e}function e7(){return e6.apply(null,arguments).exponent(.5)}function e9(e){return Math.sign(e)*e*e}function te(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tt(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function tr(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function tn(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}a=(i=function(e){var t,r,i,a=void 0===e.grouping||void 0===e.thousands?eU:(t=eF.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",c=void 0===e.decimal?".":e.decimal+"",u=void 0===e.numerals?eU:(i=eF.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",d=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=eI(e)).fill,r=e.align,i=e.sign,p=e.symbol,h=e.zero,y=e.width,v=e.comma,g=e.precision,m=e.trim,b=e.type;"n"===b?(v=!0,b="g"):eB[b]||(void 0===g&&(g=12),m=!0,b="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var x="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===p?l:/[%p]/.test(b)?s:"",O=eB[b],j=/[defgprs%]/.test(b);function P(e){var o,l,s,p=x,P=w;if("c"===b)P=O(e)+P,e="";else{var E=(e*=1)<0||1/e<0;if(e=isNaN(e)?d:O(Math.abs(e),g),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),E&&0==+e&&"+"!==i&&(E=!1),p=(E?"("===i?i:f:"-"===i||"("===i?"":i)+p,P=("s"===b?eK[8+n/3]:"")+P+(E&&"("===i?")":""),j){for(o=-1,l=e.length;++o<l;)if(48>(s=e.charCodeAt(o))||s>57){P=(46===s?c+e.slice(o+1):e.slice(o))+P,e=e.slice(0,o);break}}}v&&!h&&(e=a(e,1/0));var A=p.length+e.length+P.length,M=A<y?Array(y-A+1).join(t):"";switch(v&&h&&(e=a(M+e,M.length?y-P.length:1/0),M=""),r){case"<":e=p+e+P+M;break;case"=":e=p+M+e+P;break;case"^":e=M.slice(0,A=M.length>>1)+p+e+P+M.slice(A);break;default:e=M+p+e+P}return u(e)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),P.toString=function(){return e+""},P}return{format:p,formatPrefix:function(e,t){var r=p(((e=eI(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(eR(t)/3))),i=Math.pow(10,-n),a=eK[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,o=i.formatPrefix;let ti=new Date,ta=new Date;function to(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>to(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(ti.setTime(+t),ta.setTime(+n),e(ti),e(ta),Math.floor(r(ti,ta))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let tl=to(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);tl.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?to(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):tl:null,tl.range;let tc=to(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tc.range;let tu=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tu.range;let ts=to(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());ts.range;let tf=to(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());tf.range;let td=to(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());td.range;let tp=to(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);tp.range;let th=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);th.range;let ty=to(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function tv(e){return to(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}ty.range;let tg=tv(0),tm=tv(1),tb=tv(2),tx=tv(3),tw=tv(4),tO=tv(5),tj=tv(6);function tP(e){return to(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}tg.range,tm.range,tb.range,tx.range,tw.range,tO.range,tj.range;let tE=tP(0),tA=tP(1),tM=tP(2),tS=tP(3),tk=tP(4),t_=tP(5),tT=tP(6);tE.range,tA.range,tM.range,tS.range,tk.range,t_.range,tT.range;let tC=to(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tC.range;let tD=to(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tD.range;let tN=to(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tN.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tN.range;let tI=to(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tz(e,t,r,n,i,a){let o=[[tc,1,1e3],[tc,5,5e3],[tc,15,15e3],[tc,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=C(([,,e])=>e).right(o,i);if(a===o.length)return e.every(k(t/31536e6,r/31536e6,n));if(0===a)return tl.every(Math.max(k(t,r,n),1));let[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}tI.every=e=>isFinite(e=Math.floor(e))&&e>0?to(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tI.range;let[tL,tR]=tz(tI,tD,tE,ty,td,ts),[t$,tB]=tz(tN,tC,tg,tp,tf,tu);function tU(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tF(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tK(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tH={"-":"",_:" ",0:"0"},tG=/^\s*\d+/,tZ=/^%/,tW=/[\\^$*+?|[\]().{}]/g;function tV(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function tq(e){return e.replace(tW,"\\$&")}function tY(e){return RegExp("^(?:"+e.map(tq).join("|")+")","i")}function tJ(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tX(e,t,r){var n=tG.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tQ(e,t,r){var n=tG.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function t0(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function t1(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tG.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function t4(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function t3(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function t8(e,t,r){var n=tG.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t6(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t7(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t9(e,t,r){var n=tG.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function rt(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function rr(e,t,r){var n=tG.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function rn(e,t,r){var n=tG.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function ri(e,t,r){var n=tG.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function ra(e,t,r){var n=tZ.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function ro(e,t,r){var n=tG.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function rl(e,t,r){var n=tG.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function rc(e,t){return tV(e.getDate(),t,2)}function ru(e,t){return tV(e.getHours(),t,2)}function rs(e,t){return tV(e.getHours()%12||12,t,2)}function rf(e,t){return tV(1+tp.count(tN(e),e),t,3)}function rd(e,t){return tV(e.getMilliseconds(),t,3)}function rp(e,t){return rd(e,t)+"000"}function rh(e,t){return tV(e.getMonth()+1,t,2)}function ry(e,t){return tV(e.getMinutes(),t,2)}function rv(e,t){return tV(e.getSeconds(),t,2)}function rg(e){var t=e.getDay();return 0===t?7:t}function rm(e,t){return tV(tg.count(tN(e)-1,e),t,2)}function rb(e){var t=e.getDay();return t>=4||0===t?tw(e):tw.ceil(e)}function rx(e,t){return e=rb(e),tV(tw.count(tN(e),e)+(4===tN(e).getDay()),t,2)}function rw(e){return e.getDay()}function rO(e,t){return tV(tm.count(tN(e)-1,e),t,2)}function rj(e,t){return tV(e.getFullYear()%100,t,2)}function rP(e,t){return tV((e=rb(e)).getFullYear()%100,t,2)}function rE(e,t){return tV(e.getFullYear()%1e4,t,4)}function rA(e,t){var r=e.getDay();return tV((e=r>=4||0===r?tw(e):tw.ceil(e)).getFullYear()%1e4,t,4)}function rM(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tV(t/60|0,"0",2)+tV(t%60,"0",2)}function rS(e,t){return tV(e.getUTCDate(),t,2)}function rk(e,t){return tV(e.getUTCHours(),t,2)}function r_(e,t){return tV(e.getUTCHours()%12||12,t,2)}function rT(e,t){return tV(1+th.count(tI(e),e),t,3)}function rC(e,t){return tV(e.getUTCMilliseconds(),t,3)}function rD(e,t){return rC(e,t)+"000"}function rN(e,t){return tV(e.getUTCMonth()+1,t,2)}function rI(e,t){return tV(e.getUTCMinutes(),t,2)}function rz(e,t){return tV(e.getUTCSeconds(),t,2)}function rL(e){var t=e.getUTCDay();return 0===t?7:t}function rR(e,t){return tV(tE.count(tI(e)-1,e),t,2)}function r$(e){var t=e.getUTCDay();return t>=4||0===t?tk(e):tk.ceil(e)}function rB(e,t){return e=r$(e),tV(tk.count(tI(e),e)+(4===tI(e).getUTCDay()),t,2)}function rU(e){return e.getUTCDay()}function rF(e,t){return tV(tA.count(tI(e)-1,e),t,2)}function rK(e,t){return tV(e.getUTCFullYear()%100,t,2)}function rH(e,t){return tV((e=r$(e)).getUTCFullYear()%100,t,2)}function rG(e,t){return tV(e.getUTCFullYear()%1e4,t,4)}function rZ(e,t){var r=e.getUTCDay();return tV((e=r>=4||0===r?tk(e):tk.ceil(e)).getUTCFullYear()%1e4,t,4)}function rW(){return"+0000"}function rV(){return"%"}function rq(e){return+e}function rY(e){return Math.floor(e/1e3)}function rJ(e){return new Date(e)}function rX(e){return e instanceof Date?+e:+new Date(+e)}function rQ(e,t,r,n,i,a,o,l,c,u){var s=eD(),f=s.invert,d=s.domain,p=u(".%L"),h=u(":%S"),y=u("%I:%M"),v=u("%I %p"),g=u("%a %d"),m=u("%b %d"),b=u("%B"),x=u("%Y");function w(e){return(c(e)<e?p:l(e)<e?h:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?d(Array.from(e,rX)):d().map(rJ)},s.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:u(t)},s.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(eZ(r,e)):s},s.copy=function(){return eT(s,rQ(e,t,r,n,i,a,o,l,c,u))},s}function r0(){return h.apply(rQ(t$,tB,tN,tC,tg,tp,tf,tu,tc,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function r1(){return h.apply(rQ(tL,tR,tI,tD,tE,th,td,ts,tc,u).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r2(){var e,t,r,n,i,a=0,o=1,l=eM,c=!1;function u(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),u):[l(0),l(1)]}}return u.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),u):[a,o]},u.clamp=function(e){return arguments.length?(c=!!e,u):c},u.interpolator=function(e){return arguments.length?(l=e,u):l},u.range=s(ej),u.rangeRound=s(eP),u.unknown=function(e){return arguments.length?(i=e,u):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),u}}function r5(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function r4(){var e=e8(r2());return e.copy=function(){return r5(e,r4()).exponent(e.exponent())},y.apply(e,arguments)}function r3(){return r4.apply(null,arguments).exponent(.5)}function r8(){var e,t,r,n,i,a,o,l=0,c=.5,u=1,s=1,f=eM,d=!1;function p(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=ej);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([l,c,u]=o,e=a(l*=1),t=a(c*=1),r=a(u*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,p):[l,c,u]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(ej),p.rangeRound=h(eP),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return a=o,e=o(l),t=o(c),r=o(u),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,p}}function r6(){var e=e8(r8());return e.copy=function(){return r5(e,r6()).exponent(e.exponent())},y.apply(e,arguments)}function r7(){return r6.apply(null,arguments).exponent(.5)}c=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,u=tY(i),s=tJ(i),f=tY(a),d=tJ(a),p=tY(o),h=tJ(o),y=tY(l),v=tJ(l),g=tY(c),m=tJ(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:rc,e:rc,f:rp,g:rP,G:rA,H:ru,I:rs,j:rf,L:rd,m:rh,M:ry,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rq,s:rY,S:rv,u:rg,U:rm,V:rx,w:rw,W:rO,x:null,X:null,y:rj,Y:rE,Z:rM,"%":rV},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:rS,e:rS,f:rD,g:rH,G:rZ,H:rk,I:r_,j:rT,L:rC,m:rN,M:rI,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rq,s:rY,S:rz,u:rL,U:rR,V:rB,w:rU,W:rF,x:null,X:null,y:rK,Y:rG,Z:rW,"%":rV},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:t7,e:t7,f:ri,g:t4,G:t5,H:re,I:re,j:t9,L:rn,m:t6,M:rt,p:function(e,t,r){var n=u.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:t8,Q:ro,s:rl,S:rr,u:tQ,U:t0,V:t1,w:tX,W:t2,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:t4,Y:t5,Z:t3,"%":ra};function O(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,u=e.length;for(r instanceof Date||(r=new Date(+r));++l<u;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=tH[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=tK(1900,void 0,1);if(P(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=tF(tK(a.y,0,1))).getUTCDay())>4||0===i?tA.ceil(n):tA(n),n=th.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=tU(tK(a.y,0,1))).getDay())>4||0===i?tm.ceil(n):tm(n),n=tp.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?tF(tK(a.y,0,1)).getUTCDay():tU(tK(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,tF(a)):tU(a)}}function P(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in tH?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,u=l.utcFormat,l.utcParse;var r9=r(7238),ne=r(9827),nt=r(356),nr=r(6377),nn=r(8892);function ni(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,nn.H)(t)&&(0,nn.H)(r))return!0}return!1}function na(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var no=r(8870),nl=r.n(no),nc=e=>e,nu={},ns=e=>function t(){let r;return 0==arguments.length||1==arguments.length&&(r=arguments.length<=0?void 0:arguments[0],r===nu)?t:e(...arguments)},nf=(e,t)=>1===e?t:ns(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==nu).length;return a>=e?t(...n):nf(e-a,ns(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>e===nu?r.shift():e),...r)}))}),nd=e=>nf(e.length,e),np=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},nh=nd((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),ny=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nc;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},nv=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),ng=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function nm(e){return 0===e?1:Math.floor(new(nl())(e).abs().log(10).toNumber())+1}function nb(e,t,r){for(var n=new(nl())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}nd((e,t,r)=>{var n=+e;return n+r*(t-n)}),nd((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),nd((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nx=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nw=(e,t,r)=>{if(e.lte(0))return new(nl())(0);var n=nm(e.toNumber()),i=new(nl())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(nl())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(nl())(t?l.toNumber():Math.ceil(l.toNumber()))},nO=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(nl())(0),tickMin:new(nl())(0),tickMax:new(nl())(0)};var o=nw(new(nl())(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new(nl())(0):(i=new(nl())(e).add(t).div(2)).sub(new(nl())(i).mod(o))).sub(e).div(o).toNumber()),c=Math.ceil(new(nl())(t).sub(i).div(o).toNumber()),u=l+c+1;return u>r?nO(e,t,r,n,a+1):(u<r&&(c=t>0?c+(r-u):c,l=t>0?l:l+(r-u)),{step:o,tickMin:i.sub(new(nl())(l).mul(o)),tickMax:i.add(new(nl())(c).mul(o))})},nj=ng(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=nx([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...np(0,n-1).map(()=>1/0)]:[...np(0,n-1).map(()=>-1/0),l];return t>r?nv(c):c}if(o===l){var u=new(nl())(1),s=new(nl())(o);if(!s.isint()&&i){var f=Math.abs(o);f<1?(u=new(nl())(10).pow(nm(o)-1),s=new(nl())(Math.floor(s.div(u).toNumber())).mul(u)):f>1&&(s=new(nl())(Math.floor(o)))}else 0===o?s=new(nl())(Math.floor((n-1)/2)):i||(s=new(nl())(Math.floor(o)));var d=Math.floor((n-1)/2);return ny(nh(e=>s.add(new(nl())(e-d).mul(u)).toNumber()),np)(0,n)}var{step:p,tickMin:h,tickMax:y}=nO(o,l,a,i,0),v=nb(h,y.add(new(nl())(.1).mul(p)),p);return t>r?nv(v):v}),nP=ng(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nx([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=nw(new(nl())(o).sub(a).div(l-1),i,0),u=[...nb(new(nl())(a),new(nl())(o),c),o];return!1===i&&(u=u.map(e=>Math.round(e))),r>n?nv(u):u}),nE=r(2589),nA=r(6908),nM=r(8573),nS=r(972),nk=r(8478),n_=r(7062),nT=r(6038),nC=r(2287),nD=r(8190),nN=r(4421);function nI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nI(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nL=[0,"auto"],nR={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},n$=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?nR:r},nB={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:nL,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nN.tQ},nU=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nB:r},nF={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},nK=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nF:r},nH=(e,t,r)=>{switch(t){case"xAxis":return n$(e,r);case"yAxis":return nU(e,r);case"zAxis":return nK(e,r);case"angleAxis":return(0,n_.Be)(e,r);case"radiusAxis":return(0,n_.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nG=(e,t,r)=>{switch(t){case"xAxis":return n$(e,r);case"yAxis":return nU(e,r);case"angleAxis":return(0,n_.Be)(e,r);case"radiusAxis":return(0,n_.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nZ=e=>e.graphicalItems.countOfBars>0;function nW(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var nV=e=>e.graphicalItems.cartesianItems,nq=(0,f.Mz)([nT.N,nC.E],nW),nY=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nJ=(0,f.Mz)([nV,nH,nq],nY),nX=e=>e.filter(e=>void 0===e.stackId),nQ=(0,f.Mz)([nJ],nX),n0=e=>e.map(e=>e.data).filter(Boolean).flat(1),n1=(0,f.Mz)([nJ],n0),n2=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},n5=(0,f.Mz)([n1,nt.HS],n2),n4=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,ne.kr)(e,t)}))):e.map(e=>({value:e})),n3=(0,f.Mz)([n5,nH,nJ],n4);function n8(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function n6(e){return e.filter(e=>(0,nr.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,nr.M8)(e))}var n7=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:(0,ne.yy)(e,a,r),graphicalItems:i}]})),n9=(0,f.Mz)([n5,nJ,nk.eC],n7),ie=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,ne.Mk)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},it=(0,f.Mz)([n9,nt.LF,nT.N],ie),ir=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>n8(n,e)),l=(0,ne.kr)(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,nr.M8)(t)||!r.length?[]:n6(r.flatMap(r=>{var n,i,a=(0,ne.kr)(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,nn.H)(n)&&(0,nn.H)(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,ne.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),ii=(0,f.Mz)(n5,nH,nQ,nT.N,ir);function ia(e){var{value:t}=e;if((0,nr.vh)(t)||t instanceof Date)return t}var io=e=>{var t;if(null==e||!("domain"in e))return nL;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=n6(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:nL},il=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},ic=e=>e.referenceElements.dots,iu=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),is=(0,f.Mz)([ic,nT.N,nC.E],iu),id=e=>e.referenceElements.areas,ip=(0,f.Mz)([id,nT.N,nC.E],iu),ih=e=>e.referenceElements.lines,iy=(0,f.Mz)([ih,nT.N,nC.E],iu),iv=(e,t)=>{var r=n6(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ig=(0,f.Mz)(is,nT.N,iv),im=(e,t)=>{var r=n6(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ib=(0,f.Mz)([ip,nT.N],im),ix=(e,t)=>{var r=n6(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iw=(0,f.Mz)(iy,nT.N,ix),iO=(0,f.Mz)(ig,iw,ib,(e,t,r)=>il(e,r,t)),ij=(0,f.Mz)([nH],io),iP=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,nn.H)(i))r=i;else if("function"==typeof i)return;if((0,nn.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(ni(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(ni(n))return na(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,nr.Et)(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&ne.IH.test(o)){var c=ne.IH.exec(o);if(null==c||null==t)i=void 0;else{var u=+c[1];i=t[0]-u}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if((0,nr.Et)(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&ne.qx.test(l)){var s=ne.qx.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var d=[i,a];if(ni(d))return null==t?d:na(d,t,r)}}}(t,il(r,i,(e=>{var t=n6(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]})(n)),e.allowDataOverflow)},iE=(0,f.Mz)([nH,ij,it,ii,iO],iP),iA=[0,1],iM=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:c}=e,u=(0,ne._L)(t,a);return u&&null==l?p()(0,r.length):"category"===c?((e,t,r)=>{var n=e.map(ia).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,nr.CG)(n))?p()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,u):"expand"===i?iA:o}},iS=(0,f.Mz)([nH,r9.fz,n5,n3,nk.eC,nT.N,iE],iM),ik=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat((0,nr.Zb)(a));return l in s?l:"point"}}},i_=(0,f.Mz)([nH,r9.fz,nZ,nk.iO,nT.N],ik);function iT(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in s)return s[e]();var t="scale".concat((0,nr.Zb)(e));if(t in s)return s[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(0,ne.YB)(a),a}}}var iC=(e,t,r)=>{var n=io(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&ni(e))return nj(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&ni(e))return nP(e,t.tickCount,t.allowDecimals)}},iD=(0,f.Mz)([iS,nG,i_],iC),iN=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&ni(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,iI=(0,f.Mz)([nH,iS,iD,nT.N],iN),iz=(0,f.Mz)(n3,nH,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(n6(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),iL=(0,f.Mz)(iz,r9.fz,nk.gY,nM.HZ,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,nn.H)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,nr.F4)(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),iR=(0,f.Mz)(n$,(e,t)=>{var r=n$(e,t);return null==r||"string"!=typeof r.padding?0:iL(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),i$=(0,f.Mz)(nU,(e,t)=>{var r=nU(e,t);return null==r||"string"!=typeof r.padding?0:iL(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),iB=(0,f.Mz)([nM.HZ,iR,nS.U,nS.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),iU=(0,f.Mz)([nM.HZ,r9.fz,i$,nS.U,nS.C,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),iF=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iB(e,r,n);case"yAxis":return iU(e,r,n);case"zAxis":return null==(i=nK(e,r))?void 0:i.range;case"angleAxis":return(0,n_.Cv)(e);case"radiusAxis":return(0,n_.Dc)(e,r);default:return}},iK=(0,f.Mz)([nH,iF],nD.I),iH=(0,f.Mz)([nH,i_,iI,iK],iT);function iG(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,f.Mz)(nJ,nT.N,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>n8(t,e)));var iZ=(e,t)=>t,iW=(e,t,r)=>r,iV=(0,f.Mz)(nA.h,iZ,iW,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iG)),iq=(0,f.Mz)(nA.W,iZ,iW,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(iG)),iY=(e,t)=>({width:e.width,height:t.height}),iJ=(0,f.Mz)(nM.HZ,n$,iY),iX=(0,f.Mz)(nE.A$,nM.HZ,iV,iZ,iW,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=iY(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height}),o}),iQ=(0,f.Mz)(nE.Lp,nM.HZ,iq,iZ,iW,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:nN.tQ,height:e.height}))(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width}),o}),i0=(e,t)=>{var r=(0,nM.HZ)(e),n=n$(e,t);if(null!=n){var i=iX(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},i1=(e,t)=>{var r=(0,nM.HZ)(e),n=nU(e,t);if(null!=n){var i=iQ(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},i2=(0,f.Mz)(nM.HZ,nU,(e,t)=>({width:"number"==typeof t.width?t.width:nN.tQ,height:e.height})),i5=(e,t,r)=>{switch(t){case"xAxis":return iJ(e,r).width;case"yAxis":return i2(e,r).height;default:return}},i4=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,ne._L)(e,n),c=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&(0,nr.CG)(c))return c}},i3=(0,f.Mz)([r9.fz,n3,nH,nT.N],i4),i8=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,ne._L)(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},i6=(0,f.Mz)([r9.fz,n3,nG,nT.N],i8),i7=(0,f.Mz)([r9.fz,(e,t,r)=>{switch(t){case"xAxis":return n$(e,r);case"yAxis":return nU(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},i_,iH,i3,i6,iF,iD,nT.N],(e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var u=(0,ne._L)(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:u,niceTicks:l,range:o,realScaleType:r,scale:n}}),i9=(0,f.Mz)([r9.fz,nG,i_,iH,iD,iF,i3,i6,nT.N],(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var u=(0,ne._L)(e,c),{type:s,ticks:f,tickCount:d}=t,p="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,h="category"===s&&n.bandwidth?n.bandwidth()/p:0;h="angleAxis"===c&&null!=a&&a.length>=2?2*(0,nr.sA)(a[0]-a[1])*h:h;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+h,value:e,offset:h})).filter(e=>!(0,nr.M8)(e.coordinate)):u&&l?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+h,value:e,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:o?o[e]:e,index:t,offset:h}))}}),ae=(0,f.Mz)([r9.fz,nG,iH,iF,i3,i6,nT.N],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,ne._L)(e,o),{tickCount:c}=t,u=0;return(u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,nr.sA)(n[0]-n[1])*u:u,l&&a)?a.map((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u})):r.ticks?r.ticks(c).map(e=>({coordinate:r(e)+u,value:e,offset:u})):r.domain().map((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u}))}}),at=(0,f.Mz)(nH,iH,(e,t)=>{if(null!=e&&null!=t)return nz(nz({},e),{},{scale:t})}),ar=(0,f.Mz)([nH,i_,iS,iK],iT);(0,f.Mz)((e,t,r)=>nK(e,r),ar,(e,t)=>{if(null!=e&&null!=t)return nz(nz({},e),{},{scale:t})});var an=(0,f.Mz)([r9.fz,nA.h,nA.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},4373:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8412),i=r(8179),a=r(2384),o=r(3616);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},4421:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},4460:(e,t,r)=>{"use strict";r.d(t,{i:()=>M});var n=r(2115),i=r(2188),a=r.n(i),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),c=(e,t)=>r=>l(o(e,t),r),u=function(){let e,t;for(var r,n,i,a,u=arguments.length,s=Array(u),f=0;f<u;f++)s[f]=arguments[f];if(1===s.length)switch(s[0]){case"linear":[r,i,n,a]=[0,0,1,1];break;case"ease":[r,i,n,a]=[.25,.1,.25,1];break;case"ease-in":[r,i,n,a]=[.42,0,1,1];break;case"ease-out":[r,i,n,a]=[.42,0,.58,1];break;case"ease-in-out":[r,i,n,a]=[0,0,.58,1];break;default:var d=s[0].split("(");"cubic-bezier"===d[0]&&4===d[1].split(")")[0].split(",").length&&([r,i,n,a]=d[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===s.length&&([r,i,n,a]=s);var p=c(r,n),h=c(i,a),y=(e=r,t=n,r=>l([...o(e,t).map((e,t)=>e*t).slice(1),0],r)),v=e=>e>1?1:e<0?0:e,g=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=p(r)-t,a=y(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=v(r-i/a)}return h(r)};return g.isStepper=!1,g},s=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i};function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var p=(e,t)=>Object.keys(t).reduce((r,n)=>d(d({},r),{},{[n]:e(n,t[n])}),{});function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var v=(e,t,r)=>e+(t-e)*r,g=e=>{var{from:t,to:r}=e;return t!==r},m=(e,t,r)=>{var n=p((t,r)=>{if(g(r)){var[n,i]=e(r.from,r.to,r.velocity);return y(y({},r),{},{from:n,velocity:i})}return r},t);return r<1?p((e,t)=>g(t)?y(y({},t),{},{velocity:v(t.velocity,n[e].velocity,r),from:v(t.from,n[e].from,r)}):t,t):m(e,n,r-1)};class b{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var x=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){P(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function P(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class E extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:c}=this.state;if(r){if(!t){this.state&&c&&(n&&c[n]!==o||!n&&c!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=u||i?l:e.to;this.state&&c&&(n&&c[n]!==s||!n&&c!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(j(j({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var t,r,n,i,a,o,l,c,f,d,h,b,x,w,O,j,P,E,A,M,S,k,_,T,C,{from:D,to:N,duration:I,easing:z,begin:L,onAnimationEnd:R,onAnimationStart:$}=e,B=(k=(e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return u(e);case"spring":return s();default:if("cubic-bezier"===e.split("(")[0])return u(e)}return"function"==typeof e?e:null})(z),_=this.changeStyle,T=this.manager.getTimeoutController(),C=[Object.keys(D),Object.keys(N)].reduce((e,t)=>e.filter(e=>t.includes(e))),!0===k.isStepper?(t=D,r=N,n=k,i=C,a=_,o=T,c=i.reduce((e,n)=>y(y({},e),{},{[n]:{from:t[n],velocity:0,to:r[n]}}),{}),f=null,d=e=>{l||(l=e);var i=(e-l)/n.dt;c=m(n,c,i),a(y(y(y({},t),r),p((e,t)=>t.from,c))),l=e,Object.values(c).filter(g).length&&(f=o.setTimeout(d))},()=>(f=o.setTimeout(d),()=>{f()})):(h=D,b=N,x=k,w=I,O=C,j=_,P=T,A=null,M=O.reduce((e,t)=>y(y({},e),{},{[t]:[h[t],b[t]]}),{}),S=e=>{E||(E=e);var t=(e-E)/w,r=p((e,r)=>v(...r,x(t)),M);if(j(y(y(y({},h),b),r)),t<1)A=P.setTimeout(S);else{var n=p((e,t)=>v(...t,x(1)),M);j(y(y(y({},h),b),n))}},()=>(A=P.setTimeout(S),()=>{A()}))),U=()=>{this.stopJSAnimation=B()};this.manager.start([$,L,U,I,R])}runAnimation(e){let t;var{begin:r,duration:n,attributeName:i,to:a,easing:o,onAnimationStart:l,onAnimationEnd:c,children:u}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof o||"function"==typeof u||"spring"===o)return void this.runJSAnimation(e);var s=i?{[i]:a}:a,f=(t=Object.keys(s),t.map(e=>"".concat(e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase()))," ").concat(n,"ms ").concat(o)).join(","));this.manager.start([l,r,j(j({},s),{},{transition:f}),n,c])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:c,to:u,canBegin:s,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:p,animationManager:h}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,x),v=n.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!l||0===v||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,j(j({},y),{},{style:j(j({},t),g),className:r}))};return 1===v?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}constructor(e,t){super(e,t),P(this,"mounted",!1),P(this,"manager",null),P(this,"stopJSAnimation",null),P(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}P(E,"displayName","Animate"),P(E,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var A=(0,n.createContext)(null);function M(e){var t,r,i,a,o,l,c,u=(0,n.useContext)(A);return n.createElement(E,w({},e,{animationManager:null!=(l=null!=(c=e.animationManager)?c:u)?l:(t=new b,r=()=>null,i=!1,a=null,o=e=>{if(!i){if(Array.isArray(e)){if(!e.length)return;var[n,...l]=e;if("number"==typeof n){a=t.setTimeout(o.bind(null,l),n);return}o(n),a=t.setTimeout(o.bind(null,l));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{i=!0},start:e=>{i=!1,a&&(a(),a=null),o(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}},4487:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(5710).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},4517:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(6200),a=r(7298),o=r(921),l=r(3205);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},4532:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>u,a6:()=>s,h4:()=>G,jM:()=>H,ss:()=>F});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(e){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var c=Object.getPrototypeOf;function u(e){return!!e&&!!e[o]}function s(e){return!!e&&(d(e)||Array.isArray(e)||!!e[a]||!!e.constructor?.[a]||g(e)||m(e))}var f=Object.prototype.constructor.toString();function d(e){if(!e||"object"!=typeof e)return!1;let t=c(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function p(e,t){0===h(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function h(e){let t=e[o];return t?t.type_:Array.isArray(e)?1:g(e)?2:3*!!m(e)}function y(e,t){return 2===h(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function v(e,t,r){let n=h(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function g(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function x(e,t){if(g(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=d(e);if(!0!==t&&("class_only"!==t||r)){let t=c(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[o];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(c(e),t)}}function w(e,t=!1){return j(e)||u(e)||!s(e)||(h(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>w(t,!0))),e}function O(){l(2)}function j(e){return Object.isFrozen(e)}var P={};function E(e){let t=P[e];return t||l(0,e),t}function A(e,t){t&&(E("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function M(e){S(e),e.drafts_.forEach(_),e.drafts_=null}function S(e){e===n&&(n=e.parent_)}function k(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function _(e){let t=e[o];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[o].modified_&&(M(t),l(4)),s(e)&&(e=C(t,e),t.parent_||N(t,e)),t.patches_&&E("Patches").generateReplacementPatches_(r[o].base_,e,t.patches_,t.inversePatches_)):e=C(t,r,[]),M(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function C(e,t,r){if(j(t))return t;let n=t[o];if(!n)return p(t,(i,a)=>D(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return N(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),p(i,(i,o)=>D(e,n,t,i,o,r,a)),N(e,t,!1),r&&e.patches_&&E("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function D(e,t,r,n,i,a,o){if(u(i)){let o=C(e,i,a&&t&&3!==t.type_&&!y(t.assigned_,n)?a.concat(n):void 0);if(v(r,n,o),!u(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(s(i)&&!j(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;C(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(e,i)}}function N(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&w(t,r)}var I={get(e,t){if(t===o)return e;let r=b(e);if(!y(r,t)){var n=e,i=r,a=t;let o=R(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!s(l)?l:l===L(e.base_,t)?(B(e),e.copy_[t]=U(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=R(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=L(b(e),t),i=n?.[o];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(e.base_,t)))return!0;B(e),$(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==L(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,B(e),$(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>c(e.base_),setPrototypeOf(){l(12)}},z={};function L(e,t){let r=e[o];return(r?b(r):e)[t]}function R(e,t){if(!(t in e))return;let r=c(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=c(r)}}function $(e){!e.modified_&&(e.modified_=!0,e.parent_&&$(e.parent_))}function B(e){e.copy_||(e.copy_=x(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function U(e,t){let r=g(e)?E("MapSet").proxyMap_(e,t):m(e)?E("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=I;r&&(a=[i],o=z);let{revoke:l,proxy:c}=Proxy.revocable(a,o);return i.draft_=c,i.revoke_=l,c}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function F(e){return u(e)||l(10,e),function e(t){let r;if(!s(t)||j(t))return t;let n=t[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=x(t,n.scope_.immer_.useStrictShallowCopy_)}else r=x(t,!0);return p(r,(t,n)=>{v(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}p(I,(e,t)=>{z[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),z.deleteProperty=function(e,t){return z.set.call(this,e,t,void 0)},z.set=function(e,t,r){return I.set.call(this,e[0],t,r,e[0])};var K=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),s(e)){let i=k(this),a=U(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?M(i):S(i)}return A(i,r),T(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&w(n,!0),r){let t=[],i=[];E("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){s(e)||l(8),u(e)&&(e=F(e));let t=k(this),r=U(e,void 0);return r[o].isManual_=!0,S(t),r}finishDraft(e,t){let r=e&&e[o];r&&r.isManual_||l(9);let{scope_:n}=r;return A(n,t),T(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=E("Patches").applyPatches_;return u(e)?n(e,t):this.produce(e,e=>n(e,t))}},H=K.produce;function G(e){return e}K.produceWithPatches.bind(K),K.setAutoFreeze.bind(K),K.setUseStrictShallowCopy.bind(K),K.applyPatches.bind(K),K.createDraft.bind(K),K.finishDraft.bind(K)},4538:(e,t,r)=>{"use strict";r.d(t,{M:()=>f});var n=r(2115),i=r(2596),a=r(788),o=r(3389),l=r(4460);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,u=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(u,",").concat(e+c*s[0],",").concat(t)),a+="L ".concat(e+r-c*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(u,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(u,",\n        ").concat(e+r-c*s[2],",").concat(t+n)),a+="L ".concat(e+c*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(u,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(u,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,o.e)(e,s),r=(0,n.useRef)(null),[f,d]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&d(e)}catch(e){}},[]);var{x:p,y:h,width:y,height:v,radius:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isAnimationActive:O,isUpdateAnimationActive:j}=t;if(p!==+p||h!==+h||y!==+y||v!==+v||0===y||0===v)return null;var P=(0,i.$)("recharts-rectangle",m);return j?n.createElement(l.i,{canBegin:f>0,from:{width:y,height:v,x:p,y:h},to:{width:y,height:v,x:p,y:h},duration:x,animationEasing:b,isActive:j},e=>{var{width:i,height:o,x:s,y:d}=e;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,isActive:O,easing:b},n.createElement("path",c({},(0,a.J9)(t,!0),{className:P,d:u(s,d,i,o,g),ref:r})))}):n.createElement("path",c({},(0,a.J9)(t,!0),{className:P,d:u(p,h,y,v,g)}))}},4545:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},4664:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2694);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},4685:(e,t,r)=>{"use strict";r.d(t,{YF:()=>u,dj:()=>s,fP:()=>f,ky:()=>c});var n=r(5710),i=r(4890),a=r(3027),o=r(6523),l=r(1165),c=(0,n.VP)("mouseClick"),u=(0,n.Nc)();u.startListening({actionCreator:c,effect:(e,t)=>{var r=e.payload,n=(0,a.g)(t.getState(),(0,l.w)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,i.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.VP)("mouseMove"),f=(0,n.Nc)();f.startListening({actionCreator:s,effect:(e,t)=>{var r=e.payload,n=t.getState(),c=(0,o.au)(n,n.tooltip.settings.shared),u=(0,a.g)(n,(0,l.w)(r));"axis"===c&&((null==u?void 0:u.activeIndex)!=null?t.dispatch((0,i.Nt)({activeIndex:u.activeIndex,activeDataKey:void 0,activeCoordinate:u.activeCoordinate})):t.dispatch((0,i.xS)()))}})},4732:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>D,aX:()=>z,dS:()=>C,dp:()=>k,fW:()=>O,pg:()=>T,r1:()=>A,u9:()=>N,yn:()=>I});var n=r(8924),i=r(241),a=r.n(i),o=r(1971),l=r(9827),c=r(356),u=r(215),s=r(8478),f=r(7238),d=r(8573),p=r(2589),h=r(530),y=r(1928),v=r(841),g=r(4968),m=r(5146),b=r(6670),x=r(5714),w=r(4013),O=()=>(0,o.G)(s.iO),j=(e,t)=>t,P=(e,t,r)=>r,E=(e,t,r,n)=>n,A=(0,n.Mz)(u.R4,e=>a()(e,e=>e.coordinate)),M=(0,n.Mz)([x.J,j,P,E],y.i),S=(0,n.Mz)([M,u.n4],v.P),k=(e,t,r)=>{if(null!=t){var n=(0,x.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},_=(0,n.Mz)([x.J,j,P,E],m.q),T=(0,n.Mz)([p.Lp,p.A$,f.fz,d.HZ,u.R4,E,_,b.x],g.o),C=(0,n.Mz)([M,T],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),D=(0,n.Mz)(u.R4,S,h.E),N=(0,n.Mz)([_,S,c.LF,u.Dn,D,b.x,j],w.N),I=(0,n.Mz)([M],e=>({isActive:e.active,activeIndex:e.index})),z=(e,t,r,n,i,a,o,c)=>{if(e&&t&&n&&i&&a){var u=(0,l.r4)(e.chartX,e.chartY,t,r,c);if(u){var s=(0,l.SW)(u,t),f=(0,l.gH)(s,o,a,n,i),d=(0,l.bk)(t,a,f,u);return{activeIndex:String(f),activeCoordinate:d}}}}},4754:(e,t,r)=>{"use strict";r.d(t,{d:()=>C});var n=r(2115),i=r(675),a=r(6377),o=r(788),l=r(9827),c=r(9035),u=r(9584),s=r(7238),f=r(4299),d=r(1971),p=r(1807),h=r(3389),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:c}=e;return n.createElement("rect",{x:i,y:a,ry:c,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function P(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:l,y2:c,key:u}=t,s=O(t,y),f=(0,o.J9)(s,!1),{offset:d}=f,p=O(f,v);r=n.createElement("line",w({},p,{x1:i,y1:a,x2:l,y2:c,fill:"none",key:u}))}return r}function E(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=O(e,g),u=a.map((e,n)=>P(i,x(x({},c),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function A(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=O(e,m),u=a.map((e,n)=>P(i,x(x({},c),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function M(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:c,horizontal:u=!0}=e;if(!u||!t||!t.length)return null;var s=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,c)=>{var u=s[c+1]?s[c+1]-e:a+l-e;if(u<=0)return null;var f=c%t.length;return n.createElement("rect",{key:"react-".concat(c),y:e,x:i,height:u,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function S(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:c,verticalPoints:u}=e;if(!t||!r||!r.length)return null;var s=u.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var u=s[t+1]?s[t+1]-e:a+l-e;if(u<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:u,height:c,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var k=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,c.f)(x(x(x({},u.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},_=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,c.f)(x(x(x({},u.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function C(e){var t=(0,s.yi)(),r=(0,s.rY)(),o=(0,s.W7)(),l=x(x({},(0,h.e)(e,T)),{},{x:(0,a.Et)(e.x)?e.x:o.left,y:(0,a.Et)(e.y)?e.y:o.top,width:(0,a.Et)(e.width)?e.width:o.width,height:(0,a.Et)(e.height)?e.height:o.height}),{xAxisId:c,yAxisId:u,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:P}=l,C=(0,p.r)(),D=(0,d.G)(e=>(0,f.ZB)(e,"xAxis",c,C)),N=(0,d.G)(e=>(0,f.ZB)(e,"yAxis",u,C));if(!(0,a.Et)(g)||g<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(v)||v!==+v)return null;var I=l.verticalCoordinatesGenerator||k,z=l.horizontalCoordinatesGenerator||_,{horizontalPoints:L,verticalPoints:R}=l;if((!L||!L.length)&&"function"==typeof z){var $=O&&O.length,B=z({yAxis:N?x(x({},N),{},{ticks:$?O:N.ticks}):void 0,width:t,height:r,offset:o},!!$||b);(0,i.R)(Array.isArray(B),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof B,"]")),Array.isArray(B)&&(L=B)}if((!R||!R.length)&&"function"==typeof I){var U=P&&P.length,F=I({xAxis:D?x(x({},D),{},{ticks:U?P:D.ticks}):void 0,width:t,height:r,offset:o},!!U||b);(0,i.R)(Array.isArray(F),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&(R=F)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(j,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(M,w({},l,{horizontalPoints:L})),n.createElement(S,w({},l,{verticalPoints:R})),n.createElement(E,w({},l,{offset:o,horizontalPoints:L,xAxis:D,yAxis:N})),n.createElement(A,w({},l,{offset:o,verticalPoints:R,xAxis:D,yAxis:N})))}C.displayName="CartesianGrid"},4804:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8132),i=r(2429);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},4811:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},4890:(e,t,r)=>{"use strict";r.d(t,{E1:()=>v,En:()=>m,Ix:()=>l,ML:()=>p,Nt:()=>h,RD:()=>s,UF:()=>u,XB:()=>c,jF:()=>y,k_:()=>a,o4:()=>g,oP:()=>f,xS:()=>d});var n=r(5710),i=r(4532),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:c,setTooltipSettingsState:u,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:d,setActiveClickItemIndex:p,setMouseOverAxisIndex:h,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=o.actions,m=o.reducer},4968:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],u=null==c?void 0:l(c.positions,a);if(null!=u)return u;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}}},4986:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},4993:(e,t,r)=>{"use strict";var n=r(2115);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},5064:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(2115).createContext)(null)},5115:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>a});var n=r(2115),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},5146:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},5160:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},5181:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},5252:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2520),i=r(2767);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},5306:(e,t,r)=>{"use strict";r.d(t,{CA:()=>y,MC:()=>u,QG:()=>h,Vi:()=>c,cU:()=>s,fR:()=>f});var n=r(5710),i=r(4532);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=o(o({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:c,removeXAxis:u,addYAxis:s,removeYAxis:f,addZAxis:d,removeZAxis:p,updateYAxisWidth:h}=l.actions,y=l.reducer},5641:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>o,Kg:()=>a,lY:()=>l,yy:()=>c}),r(2115);var a=Math.PI/180,o=(e,t,r,n)=>({x:e+Math.cos(-a*n)*r,y:t+Math.sin(-a*n)*r}),l=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},c=(e,t)=>{var r,{x:n,y:a}=e,{radius:o,angle:l}=((e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)})({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-i)/o);return n>a&&(l=2*Math.PI-l),{radius:o,angle:180*l/Math.PI,angleInRadian:l}})({x:n,y:a},t),{innerRadius:c,outerRadius:u}=t;if(o<c||o>u||0===o)return null;var{startAngle:s,endAngle:f}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}})(t),d=l;if(s<=f){for(;d>f;)d-=360;for(;d<s;)d+=360;r=d>=s&&d<=f}else{for(;d>s;)d-=360;for(;d<f;)d+=360;r=d>=f&&d<=s}return r?i(i({},t),{},{radius:o,angle:((e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))})(d,t)}):null}},5643:(e,t,r)=>{"use strict";e.exports=r(6115)},5654:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},5672:(e,t,r)=>{e.exports=r(921).get},5710:(e,t,r)=>{"use strict";r.d(t,{U1:()=>p,VP:()=>c,Nc:()=>q,Z0:()=>g});var n=r(52);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var a=i(),o=r(4532),l=(r(9509),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});function c(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(Y(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var u=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function s(e){return(0,o.a6)(e)?(0,o.jM)(e,()=>{}):e}function f(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var d=e=>t=>{setTimeout(t,e)};function p(e){let t,r,o,c=function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},l=new u;return t&&("boolean"==typeof t?l.push(a):l.push(i(t.extraArgument))),l},{reducer:s,middleware:f,devTools:p=!0,duplicateMiddlewareCheck:h=!0,preloadedState:y,enhancers:v}=e||{};if("function"==typeof s)t=s;else if((0,n.Qd)(s))t=(0,n.HY)(s);else throw Error(Y(1));r="function"==typeof f?f(c):c();let g=n.Zz;p&&(g=l({trace:!1,..."object"==typeof p&&p}));let m=(o=(0,n.Tw)(...r),function(e){let{autoBatch:t=!0}=e??{},r=new u(o);return t&&r.push(((e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:d(10):"callback"===e.type?e.queueNotification:d(e.timeout),u=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,c(u)),n.dispatch(e)}finally{i=!0}}})})("object"==typeof t?t:void 0)),r}),b=g(..."function"==typeof v?v(m):m());return(0,n.y$)(t,y,b)}function h(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(Y(28));if(n in r)throw Error(Y(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var y=Symbol.for("rtk-slice-createasyncthunk"),v=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(v||{}),g=function({creators:e}={}){let t=e?.asyncThunk?.[y];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(Y(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(a),u={},d={},p={},y=[],v={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(Y(12));if(r in d)throw Error(Y(13));return d[r]=t,v},addMatcher:(e,t)=>(y.push({matcher:e,reducer:t}),v),exposeAction:(e,t)=>(p[e]=t,v),exposeCaseReducer:(e,t)=>(u[e]=t,v)};function g(){let[t={},r=[],n]="function"==typeof e.extraReducers?h(e.extraReducers):[e.extraReducers],i={...t,...d};return function(e,t){let r,[n,i,a]=h(t);if("function"==typeof e)r=()=>s(e());else{let t=s(e);r=()=>t}function l(e=r(),t){let c=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===c.filter(e=>!!e).length&&(c=[a]),c.reduce((e,r)=>{if(r)if((0,o.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,o.a6)(e))return(0,o.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of y)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(Y(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:c,settled:u,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),c&&n.addCase(f.rejected,c),u&&n.addMatcher(f.settled,u),n.exposeCaseReducer(t,{fulfilled:o||m,pending:l||m,rejected:c||m,settled:u||m})}(o,i,v,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(Y(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?c(e,o):c(e))}(o,i,v)});let b=e=>e,x=new Map,w=new WeakMap;function O(e,t){return r||(r=g()),r(e,t)}function j(){return r||(r=g()),r.getInitialState()}function P(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=f(w,n,j)),i}function i(t=b){let n=f(x,r,()=>new WeakMap);return f(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>f(w,t,j),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let E={name:n,reducer:O,actions:p,caseReducers:u,getInitialState:j,...P(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:O},r),{...E,...P(n,!0)}}};return E}}();function m(){}var b="listener",x="completed",w="cancelled",O=`task-${w}`,j=`task-${x}`,P=`${b}-${w}`,E=`${b}-${x}`,A=class{constructor(e){this.code=e,this.message=`task ${w} (reason: ${e})`}name="TaskAbortError";message},M=(e,t)=>{if("function"!=typeof e)throw TypeError(Y(32))},S=()=>{},k=(e,t=S)=>(e.catch(t),e),_=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),T=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},C=e=>{if(e.aborted){let{reason:t}=e;throw new A(t)}};function D(e,t){let r=S;return new Promise((n,i)=>{let a=()=>i(new A(e.reason));if(e.aborted)return void a();r=_(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=S})}var N=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof A?"cancelled":"rejected",error:e}}finally{t?.()}},I=e=>t=>k(D(e,t).then(t=>(C(e),t))),z=e=>{let t=I(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:L}=Object,R={},$="listenerMiddleware",B=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=c(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(Y(21));return M(a,"options.listener"),{predicate:i,type:t,effect:a}},U=L(e=>{let{type:t,predicate:r,effect:n}=B(e);return{id:((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(Y(22))}}},{withTypes:()=>U}),F=(e,t)=>{let{type:r,effect:n,predicate:i}=B(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},K=e=>{e.pending.forEach(e=>{T(e,P)})},H=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},G=L(c(`${$}/add`),{withTypes:()=>G}),Z=c(`${$}/removeAll`),W=L(c(`${$}/remove`),{withTypes:()=>W}),V=(...e)=>{console.error(`${$}/error`,...e)},q=(e={})=>{let t=new Map,{extra:r,onError:i=V}=e;M(i,"onError");let a=e=>(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&K(e)}))(F(t,e)??U(e));L(a,{withTypes:()=>a});let o=e=>{let r=F(t,e);return r&&(r.unsubscribe(),e.cancelActive&&K(r)),!!r};L(o,{withTypes:()=>o});let l=async(e,n,o,l)=>{let c=new AbortController,u=((e,t)=>{let r=async(r,n)=>{C(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await D(t,Promise.race(a));return C(t),e}finally{i()}};return(e,t)=>k(r(e,t))})(a,c.signal),s=[];try{e.pending.add(c),await Promise.resolve(e.effect(n,L({},o,{getOriginalState:l,condition:(e,t)=>u(e,t).then(Boolean),take:u,delay:z(c.signal),pause:I(c.signal),extra:r,signal:c.signal,fork:((e,t)=>(r,n)=>{M(r,"taskExecutor");let i=new AbortController;_(e,()=>T(i,e.reason));let a=N(async()=>{C(e),C(i.signal);let t=await r({pause:I(i.signal),delay:z(i.signal),signal:i.signal});return C(i.signal),t},()=>T(i,j));return n?.autoJoin&&t.push(a.catch(S)),{result:I(e)(a),cancel(){T(i,O)}}})(c.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==c&&(T(e,P),r.delete(e))})},cancel:()=>{T(c,P),e.pending.delete(c)},throwIfCancelled:()=>{C(c.signal)}})))}catch(e){e instanceof A||H(i,e,{raisedBy:"effect"})}finally{await Promise.all(s),T(c,E),e.pending.delete(c)}},c=(e=>()=>{e.forEach(K),e.clear()})(t);return{middleware:e=>r=>u=>{let s;if(!(0,n.ve)(u))return r(u);if(G.match(u))return a(u.payload);if(Z.match(u))return void c();if(W.match(u))return o(u.payload);let f=e.getState(),d=()=>{if(f===R)throw Error(Y(23));return f};try{if(s=r(u),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(u,r,f)}catch(e){t=!1,H(i,e,{raisedBy:"predicate"})}t&&l(n,u,e,d)}}}finally{f=R}return s},startListening:a,stopListening:o,clearListeners:c}};function Y(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original")},5713:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(2115);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},a),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},5714:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},5932:(e,t,r)=>{"use strict";r.d(t,{s:()=>l});var n=r(2115),i=r(1807),a=r(8627),o=r(1971);function l(e){var{layout:t,width:r,height:l,margin:c}=e,u=(0,o.j)(),s=(0,i.r)();return(0,n.useEffect)(()=>{s||(u((0,a.JK)(t)),u((0,a.gX)({width:r,height:l})),u((0,a.B_)(c)))},[u,s,t,r,l,c]),null}},5998:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},6006:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},6025:(e,t,r)=>{"use strict";r.d(t,{W:()=>b});var n=r(2115),i=r(2596),a=r(9584),o=r(1971),l=r(5306),c=r(4299),u=r(8573),s=r(1807),f=["children"],d=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function v(e){var t=(0,o.j)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return y(e,f)},[e]),i=(0,o.G)(e=>(0,c.Rl)(e,r.id)),a=r===i;return((0,n.useEffect)(()=>(t((0,l.Vi)(r)),()=>{t((0,l.MC)(r))}),[r,t]),a)?e.children:null}var g=e=>{var{xAxisId:t,className:r}=e,l=(0,o.G)(u.c2),f=(0,s.r)(),p="xAxis",v=(0,o.G)(e=>(0,c.iV)(e,p,t,f)),g=(0,o.G)(e=>(0,c.Zi)(e,p,t,f)),m=(0,o.G)(e=>(0,c.Lw)(e,t)),b=(0,o.G)(e=>(0,c.L$)(e,t));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:x,ticks:w}=e,O=y(e,d);return n.createElement(a.u,h({},O,{scale:v,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.$)("recharts-".concat(p," ").concat(p),r),viewBox:l,ticks:g}))},m=e=>{var t,r,i,a,o;return n.createElement(v,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter},n.createElement(g,e))};class b extends n.Component{render(){return n.createElement(m,this.props)}}p(b,"displayName","XAxis"),p(b,"defaultProps",{allowDataOverflow:c.PU.allowDataOverflow,allowDecimals:c.PU.allowDecimals,allowDuplicatedCategory:c.PU.allowDuplicatedCategory,height:c.PU.height,hide:!1,mirror:c.PU.mirror,orientation:c.PU.orientation,padding:c.PU.padding,reversed:c.PU.reversed,scale:c.PU.scale,tickCount:c.PU.tickCount,type:c.PU.type,xAxisId:0})},6038:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var n=(e,t)=>t},6091:(e,t,r)=>{"use strict";r.d(t,{r:()=>l});var n=r(2115),i=r(1971),a=r(4890),o=r(1807);function l(e){var{fn:t,args:r}=e,l=(0,i.j)(),c=(0,o.r)();return(0,n.useEffect)(()=>{if(!c){var e=t(r);return l((0,a.Ix)(e)),()=>{l((0,a.XB)(e))}}},[t,r,l,c]),null}},6115:(e,t,r)=>{"use strict";var n=r(2115),i=r(9033),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,u=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=o(e,(f=u(function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,i]))[0],f[1]);return c(function(){d.hasValue=!0,d.value=p},[p]),s(p),p}},6144:(e,t,r)=>{"use strict";r.d(t,{e:()=>h,k:()=>y});var n=r(5710),i=r(4890),a=r(3027),o=r(1165),l=r(6523),c=r(4421),u=r(8924),s=r(6670),f=r(5714),d=(0,u.Mz)([f.J],e=>e.tooltipItemPayloads),p=(0,u.Mz)([d,s.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),h=(0,n.VP)("touchMove"),y=(0,n.Nc)();y.startListening({actionCreator:h,effect:(e,t)=>{var r=e.payload,n=t.getState(),u=(0,l.au)(n,n.tooltip.settings.shared);if("axis"===u){var s=(0,a.g)(n,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&t.dispatch((0,i.Nt)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===u){var f,d=r.touches[0],h=document.elementFromPoint(d.clientX,d.clientY);if(!h||!h.getAttribute)return;var y=h.getAttribute(c.F0),v=null!=(f=h.getAttribute(c.um))?f:void 0,g=p(t.getState(),y,v);t.dispatch((0,i.RD)({activeDataKey:v,activeIndex:y,activeCoordinate:g}))}}})},6200:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},6377:(e,t,r)=>{"use strict";r.d(t,{CG:()=>p,Dj:()=>h,Et:()=>c,F4:()=>d,M8:()=>o,NF:()=>f,Zb:()=>g,_3:()=>l,eP:()=>y,sA:()=>a,uy:()=>v,vh:()=>u});var n=r(5672),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,c=e=>("number"==typeof e||e instanceof Number)&&!o(e),u=e=>c(e)||"string"==typeof e,s=0,f=e=>{var t=++s;return"".concat(e||"").concat(t)},d=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!c(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},p=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},h=(e,t)=>c(e)&&c(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var v=e=>null==e,g=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},6523:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>a,Td:()=>c,au:()=>l,xH:()=>i});var n=r(1971),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return o(t,i(e),a(e))}function c(e){return(0,n.G)(t=>l(t,e))}},6605:(e,t,r)=>{"use strict";r.d(t,{P:()=>u});var n=r(1643);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},c="recharts_measurement_span",u=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(t=a({},r)).forEach(e=>{t[e]||delete t[e]}),t),u=JSON.stringify({text:e,copyStyle:i});if(o.widthCache[u])return o.widthCache[u];try{var s=document.getElementById(c);s||((s=document.createElement("span")).setAttribute("id",c),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},l),i);Object.assign(s.style,f),s.textContent="".concat(e);var d=s.getBoundingClientRect(),p={width:d.width,height:d.height};return o.widthCache[u]=p,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),p}catch(e){return{width:0,height:0}}}},6633:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},6641:(e,t,r)=>{"use strict";r.d(t,{dl:()=>c,lJ:()=>l,uN:()=>a});var n=r(5710),i=r(6377);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:c}=o.actions},6670:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},6690:(e,t,r)=>{"use strict";r.d(t,{y:()=>eb,L:()=>em});var n=r(2115),i=r(2596),a=r(2348),o=r(4011),l=r(4811),c=r(8460),u=r(6377),s=r(788),f=r(1643),d=r(9827),p=r(3597),h=r(8605),y=["x","y"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),c=parseInt("".concat(t.width||i.width),10);return m(m(m(m(m({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:c,name:t.name,radius:t.radius})}function x(e){return n.createElement(h.y,v({shapeType:"rectangle",propTransformer:b,activeClassName:"recharts-active-bar"},e))}var w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,u.Et)(e))return e;var i=(0,u.Et)(r)||(0,u.uy)(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},O=r(9129),j=r(6091),P=r(1971),E=r(2248),A=()=>{var e=(0,P.j)();return(0,n.useEffect)(()=>(e((0,E.lm)()),()=>{e((0,E.Ch)())})),null},M=r(9226),S=r(7195),k=r(7238),_=r(8924),T=r(4299),C=r(356),D=r(8573),N=r(8478),I=r(8892);function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var R=(e,t,r,n,i)=>i,$=(e,t,r)=>{var n=null!=r?r:e;if(!(0,u.uy)(n))return(0,u.F4)(n,t,0)},B=(0,_.Mz)([k.fz,T.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function U(e){return null!=e.stackId&&null!=e.dataKey}var F=(0,_.Mz)([B,N.x3,(e,t,r)=>"horizontal"===(0,k.fz)(e)?(0,T.BQ)(e,"xAxis",t):(0,T.BQ)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(U),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:$(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:$(t,r,e.barSize)}))]}),K=(e,t,r,n)=>{var i,a;return"horizontal"===(0,k.fz)(e)?(i=(0,T.Gx)(e,"xAxis",t,n),a=(0,T.CR)(e,"xAxis",t,n)):(i=(0,T.Gx)(e,"yAxis",r,n),a=(0,T.CR)(e,"yAxis",r,n)),(0,d.Hj)(i,a)},H=(0,_.Mz)([F,N.JN,N._5,N.gY,(e,t,r,n,i)=>{var a,o,l,c,s=(0,k.fz)(e),f=(0,N.JN)(e),{maxBarSize:p}=i,h=(0,u.uy)(p)?f:p;return"horizontal"===s?(l=(0,T.Gx)(e,"xAxis",t,n),c=(0,T.CR)(e,"xAxis",t,n)):(l=(0,T.Gx)(e,"yAxis",r,n),c=(0,T.CR)(e,"yAxis",r,n)),null!=(a=null!=(o=(0,d.Hj)(l,c,!0))?o:h)?a:0},K,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=(0,u.F4)(e,r,0,!0),c=[];if((0,I.H)(n[0].barSize)){var s=!1,f=r/o,d=n.reduce((e,t)=>e+(t.barSize||0),0);(d+=(o-1)*l)>=r&&(d-=(o-1)*l,l=0),d>=r&&f>0&&(s=!0,f*=.9,d=o*f);var p={offset:((r-d)/2|0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p.offset+p.size+l,size:s?f:null!=(r=t.barSize)?r:0}}];return p=n[n.length-1].position,n},c)}else{var h=(0,u.F4)(t,r,0,!0);r-2*h-(o-1)*l<=0&&(l=0);var y=(r-2*h-(o-1)*l)/o;y>1&&(y>>=0);var v=(0,I.H)(i)?Math.min(y,i):y;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h+(y+l)*r+(y-v)/2,size:v}}],c)}return a}}(r,n,i!==a?i:a,e,(0,u.uy)(o)?t:o);return i!==a&&null!=l&&(l=l.map(e=>L(L({},e),{},{position:L(L({},e.position),{},{offset:e.position.offset-i/2})}))),l}),G=(0,_.Mz)([H,R],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),Z=(0,_.Mz)([T.ld,R],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),W=(0,_.Mz)([(e,t,r,n)=>"horizontal"===(0,k.fz)(e)?(0,T.TC)(e,"yAxis",r,n):(0,T.TC)(e,"xAxis",t,n),R],(e,t)=>{if(!e||(null==t?void 0:t.dataKey)==null)return;var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}),V=(0,_.Mz)([D.HZ,(e,t,r,n)=>(0,T.Gx)(e,"xAxis",t,n),(e,t,r,n)=>(0,T.Gx)(e,"yAxis",r,n),(e,t,r,n)=>(0,T.CR)(e,"xAxis",t,n),(e,t,r,n)=>(0,T.CR)(e,"yAxis",r,n),G,k.fz,C.HS,K,W,Z,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,c,u,s,f)=>{var d,{chartData:p,dataStartIndex:h,dataEndIndex:y}=l;if(null!=s&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var{data:v}=s;if(null!=(d=null!=v&&v.length>0?v:null==p?void 0:p.slice(h,y+1)))return em({layout:o,barSettings:s,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:u,displayedData:d,offset:e,cells:f})}}),q=r(1807),Y=r(215),J=r(9020),X=r(9426),Q=r(3389),ee=r(4460),et=["onMouseEnter","onMouseLeave","onClick"],er=["value","background","tooltipPosition"],en=["onMouseEnter","onClick","onMouseLeave"];function ei(){return(ei=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ea(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eo(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ea(Object(r),!0).forEach(function(t){el(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ea(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function el(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ec(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function eu(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,d.uM)(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function es(e){var t=(0,P.G)(Y.A2),{data:r,dataKey:i,background:a,allOtherBarProps:o}=e,{onMouseEnter:l,onMouseLeave:c,onClick:u}=o,f=ec(o,et),d=(0,O.Cj)(l,i),h=(0,O.Pg)(c),y=(0,O.Ub)(u,i);if(!a||null==r)return null;var v=(0,s.J9)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:c}=e,u=ec(e,er);if(!l)return null;var s=d(e,r),g=h(e,r),m=y(e,r),b=eo(eo(eo(eo(eo({option:a,isActive:String(r)===t},u),{},{fill:"#eee"},l),v),(0,p.XC)(f,e,r)),{},{onMouseEnter:s,onMouseLeave:g,onClick:m,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(x,ei({key:"background-bar-".concat(r)},b))}))}function ef(e){var{data:t,props:r,showLabels:i}=e,o=(0,s.J9)(r,!1),{shape:l,dataKey:u,activeBar:f}=r,d=(0,P.G)(Y.A2),h=(0,P.G)(Y.Xb),{onMouseEnter:y,onClick:v,onMouseLeave:g}=r,m=ec(r,en),b=(0,O.Cj)(y,u),w=(0,O.Pg)(g),j=(0,O.Ub)(v,u);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=f&&String(t)===d&&(null==h||u===h),i=eo(eo(eo({},o),e),{},{isActive:r,option:r?f:l,index:t,dataKey:u});return n.createElement(a.W,ei({className:"recharts-bar-rectangle"},(0,p.XC)(m,e,t),{onMouseEnter:b(e,t),onMouseLeave:w(e,t),onClick:j(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(x,i))}),i&&c.Z.renderCallByParent(r,t)):null}function ed(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:o,isAnimationActive:l,animationBegin:c,animationDuration:s,animationEasing:f,onAnimationEnd:d,onAnimationStart:p}=t,h=r.current,y=(0,X.n)(t,"recharts-bar-"),[v,g]=(0,n.useState)(!1),m=(0,n.useCallback)(()=>{"function"==typeof d&&d(),g(!1)},[d]),b=(0,n.useCallback)(()=>{"function"==typeof p&&p(),g(!0)},[p]);return n.createElement(ee.i,{begin:c,duration:s,isActive:l,easing:f,from:{t:0},to:{t:1},onAnimationEnd:m,onAnimationStart:b,key:y},e=>{var{t:l}=e,c=1===l?i:i.map((e,t)=>{var r=h&&h[t];if(r){var n=(0,u.Dj)(r.x,e.x),i=(0,u.Dj)(r.y,e.y),a=(0,u.Dj)(r.width,e.width),c=(0,u.Dj)(r.height,e.height);return eo(eo({},e),{},{x:n(l),y:i(l),width:a(l),height:c(l)})}if("horizontal"===o){var s=(0,u.Dj)(0,e.height)(l);return eo(eo({},e),{},{y:e.y+e.height-s,height:s})}var f=(0,u.Dj)(0,e.width)(l);return eo(eo({},e),{},{width:f})});return l>0&&(r.current=c),n.createElement(a.W,null,n.createElement(ef,{props:t,data:c,showLabels:!v}))})}function ep(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(ed,{previousRectanglesRef:i,props:e}):n.createElement(ef,{props:e,data:t,showLabels:!0})}var eh=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,d.kr)(e,t)}};class ey extends n.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:l,xAxisId:c,yAxisId:s,needClip:f,background:d,id:p,layout:h}=this.props;if(e)return null;var y=(0,i.$)("recharts-bar",l),v=(0,u.uy)(p)?this.id:p;return n.createElement(a.W,{className:y},f&&n.createElement("defs",null,n.createElement(S.Q,{clipPathId:v,xAxisId:c,yAxisId:s})),n.createElement(a.W,{className:"recharts-bar-rectangles",clipPath:f?"url(#clipPath-".concat(v,")"):null},n.createElement(es,{data:t,dataKey:r,background:d,allOtherBarProps:this.props}),n.createElement(ep,this.props)),n.createElement(o._,{direction:"horizontal"===h?"y":"x"},this.props.children))}constructor(){super(...arguments),el(this,"id",(0,u.NF)("recharts-bar-"))}}var ev={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!f.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function eg(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:o,minPointSize:c,activeBar:u,animationBegin:f,animationDuration:p,animationEasing:h,isAnimationActive:y}=(0,Q.e)(e,ev),{needClip:v}=(0,S.l)(r,i),g=(0,k.WX)(),m=(0,q.r)(),b=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:c,stackId:(0,d.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,c,e.stackId]),x=(0,s.aS)(e.children,l.f),w=(0,P.G)(e=>V(e,r,i,m,b,x));if("vertical"!==g&&"horizontal"!==g)return null;var O=null==w?void 0:w[0];return t=null==O||null==O.height||null==O.width?0:"vertical"===g?O.height/2:O.width/2,n.createElement(M.zk,{xAxisId:r,yAxisId:i,data:w,dataPointFormatter:eh,errorBarOffset:t},n.createElement(ey,ei({},e,{layout:g,needClip:v,data:w,xAxisId:r,yAxisId:i,hide:a,legendType:o,minPointSize:c,activeBar:u,animationBegin:f,animationDuration:p,animationEasing:h,isAnimationActive:y})))}function em(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:s,stackedData:f,displayedData:p,offset:h,cells:y}=e,v="horizontal"===t?l:o,g=f?v.scale.domain():null,m=(0,d.DW)({numericAxis:v});return p.map((e,p)=>{f?b=(0,d._f)(f[p],g):Array.isArray(b=(0,d.kr)(e,r))||(b=[m,b]);var v=w(n,0)(b[1],p);if("horizontal"===t){var b,x,O,j,P,E,A,[M,S]=[l.scale(b[0]),l.scale(b[1])];x=(0,d.y2)({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:e,index:p}),O=null!=(A=null!=S?S:M)?A:void 0,j=i.size;var k=M-S;if(P=(0,u.M8)(k)?0:k,E={x,y:h.top,width:j,height:h.height},Math.abs(v)>0&&Math.abs(P)<Math.abs(v)){var _=(0,u.sA)(P||v)*(Math.abs(v)-Math.abs(P));O-=_,P+=_}}else{var[T,C]=[o.scale(b[0]),o.scale(b[1])];if(x=T,O=(0,d.y2)({axis:l,ticks:s,bandSize:a,offset:i.offset,entry:e,index:p}),j=C-T,P=i.size,E={x:h.left,y:O,width:h.width,height:P},Math.abs(v)>0&&Math.abs(j)<Math.abs(v)){var D=(0,u.sA)(j||v)*(Math.abs(v)-Math.abs(j));j+=D}}return eo(eo({},e),{},{x,y:O,width:j,height:P,value:f?b:b[1],payload:e,background:E,tooltipPosition:{x:x+j/2,y:O+P/2}},y&&y[p]&&y[p].props)})}class eb extends n.PureComponent{render(){return n.createElement(M._S,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(A,null),n.createElement(J.A,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,d.uM)(r,t),payload:e}]})(this.props)}),n.createElement(j.r,{fn:eu,args:this.props}),n.createElement(eg,this.props))}}el(eb,"displayName","Bar"),el(eb,"defaultProps",ev)},6752:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(1971),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},6784:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(2115);let i=n.forwardRef(function(e,t){let{title:r,titleId:i,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},a),r?n.createElement("title",{id:i},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))})},6850:(e,t,r)=>{"use strict";r.d(t,{l3:()=>g,m7:()=>m});var n=r(2115),i=r(1971),a=r(8478),o=new(r(2661)),l="recharts.syncEvent.tooltip",c="recharts.syncEvent.brush",u=r(6641),s=r(4890),f=r(4732),d=r(215);function p(e){return e.tooltip.syncInteraction}var h=r(7238),y=r(4487),v=()=>{};function g(){var e,t,r,f,p,g,m,b,x,w,O,j=(0,i.j)();(0,n.useEffect)(()=>{j((0,u.dl)())},[j]),e=(0,i.G)(a.lZ),t=(0,i.G)(a.pH),r=(0,i.j)(),f=(0,i.G)(a.hX),p=(0,i.G)(d.R4),g=(0,h.WX)(),m=(0,h.sk)(),b=(0,i.G)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return v;var n=(n,i,a)=>{if(t!==a&&e===n){if("index"===f)return void r(i);if(null!=p){if("function"==typeof f){var o,l=f(p,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});o=p[l]}else"value"===f&&(o=p.find(e=>String(e.value)===i.payload.label));var{coordinate:c}=i.payload;if(null==o||!1===i.payload.active||null==c||null==m)return void r((0,s.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:u,y:d}=c,h=Math.min(u,m.x+m.width),y=Math.min(d,m.y+m.height),v={x:"horizontal"===g?o.coordinate:h,y:"horizontal"===g?y:o.coordinate};r((0,s.E1)({active:i.payload.active,coordinate:v,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label}))}}};return o.on(l,n),()=>{o.off(l,n)}},[b,r,t,e,f,p,g,m]),x=(0,i.G)(a.lZ),w=(0,i.G)(a.pH),O=(0,i.j)(),(0,n.useEffect)(()=>{if(null==x)return v;var e=(e,t,r)=>{w!==r&&x===e&&O((0,y.M)(t))};return o.on(c,e),()=>{o.off(c,e)}},[O,w,x])}function m(e,t,r,c,u,d){var h=(0,i.G)(r=>(0,f.dp)(r,e,t)),y=(0,i.G)(a.pH),v=(0,i.G)(a.lZ),g=(0,i.G)(a.hX),m=(0,i.G)(p),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=v&&null!=y){var e=(0,s.E1)({active:d,coordinate:r,dataKey:h,index:u,label:"number"==typeof c?String(c):c});o.emit(l,v,e,y)}},[b,r,h,u,c,y,v,g,d])}},6908:(e,t,r)=>{"use strict";r.d(t,{W:()=>a,h:()=>i});var n=r(8924),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},7040:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},7062:(e,t,r)=>{"use strict";r.d(t,{Be:()=>v,Cv:()=>O,D0:()=>P,Gl:()=>g,Dc:()=>j});var n=r(8924),i=r(2589),a=r(8573),o=r(5641),l=r(6377),c={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},u={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},s=r(8190),f=r(7238),d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:c.angleAxisId,includeHidden:!1,name:void 0,reversed:c.reversed,scale:c.scale,tick:c.tick,tickCount:void 0,ticks:void 0,type:c.type,unit:void 0},p={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:u.type,unit:void 0},h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:"category",unit:void 0},v=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?h:d,g=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:p,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.HZ],o.lY),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([v,O],s.I);var j=(0,n.Mz)([b,x,w],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([g,j],s.I);var P=(0,n.Mz)([f.fz,m,x,w,i.Lp,i.A$],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:c,startAngle:u,endAngle:s}=t;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(c,a,a/2),innerRadius:r,outerRadius:n,startAngle:u,endAngle:s,clockWise:!1}}})},7064:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(5181),i=r(1551),a=r(4072);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>{var r,n;return r=t,null==(n=e)||null==r?n:"object"==typeof r&&"key"in r?Object.hasOwn(n,r.key)?n[r.key]:l(n,r.path):"function"==typeof r?r(n):Array.isArray(r)?l(n,r):"object"==typeof n?n[r]:n})})).slice().sort((e,t)=>{for(let i=0;i<c.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},7119:(e,t,r)=>{"use strict";r.d(t,{p:()=>u,v:()=>s});var n=r(2115),i=r(1971),a=r(2248),o=r(9827);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){var t=(0,i.j)(),r=(0,n.useRef)(null);return(0,n.useEffect)(()=>{var n=c(c({},e),{},{stackId:(0,o.$8)(e.stackId)});null===r.current?t((0,a.g5)(n)):r.current!==n&&t((0,a.ZF)({prev:r.current,next:n})),r.current=n},[t,e]),(0,n.useEffect)(()=>()=>{r.current&&(t((0,a.Vi)(r.current)),r.current=null)},[t]),null}function s(e){var t=(0,i.j)();return(0,n.useEffect)(()=>(t((0,a.As)(e)),()=>{t((0,a.TK)(e))}),[t,e]),null}},7195:(e,t,r)=>{"use strict";r.d(t,{Q:()=>c,l:()=>l});var n=r(2115),i=r(1971),a=r(4299),o=r(8234);function l(e,t){var r,n,o=(0,i.G)(t=>(0,a.Rl)(t,e)),l=(0,i.G)(e=>(0,a.sf)(e,t)),c=null!=(r=null==o?void 0:o.allowDataOverflow)?r:a.PU.allowDataOverflow,u=null!=(n=null==l?void 0:l.allowDataOverflow)?n:a.cd.allowDataOverflow;return{needClip:c||u,needClipX:c,needClipY:u}}function c(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,o.oM)(),{needClipX:c,needClipY:u,needClip:s}=l(t,r);if(!s)return null;var{x:f,y:d,width:p,height:h}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:c?f:f-p/2,y:u?d:d-h/2,width:c?p:2*p,height:u?h:2*h}))}},7238:(e,t,r)=>{"use strict";r.d(t,{W7:()=>s,WX:()=>h,fz:()=>p,rY:()=>d,sk:()=>c,yi:()=>f}),r(2115);var n=r(1971),i=r(8573),a=r(2589),o=r(1807),l=r(972),c=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),c=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&a&&c?{width:a.width-c.left-c.right,height:a.height-c.top-c.bottom,x:c.left,y:c.top}:r},u={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:u},f=()=>(0,n.G)(a.Lp),d=()=>(0,n.G)(a.A$),p=e=>e.layout.layoutType,h=()=>(0,n.G)(p)},7283:(e,t,r)=>{"use strict";r.d(t,{h:()=>p});var n=r(2115),i=r(2596),a=r(788),o=r(5641),l=r(6377),c=r(3389);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:l,cornerRadius:c,cornerIsExternal:u}=e,s=c*(l?1:-1)+n,f=Math.asin(c/s)/o.Kg,d=u?i:i+a*f,p=(0,o.IZ)(t,r,s,d);return{center:p,circleTangency:(0,o.IZ)(t,r,n,d),lineTangency:(0,o.IZ)(t,r,s*Math.cos(f*o.Kg),u?i-a*f:i),theta:f}},f=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:c}=e,u=((e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999))(a,c),s=a+u,f=(0,o.IZ)(t,r,i,a),d=(0,o.IZ)(t,r,i,s),p="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(a>s),",\n    ").concat(d.x,",").concat(d.y,"\n  ");if(n>0){var h=(0,o.IZ)(t,r,n,a),y=(0,o.IZ)(t,r,n,s);p+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(a<=s),",\n            ").concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(t,",").concat(r," Z");return p},d={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},p=e=>{var t,r=(0,c.e)(e,d),{cx:o,cy:p,innerRadius:h,outerRadius:y,cornerRadius:v,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x,className:w}=r;if(y<h||b===x)return null;var O=(0,i.$)("recharts-sector",w),j=y-h,P=(0,l.F4)(v,j,0,!0);return t=P>0&&360>Math.abs(b-x)?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:c,startAngle:u,endAngle:d}=e,p=(0,l.sA)(d-u),{circleTangency:h,lineTangency:y,theta:v}=s({cx:t,cy:r,radius:i,angle:u,sign:p,cornerRadius:a,cornerIsExternal:c}),{circleTangency:g,lineTangency:m,theta:b}=s({cx:t,cy:r,radius:i,angle:d,sign:-p,cornerRadius:a,cornerIsExternal:c}),x=c?Math.abs(u-d):Math.abs(u-d)-v-b;if(x<0)return o?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):f({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:d});var w="M ".concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(x>180),",").concat(+(p<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:j,theta:P}=s({cx:t,cy:r,radius:n,angle:u,sign:p,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),{circleTangency:E,lineTangency:A,theta:M}=s({cx:t,cy:r,radius:n,angle:d,sign:-p,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),S=c?Math.abs(u-d):Math.abs(u-d)-P-M;if(S<0&&0===a)return"".concat(w,"L").concat(t,",").concat(r,"Z");w+="L".concat(A.x,",").concat(A.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(E.x,",").concat(E.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(S>180),",").concat(+(p>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(j.x,",").concat(j.y,"Z")}else w+="L".concat(t,",").concat(r,"Z");return w})({cx:o,cy:p,innerRadius:h,outerRadius:y,cornerRadius:Math.min(P,j/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x}):f({cx:o,cy:p,innerRadius:h,outerRadius:y,startAngle:b,endAngle:x}),n.createElement("path",u({},(0,a.J9)(r,!0),{className:O,d:t}))}},7298:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9738);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},7335:(e,t,r)=>{"use strict";r.d(t,{x:()=>o,y:()=>a});var n=r(5710),i=r(215),a=(0,n.VP)("externalEvent"),o=(0,n.Nc)();o.startListening({actionCreator:a,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,i.eE)(r),activeDataKey:(0,i.Xb)(r),activeIndex:(0,i.A2)(r),activeLabel:(0,i.BZ)(r),activeTooltipIndex:(0,i.A2)(r),isTooltipActive:(0,i.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},7547:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(3676),i=r(2465),a=r(656),o=r(1571);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},8080:(e,t,r)=>{e.exports=r(8359).last},8132:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(2744);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},8179:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(9452);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},8190:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},8221:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},8234:(e,t,r)=>{"use strict";r.d(t,{EI:()=>y,oM:()=>h,ZI:()=>d,gi:()=>p});var n=r(4299),i=r(1971),a=r(1807),o=r(215),l=r(8924),c=r(8573),u=(0,l.Mz)([c.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),s=r(2589),f=(0,l.Mz)([u,s.Lp,s.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),d=e=>{var t=(0,a.r)();return(0,i.G)(r=>(0,n.Gx)(r,"xAxis",e,t))},p=e=>{var t=(0,a.r)();return(0,i.G)(r=>(0,n.Gx)(r,"yAxis",e,t))},h=()=>(0,i.G)(f),y=()=>(0,i.G)(o.JG)},8357:(e,t,r)=>{"use strict";r.d(t,{F:()=>eg,L:()=>ef});var n=r(2115),i=r(5672),a=r.n(i),o=r(2596),l=r(8924),c=r(356),u=r(8573),s=r(9827),f=r(4299),d=r(7238),p=r(6038),h=r(2287),y=r(8478),v=e=>e.graphicalItems.polarItems,g=(0,l.Mz)([p.N,h.E],f.eo),m=(0,l.Mz)([v,f.DP,g],f.ec),b=(0,l.Mz)([m],f.rj),x=(0,l.Mz)([b,c.z3],f.Nk),w=(0,l.Mz)([x,f.DP,m],f.fb),O=(0,l.Mz)([x,f.DP,m],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,s.kr)(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,s.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),j=()=>void 0,P=(0,l.Mz)([f.DP,f.AV,j,O,j],f.wL),E=(0,l.Mz)([f.DP,d.fz,x,w,y.eC,p.N,P],f.tP),A=(0,l.Mz)([E,f.DP,f.xM],f.xp);function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,l.Mz)([f.DP,E,A,p.N],f.g1);var k=(e,t)=>t,_=[],T=(e,t,r)=>(null==r?void 0:r.length)===0?_:r,C=(0,l.Mz)([c.z3,k,T],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>S(S({},t.presentationProps),e.props))),null!=n)return n}),D=(0,l.Mz)([C,k,T],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=(0,s.kr)(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,s.uM)(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),N=(0,l.Mz)([v,k],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),I=(0,l.Mz)([C,N,T,u.HZ],(e,t,r,n)=>{if(null!=t&&null!=e)return ef({offset:n,pieSettings:t,displayedData:e,cells:r})}),z=r(1971),L=r(7119),R=r(2348),$=r(688),B=r(9095),U=r(4811),F=r(788),K=r(1643),H=r(5641),G=r(6377),Z=r(3597),W=r(8605),V=r(9129),q=r(6091),Y=r(215),J=r(9020),X=r(4421),Q=r(9426),ee=r(3389),et=r(4460),er=["onMouseEnter","onClick","onMouseLeave"];function en(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ei(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?en(Object(r),!0).forEach(function(t){ea(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ea(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eo(){return(eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function el(e){var t=(0,n.useMemo)(()=>(0,F.J9)(e,!1),[e]),r=(0,n.useMemo)(()=>(0,F.aS)(e.children,U.f),[e.children]),i=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),a=(0,z.G)(e=>D(e,i,r));return n.createElement(J._,{legendPayload:a})}function ec(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:c,tooltipType:u}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:(0,s.uM)(l,t),hide:c,type:u,color:o,unit:""}}}function eu(e){var{sectors:t,props:r,showLabels:i}=e,{label:a,labelLine:l,dataKey:c}=r;if(!i||!a||!t)return null;var u=(0,F.J9)(r,!1),f=(0,F.J9)(a,!1),d=(0,F.J9)(l,!1),p="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,h=t.map((e,t)=>{var r,i,h=(e.startAngle+e.endAngle)/2,y=(0,H.IZ)(e.cx,e.cy,e.outerRadius+p,h),v=ei(ei(ei(ei({},u),e),{},{stroke:"none"},f),{},{index:t,textAnchor:(r=y.x)>(i=e.cx)?"start":r<i?"end":"middle"},y),g=ei(ei(ei(ei({},u),e),{},{fill:"none",stroke:e.fill},d),{},{index:t,points:[(0,H.IZ)(e.cx,e.cy,e.outerRadius,h),y],key:"line"});return n.createElement(R.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},l&&((e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,o.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement($.I,eo({},t,{type:"linear",className:r}))})(l,g),((e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var i=r;if("function"==typeof e&&(i=e(t),n.isValidElement(i)))return i;var a=(0,o.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(B.E,eo({},t,{alignmentBaseline:"middle",className:a}),i)})(a,v,(0,s.kr)(e,c)))});return n.createElement(R.W,{className:"recharts-pie-labels"},h)}function es(e){var{sectors:t,activeShape:r,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,l=(0,z.G)(Y.A2),{onMouseEnter:c,onClick:u,onMouseLeave:s}=a,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(a,er),d=(0,V.Cj)(c,a.dataKey),p=(0,V.Pg)(s),h=(0,V.Ub)(u,a.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var c=r&&String(o)===l,u=c?r:l?i:null,s=ei(ei({},e),{},{stroke:e.stroke,tabIndex:-1,[X.F0]:o,[X.um]:a.dataKey});return n.createElement(R.W,eo({tabIndex:-1,className:"recharts-pie-sector"},(0,Z.XC)(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:p(e,o),onClick:h(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),n.createElement(W.y,eo({option:u,isActive:c,shapeType:"sector"},s)))}),n.createElement(eu,{sectors:t,props:a,showLabels:o}))}function ef(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:c,startAngle:u,endAngle:f,dataKey:d,nameKey:p,tooltipType:h}=i,y=Math.abs(i.minAngle),v=(0,G.sA)(f-u)*Math.min(Math.abs(f-u),360),g=Math.abs(v),m=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,b=a.filter(e=>0!==(0,s.kr)(e,d,0)).length,x=g-b*y-(g>=360?b:b-1)*m,w=a.reduce((e,t)=>{var r=(0,s.kr)(t,d,0);return e+((0,G.Et)(r)?r:0)},0);return w>0&&(r=a.map((e,t)=>{var r,a=(0,s.kr)(e,d,0),f=(0,s.kr)(e,p,t),g=((e,t,r)=>{let n,i,a;var{top:o,left:l,width:c,height:u}=t,s=(0,H.lY)(c,u),f=l+(0,G.F4)(e.cx,c,c/2),d=o+(0,G.F4)(e.cy,u,u/2),p=(0,G.F4)(e.innerRadius,s,0);return{cx:f,cy:d,innerRadius:p,outerRadius:(n=r,i=e.outerRadius,a=s,"function"==typeof i?i(n):(0,G.F4)(i,a,.8*a)),maxRadius:e.maxRadius||Math.sqrt(c*c+u*u)/2}})(i,l,e),b=((0,G.Et)(a)?a:0)/w,O=ei(ei({},e),o&&o[t]&&o[t].props),j=(r=t?n.endAngle+(0,G.sA)(v)*m*(0!==a):u)+(0,G.sA)(v)*((0!==a?y:0)+b*x),P=(r+j)/2,E=(g.innerRadius+g.outerRadius)/2,A=[{name:f,value:a,payload:O,dataKey:d,type:h}],M=(0,H.IZ)(g.cx,g.cy,E,P);return n=ei(ei(ei(ei({},i.presentationProps),{},{percent:b,cornerRadius:c,name:f,tooltipPayload:A,midAngle:P,middleRadius:E,tooltipPosition:M},O),g),{},{value:(0,s.kr)(e,d),startAngle:r,endAngle:j,payload:O,paddingAngle:(0,G.sA)(v)*m})})),r}function ed(e){var{props:t,previousSectorsRef:r}=e,{sectors:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:u,activeShape:s,inactiveShape:f,onAnimationStart:d,onAnimationEnd:p}=t,h=(0,Q.n)(t,"recharts-pie-"),y=r.current,[v,g]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof p&&p(),g(!1)},[p]),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),g(!0)},[d]);return n.createElement(et.i,{begin:l,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:m,key:h},e=>{var{t:o}=e,l=[],c=(i&&i[0]).startAngle;return i.forEach((e,t)=>{var r=y&&y[t],n=t>0?a()(e,"paddingAngle",0):0;if(r){var i=(0,G.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),u=ei(ei({},e),{},{startAngle:c+n,endAngle:c+i(o)+n});l.push(u),c=u.endAngle}else{var{endAngle:s,startAngle:f}=e,d=(0,G.Dj)(0,s-f)(o),p=ei(ei({},e),{},{startAngle:c+n,endAngle:c+d+n});l.push(p),c=p.endAngle}}),r.current=l,n.createElement(R.W,null,n.createElement(es,{sectors:l,activeShape:s,inactiveShape:f,allOtherPieProps:t,showLabels:!v}))})}function ep(e){var{sectors:t,isAnimationActive:r,activeShape:i,inactiveShape:a}=e,o=(0,n.useRef)(null),l=o.current;return r&&t&&t.length&&(!l||l!==t)?n.createElement(ed,{props:e,previousSectorsRef:o}):n.createElement(es,{sectors:t,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function eh(e){var{hide:t,className:r,rootTabIndex:i}=e,a=(0,o.$)("recharts-pie",r);return t?null:n.createElement(R.W,{tabIndex:i,className:a},n.createElement(ep,e))}var ey={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!K.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function ev(e){var t=(0,ee.e)(e,ey),r=(0,n.useMemo)(()=>(0,F.aS)(e.children,U.f),[e.children]),i=(0,F.J9)(t,!1),a=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:i}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,i]),o=(0,z.G)(e=>I(e,a,r));return n.createElement(n.Fragment,null,n.createElement(q.r,{fn:ec,args:ei(ei({},t),{},{sectors:o})}),n.createElement(eh,eo({},t,{sectors:o})))}class eg extends n.PureComponent{render(){return n.createElement(n.Fragment,null,n.createElement(L.v,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(el,this.props),n.createElement(ev,this.props),this.props.children)}constructor(){super(...arguments),ea(this,"id",(0,G.NF)("recharts-pie-"))}}ea(eg,"displayName","Pie"),ea(eg,"defaultProps",ey)},8359:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(220),i=r(4986),a=r(8179);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},8412:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},8460:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var n=r(2115),i=r(8080),a=r.n(i),o=r(379),l=r(2348),c=r(788),u=r(9827),s=r(6377),f=["valueAccessor"],d=["data","dataKey","clockWise","id","textBreakAll"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var g=e=>Array.isArray(e.value)?a()(e.value):e.value;function m(e){var{valueAccessor:t=g}=e,r=v(e,f),{data:i,dataKey:a,clockWise:h,id:m,textBreakAll:b}=r,x=v(r,d);return i&&i.length?n.createElement(l.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,s.uy)(a)?t(e,r):(0,u.kr)(e&&e.payload,a),l=(0,s.uy)(m)?{}:{id:"".concat(m,"-").concat(r)};return n.createElement(o.J,p({},(0,c.J9)(e,!0),x,l,{parentViewBox:e.parentViewBox,value:i,textBreakAll:b,viewBox:o.J.parseViewBox((0,s.uy)(h)?e:y(y({},e),{},{clockWise:h})),key:"label-".concat(r),index:r}))})):null}m.displayName="LabelList",m.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,l=(0,c.aS)(a,m).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement(m,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,o.Z)(r)?n.createElement(m,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(m,p({data:t},r,{key:"labelList-implicit"})):null:null),...l]:l}},8478:(e,t,r)=>{"use strict";r.d(t,{JN:()=>n,_5:()=>i,eC:()=>l,gY:()=>a,hX:()=>s,iO:()=>c,lZ:()=>u,pH:()=>f,x3:()=>o});var n=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,a=e=>e.rootProps.barCategoryGap,o=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,c=e=>e.options.chartName,u=e=>e.rootProps.syncId,s=e=>e.rootProps.syncMethod,f=e=>e.options.eventEmitter},8573:(e,t,r)=>{"use strict";r.d(t,{c2:()=>g,HZ:()=>y,Ds:()=>v});var n=r(8924),i=r(5672),a=r.n(i),o=r(241),l=r.n(o),c=e=>e.legend.settings;(0,n.Mz)([e=>e.legend.payload,c],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?l()(n,r):n});var u=r(9827),s=r(2589),f=r(6908),d=r(4421);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(0,n.Mz)([s.Lp,s.A$,s.HK,e=>e.brush.height,f.h,f.W,c,e=>e.legend.size],(e,t,r,n,i,o,l,c)=>{var s=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:d.tQ;return h(h({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),f=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:h(h({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),p=h(h({},f),s),y=p.bottom;p.bottom+=n;var v=e-(p=(0,u.s0)(p,l,c)).left-p.right,g=t-p.top-p.bottom;return h(h({brushBottom:y},p),{},{width:Math.max(v,0),height:Math.max(g,0)})}),v=(0,n.Mz)(y,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),g=(0,n.Mz)(s.Lp,s.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},8605:(e,t,r)=>{"use strict";r.d(t,{y:()=>W});var n=r(2115),i=r(931),a=r.n(i),o=r(4538),l=r(2596),c=r(788),u=r(3389),s=r(4460);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d=(e,t,r,n,i)=>{var a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+i)+"L ".concat(e+r-a/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},p={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},h=e=>{var t=(0,u.e)(e,p),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:o,y:h,upperWidth:y,lowerWidth:v,height:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isUpdateAnimationActive:O}=t;if(o!==+o||h!==+h||y!==+y||v!==+v||g!==+g||0===y&&0===v||0===g)return null;var j=(0,l.$)("recharts-trapezoid",m);return O?n.createElement(s.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:g,x:o,y:h},to:{upperWidth:y,lowerWidth:v,height:g,x:o,y:h},duration:x,animationEasing:b,isActive:O},e=>{var{upperWidth:a,lowerWidth:o,height:l,x:u,y:p}=e;return n.createElement(s.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,easing:b},n.createElement("path",f({},(0,c.J9)(t,!0),{className:j,d:d(u,p,a,o,l),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,c.J9)(t,!0),{className:j,d:d(o,h,y,v,g)})))},y=r(7283),v=r(2348);let g=Math.cos,m=Math.sin,b=Math.sqrt,x=Math.PI,w=2*x,O={draw(e,t){let r=b(t/x);e.moveTo(r,0),e.arc(0,0,r,0,w)}},j=b(1/3),P=2*j,E=m(x/10)/m(7*x/10),A=m(w/10)*E,M=-g(w/10)*E,S=b(3),k=b(3)/2,_=1/b(12),T=(_/2+1)*3;var C=r(5654),D=r(1847);b(3),b(3);var N=r(6377),I=["type","size","sizeType"];function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var $={symbolCircle:O,symbolCross:{draw(e,t){let r=b(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=b(t/P),n=r*j;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=b(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=b(.8908130915292852*t),n=A*r,i=M*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=w*t/5,o=g(a),l=m(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-b(t/(3*S));e.moveTo(0,2*r),e.lineTo(-S*r,-r),e.lineTo(S*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=b(t/T),n=r/2,i=r*_,a=r*_+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-k*i,k*n+-.5*i),e.lineTo(-.5*n-k*a,k*n+-.5*a),e.lineTo(-.5*o-k*a,k*o+-.5*a),e.lineTo(-.5*n+k*i,-.5*i-k*n),e.lineTo(-.5*n+k*a,-.5*a-k*n),e.lineTo(-.5*o+k*a,-.5*a-k*o),e.closePath()}}},B=Math.PI/180,U=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,a=R(R({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,I)),{},{type:t,size:r,sizeType:i}),{className:o,cx:u,cy:s}=a,f=(0,c.J9)(a,!0);return u===+u&&s===+s&&r===+r?n.createElement("path",z({},f,{className:(0,l.$)("recharts-symbols",o),transform:"translate(".concat(u,", ").concat(s,")"),d:(()=>{var e=$["symbol".concat((0,N.Zb)(t))]||O;return(function(e,t){let r=null,n=(0,D.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,C.A)(e||O),t="function"==typeof t?t:(0,C.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,C.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,C.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*B;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(r,i,t))()})()})):null};U.registerSymbol=(e,t)=>{$["symbol".concat((0,N.Zb)(e))]=t};var F=["option","shapeType","propTransformer","activeClassName","isActive"];function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function G(e,t){return H(H({},t),e)}function Z(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(o.M,r);case"trapezoid":return n.createElement(h,r);case"sector":return n.createElement(y.h,r);case"symbols":if("symbols"===t)return n.createElement(U,r);break;default:return null}}function W(e){var t,{option:r,shapeType:i,propTransformer:o=G,activeClassName:l="recharts-active-shape",isActive:c}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,F);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,H(H({},u),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(u);else if(a()(r)&&"boolean"!=typeof r){var s=o(r,u);t=n.createElement(Z,{shapeType:i,elementProps:s})}else t=n.createElement(Z,{shapeType:i,elementProps:u});return c?n.createElement(v.W,{className:l},t):t}},8627:(e,t,r)=>{"use strict";r.d(t,{B_:()=>i,JK:()=>a,Vp:()=>c,gX:()=>o,hF:()=>l});var n=(0,r(5710).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:a,setChartSize:o,setScale:l}=n.actions,c=n.reducer},8673:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(564);t.debounce=function(e,t=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:l}=r,c=[,,];a&&(c[0]="leading"),o&&(c[1]="trailing");let u=null,s=n.debounce(function(...t){i=e.apply(this,t),u=null},t,{edges:c}),f=function(...t){return null!=l&&(null===u&&(u=Date.now()),Date.now()-u>=l)?(i=e.apply(this,t),u=Date.now(),s.cancel(),s.schedule(),i):(s.apply(this,t),i)};return f.cancel=s.cancel,f.flush=()=>(s.flush(),i),f}},8870:function(e,t,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,c="[DecimalError] ",u=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,d=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=f(1286742750677284.5),y={};function v(e,t){var r,n,i,a,o,c,u,s,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?A(t,d):t;if(u=e.d,s=t.d,o=e.e,i=t.e,u=u.slice(),a=o-i){for(a<0?(n=u,a=-a,c=s.length):(n=s,i=o,c=u.length),a>(c=(o=Math.ceil(d/7))>c?o+1:c+1)&&(a=c,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((c=u.length)-(a=s.length)<0&&(a=c,n=s,s=u,u=n),r=0;a;)r=(u[--a]=u[a]+s[a]+r)/1e7|0,u[a]%=1e7;for(r&&(u.unshift(r),++i),c=u.length;0==u[--c];)u.pop();return t.d=u,t.e=i,l?A(t,d):t}function g(e,t,r){if(e!==~~e||e<t||e>r)throw Error(u+e)}function m(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=j(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=j(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return A(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return w(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(a))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(l=!1,t=b(P(this,i),P(e,i),i),l=!0,A(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?M(this,e):v(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(c+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):A(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return P(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?v(this,e):M(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(u+e);if(t=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,i,a,o,u=this.constructor;if(this.s<1){if(!this.s)return new u(0);throw Error(c+"NaN")}for(e=w(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new u(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new u(i.toString()),i=o=(r=u.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),m(a.d).slice(0,o)===(t=m(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(A(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,A(n,r)},y.times=y.mul=function(e){var t,r,n,i,a,o,c,u,s,f=this.constructor,d=this.d,p=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(u=d.length)<(s=p.length)&&(a=d,d=p,p=a,o=u,u=s,s=o),a=[],n=o=u+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=u+n;i>n;)c=a[i]+p[n]*d[i-n-1]+t,a[i--]=c%1e7|0,t=c/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?A(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(g(e,0,1e9),void 0===t?t=n.rounding:g(t,0,8),A(r,e+w(r)+1,t))},y.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=S(n,!0):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=S(n=A(new i(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?S(this):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=S((n=A(new i(this),e+w(this)+1,t)).abs(),!1,e+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return A(new e(this),w(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,i,o,u,s=this,d=s.constructor,p=+(e=new d(e));if(!e.s)return new d(a);if(!(s=new d(s)).s){if(e.s<1)throw Error(c+"Infinity");return s}if(s.eq(a))return s;if(n=d.precision,e.eq(a))return A(s,n);if(u=(t=e.e)>=(r=e.d.length-1),o=s.s,u){if((r=p<0?-p:p)<=0x1fffffffffffff){for(i=new d(a),t=Math.ceil(n/7+4),l=!1;r%2&&k((i=i.times(s)).d,t),0!==(r=f(r/2));)k((s=s.times(s)).d,t);return l=!0,e.s<0?new d(a).div(i):A(i,n)}}else if(o<0)throw Error(c+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,l=!1,i=e.times(P(s,n+12)),l=!0,(i=x(i)).s=o,i},y.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=w(i),n=S(i,r<=a.toExpNeg||r>=a.toExpPos)):(g(e,1,1e9),void 0===t?t=a.rounding:g(t,0,8),r=w(i=A(new a(i),e,t)),n=S(i,e<=r||r<=a.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(g(e,1,1e9),void 0===t?t=r.rounding:g(t,0,8)),A(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=w(this),t=this.constructor;return S(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,u,s,f,d,p,h,y,v,g,m,b,x,O,j,P,E,M,S=n.constructor,k=n.s==i.s?1:-1,_=n.d,T=i.d;if(!n.s)return new S(n);if(!i.s)throw Error(c+"Division by zero");for(s=0,u=n.e-i.e,E=T.length,j=_.length,y=(h=new S(k)).d=[];T[s]==(_[s]||0);)++s;if(T[s]>(_[s]||0)&&--u,(b=null==a?a=S.precision:o?a+(w(n)-w(i))+1:a)<0)return new S(0);if(b=b/7+2|0,s=0,1==E)for(f=0,T=T[0],b++;(s<j||f)&&b--;s++)x=1e7*f+(_[s]||0),y[s]=x/T|0,f=x%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=e(T,f),_=e(_,f),E=T.length,j=_.length),O=E,g=(v=_.slice(0,E)).length;g<E;)v[g++]=0;(M=T.slice()).unshift(0),P=T[0],T[1]>=1e7/2&&++P;do f=0,(l=t(T,v,E,g))<0?(m=v[0],E!=g&&(m=1e7*m+(v[1]||0)),(f=m/P|0)>1?(f>=1e7&&(f=1e7-1),p=(d=e(T,f)).length,g=v.length,1==(l=t(d,v,p,g))&&(f--,r(d,E<p?M:T,p))):(0==f&&(l=f=1),d=T.slice()),(p=d.length)<g&&d.unshift(0),r(v,d,g),-1==l&&(g=v.length,(l=t(T,v,E,g))<1&&(f++,r(v,E<g?M:T,g))),g=v.length):0===l&&(f++,v=[0]),y[s++]=f,l&&v[0]?v[g++]=_[O]||0:(v=[_[O]],g=1);while((O++<j||void 0!==v[0])&&b--)}return y[0]||y.shift(),h.e=u,A(h,o?a+w(h)+1:a)}}();function x(e,t){var r,n,i,o,c,u=0,f=0,p=e.constructor,h=p.precision;if(w(e)>16)throw Error(s+w(e));if(!e.s)return new p(a);for(null==t?(l=!1,c=h):c=t,o=new p(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(c+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=i=new p(a),p.precision=c;;){if(n=A(n.times(e),c),r=r.times(++u),m((o=i.plus(b(n,r,c))).d).slice(0,c)===m(i.d).slice(0,c)){for(;f--;)i=A(i.times(i),c);return p.precision=h,null==t?(l=!0,A(i,h)):i}i=o}}function w(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(c+"LN10 precision limit exceeded");return A(new e(e.LN10),t)}function j(e){for(var t="";e--;)t+="0";return t}function P(e,t){var r,n,i,o,u,s,f,d,p,h=1,y=e,v=y.d,g=y.constructor,x=g.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(a))return new g(0);if(null==t?(l=!1,d=x):d=t,y.eq(10))return null==t&&(l=!0),O(g,d);if(g.precision=d+=10,n=(r=m(v)).charAt(0),!(15e14>Math.abs(o=w(y))))return f=O(g,d+2,x).times(o+""),y=P(new g(n+"."+r.slice(1)),d-10).plus(f),g.precision=x,null==t?(l=!0,A(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(e)).d)).charAt(0),h++;for(o=w(y),n>1?(y=new g("0."+r),o++):y=new g(n+"."+r.slice(1)),s=u=y=b(y.minus(a),y.plus(a),d),p=A(y.times(y),d),i=3;;){if(u=A(u.times(p),d),m((f=s.plus(b(u,new g(i),d))).d).slice(0,d)===m(s.d).slice(0,d))return s=s.times(2),0!==o&&(s=s.plus(O(g,d+2,x).times(o+""))),s=b(s,new g(h),d),g.precision=x,null==t?(l=!0,A(s,x)):s;s=f,i+=2}}function E(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>h||e.e<-h))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function A(e,t,r){var n,i,a,o,c,u,p,y,v=e.d;for(o=1,a=v[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,p=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=v.length))return e;for(o=1,p=a=v[y];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(c=p/(a=d(10,o-i-1))%10|0,u=t<0||void 0!==v[y+1]||p%a,u=r<4?(c||u)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?i>0?p/d(10,o-i):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return u?(a=w(e),v.length=1,t=t-a-1,v[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,a=1,y--):(v.length=y+1,a=d(10,7-n),v[y]=i>0?(p/d(10,o-i)%d(10,i)|0)*a:0),u)for(;;)if(0==y){1e7==(v[0]+=a)&&(v[0]=1,++e.e);break}else{if(v[y]+=a,1e7!=v[y])break;v[y--]=0,a=1}for(n=v.length;0===v[--n];)v.pop();if(l&&(e.e>h||e.e<-h))throw Error(s+w(e));return e}function M(e,t){var r,n,i,a,o,c,u,s,f,d,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),l?A(t,h):t;if(u=e.d,d=t.d,n=t.e,s=e.e,u=u.slice(),o=s-n){for((f=o<0)?(r=u,o=-o,c=d.length):(r=d,n=s,c=u.length),o>(i=Math.max(Math.ceil(h/7),c)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=u.length)<(c=d.length))&&(c=i),i=0;i<c;i++)if(u[i]!=d[i]){f=u[i]<d[i];break}o=0}for(f&&(r=u,u=d,d=r,t.s=-t.s),c=u.length,i=d.length-c;i>0;--i)u[c++]=0;for(i=d.length;i>o;){if(u[--i]<d[i]){for(a=i;a&&0===u[--a];)u[a]=1e7-1;--u[a],u[i]+=1e7}u[i]-=d[i]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(t.d=u,t.e=n,l?A(t,h):t):new p(0)}function S(e,t,r){var n,i=w(e),a=m(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+j(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+j(-i-1)+a,r&&(n=r-o)>0&&(a+=j(n))):i>=o?(a+=j(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+j(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=j(n))),e.s<0?"-"+a:a}function k(e,t){if(e.length>t)return e.length=t,!0}function _(e){if(!e||"object"!=typeof e)throw Error(c+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(u+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(u+r+": "+n);return this}(o=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(u+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return E(this,e.toString())}if("string"!=typeof e)throw Error(u+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,p.test(e))E(this,e);else throw Error(u+e)}if(a.prototype=y,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=_,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o)).default=o.Decimal=o,a=new o(1),void 0===(n=(function(){return o}).call(t,r,t,e))||(e.exports=n)}(0)},8892:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},8924:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>x});var n=e=>Array.isArray(e)?e:[e],i=0,a=class{revision=i;_value;_lastValue;_isEqual=o;constructor(e,t=o){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function o(e,t){return e===t}function l(e){return e instanceof a||console.warn("Not a valid cell! ",e),e.value}var c=(e,t)=>!1;function u(){return function(e,t=o){return new a(null,t)}(0,c)}var s=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=u()),l(t)};Symbol();var f=0,d=Object.getPrototypeOf({}),p=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,h);tag=u();tags={};children={};collectionTag=null;id=f++},h={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in d)return n;if("object"==typeof n&&null!==n){var i;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(i=n)?new y(i):new p(i)),r.tag&&l(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=u()).value=n),l(r),n}})(),ownKeys:e=>(s(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},y=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],v);tag=u();tags={};children={};collectionTag=null;id=f++},v={get:([e],t)=>("length"===t&&s(e),h.get(e,t)),ownKeys:([e])=>h.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>h.getOwnPropertyDescriptor(e,t),has:([e],t)=>h.has(e,t)},g="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function m(){return{s:0,v:void 0,o:null,p:null}}function b(e,t={}){let r,n=m(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=m(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=m(),e.set(t,o)):o=r}}let c=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new g(t):t}return c.s=1,c.v=t,t}return o.clearCache=()=>{n=m(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var x=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},l=e.pop();"object"==typeof l&&(o=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:c,memoizeOptions:u=[],argsMemoize:s=b,argsMemoizeOptions:f=[],devModeChecks:d={}}={...r,...o},p=n(u),h=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),v=c(function(){return i++,l.apply(null,arguments)},...p);return Object.assign(s(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=v.apply(null,e)},...h),{resultFunc:l,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:c,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(b),w=Object.assign((e,t=x)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>w})},9020:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,_:()=>s});var n=r(2115),i=r(1807),a=r(7238),o=r(1971),l=r(2634),c=()=>{};function u(e){var{legendPayload:t}=e,r=(0,o.j)(),a=(0,i.r)();return(0,n.useEffect)(()=>a?c:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,a,t]),null}function s(e){var{legendPayload:t}=e,r=(0,o.j)(),i=(0,o.G)(a.fz);return(0,n.useEffect)(()=>"centric"!==i&&"radial"!==i?c:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,i,t]),null}},9033:(e,t,r)=>{"use strict";e.exports=r(2436)},9035:(e,t,r)=>{"use strict";r.d(t,{f:()=>d});var n=r(6377),i=r(6605),a=r(1643);class o{static create(e){return new o(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):e[t]=1e-4}(o,"EPS",1e-4);var l=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function c(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function u(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t,r){var o,{tick:s,ticks:d,viewBox:p,minTickGap:h,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=e;if(!d||!d.length||!s)return[];if((0,n.Et)(v)||a.m.isSsr)return null!=(o=c(d,((0,n.Et)(v)?v:0)+1))?o:[];var x="top"===y||"bottom"===y?"width":"height",w=m&&"width"===x?(0,i.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},O=(e,n)=>{var a,o="function"==typeof g?g(e.value,n):e.value;return"width"===x?(a=(0,i.P)(o,{fontSize:t,letterSpacing:r}),l({width:a.width+w.width,height:a.height+w.height},b)):(0,i.P)(o,{fontSize:t,letterSpacing:r})[x]},j=d.length>=2?(0,n.sA)(d[1].coordinate-d[0].coordinate):1,P=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(p,j,x);return"equidistantPreserveStart"===v?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:s}=t,f=0,d=1,p=l;d<=o.length;)if(a=function(){var t,a=null==n?void 0:n[f];if(void 0===a)return{v:c(n,d)};var o=f,h=()=>(void 0===t&&(t=r(a,o)),t),y=a.coordinate,v=0===f||u(e,y,h,p,s);v||(f=0,p=l,d+=1),v&&(p=y+e*(h()/2+i),f+=d)}())return a.v;return[]}(j,P,O,d,h):("preserveStart"===v||"preserveStartEnd"===v?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:s}=t;if(a){var d=n[l-1],p=r(d,l-1),h=e*(d.coordinate+e*p/2-s);o[l-1]=d=f(f({},d),{},{tickCoord:h>0?d.coordinate-h*e:d.coordinate}),u(e,d.tickCoord,()=>p,c,s)&&(s=d.tickCoord-e*(p/2+i),o[l-1]=f(f({},d),{},{isShow:!0}))}for(var y=a?l-1:l,v=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var d=e*(a.coordinate-e*l()/2-c);o[t]=a=f(f({},a),{},{tickCoord:d<0?a.coordinate-d*e:a.coordinate})}else o[t]=a=f(f({},a),{},{tickCoord:a.coordinate});u(e,a.tickCoord,l,c,s)&&(c=a.tickCoord+e*(l()/2+i),o[t]=f(f({},a),{},{isShow:!0}))},g=0;g<y;g++)v(g);return o}(j,P,O,d,h,"preserveStartEnd"===v):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,s=function(t){var n,s=a[t],d=()=>(void 0===n&&(n=r(s,t)),n);if(t===o-1){var p=e*(s.coordinate+e*d()/2-c);a[t]=s=f(f({},s),{},{tickCoord:p>0?s.coordinate-p*e:s.coordinate})}else a[t]=s=f(f({},s),{},{tickCoord:s.coordinate});u(e,s.tickCoord,d,l,c)&&(c=s.tickCoord-e*(d()/2+i),a[t]=f(f({},s),{},{isShow:!0}))},d=o-1;d>=0;d--)s(d);return a}(j,P,O,d,h)).filter(e=>e.isShow)}},9068:(e,t,r)=>{"use strict";r.d(t,{TK:()=>l});var n=r(2115),i=r(4487),a=r(1971),o=r(1807),l=e=>{var{chartData:t}=e,r=(0,a.j)(),l=(0,o.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(t)),()=>{r((0,i.hq)(void 0))}),[t,r,l]),null}},9095:(e,t,r)=>{"use strict";r.d(t,{E:()=>M});var n=r(2115),i=r(2596),a=r(6377),o=r(1643),l=r(788),c=r(6605),u=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,d=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,p={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},h=Object.keys(p);class y{static parse(e){var t,[,r,n]=null!=(t=d.exec(e))?t:[];return new y(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,a.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),h.includes(t)&&(this.num=e*p[t],this.unit="px")}}function v(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=u.exec(t))?r:[],o=y.parse(null!=n?n:""),l=y.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return"NaN";t=t.replace(u,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,d,p,h]=null!=(f=s.exec(t))?f:[],v=y.parse(null!=d?d:""),g=y.parse(null!=h?h:""),m="+"===p?v.add(g):v.subtract(g);if(m.isNaN())return"NaN";t=t.replace(s,m.toString())}return t}var g=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=g.exec(r));){var[,n]=t;r=r.replace(g,v(n))}return r}(t),t=v(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],x=["dx","dy","angle","className","breakAll"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=/[ \f\n\r\t\v\u2028\u2029]+/,P=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,a.uy)(t)||(i=r?t.toString().split(""):t.toString().split(j));var o=i.map(e=>({word:e,width:(0,c.P)(e,n).width})),l=r?0:(0,c.P)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(e){return null}},E=e=>[{words:(0,a.uy)(e)?[]:e.toString().split(j)}],A="#808080",M=(0,n.forwardRef)((e,t)=>{var r,{x:c=0,y:u=0,lineHeight:s="1em",capHeight:f="0.71em",scaleToFit:d=!1,textAnchor:p="start",verticalAnchor:h="end",fill:y=A}=e,v=O(e,b),g=(0,n.useMemo)(()=>(e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:l,maxLines:c}=e;if((t||r)&&!o.m.isSsr){var u=P({breakAll:l,children:n,style:i});if(!u)return E(n);var{wordsWithComputedWidth:s,spaceWidth:f}=u;return((e,t,r,n,i)=>{var o,{maxLines:l,children:c,style:u,breakAll:s}=e,f=(0,a.Et)(l),d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},p=d(t),h=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(p.length>l||h(p).width>Number(n)))return p;for(var y=e=>{var t=d(P({breakAll:s,style:u,children:c.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||h(t).width>Number(n),t]},v=0,g=c.length-1,m=0;v<=g&&m<=c.length-1;){var b=Math.floor((v+g)/2),[x,w]=y(b-1),[O]=y(b);if(x||O||(v=b+1),x&&O&&(g=b-1),!x&&O){o=w;break}m++}return o||p})({breakAll:l,children:n,maxLines:c,style:i},s,f,t,r)}return E(n)})({breakAll:v.breakAll,children:v.children,maxLines:v.maxLines,scaleToFit:d,style:v.style,width:v.width}),[v.breakAll,v.children,v.maxLines,d,v.style,v.width]),{dx:j,dy:M,angle:S,className:k,breakAll:_}=v,T=O(v,x);if(!(0,a.vh)(c)||!(0,a.vh)(u))return null;var C=c+((0,a.Et)(j)?j:0),D=u+((0,a.Et)(M)?M:0);switch(h){case"start":r=m("calc(".concat(f,")"));break;case"middle":r=m("calc(".concat((g.length-1)/2," * -").concat(s," + (").concat(f," / 2))"));break;default:r=m("calc(".concat(g.length-1," * -").concat(s,")"))}var N=[];if(d){var I=g[0].width,{width:z}=v;N.push("scale(".concat((0,a.Et)(z)?z/I:1,")"))}return S&&N.push("rotate(".concat(S,", ").concat(C,", ").concat(D,")")),N.length&&(T.transform=N.join(" ")),n.createElement("text",w({},(0,l.J9)(T,!0),{ref:t,x:C,y:D,className:(0,i.$)("recharts-text",k),textAnchor:p,fill:y.includes("url")?A:y}),g.map((e,t)=>{var i=e.words.join(_?"":" ");return n.createElement("tspan",{x:C,dy:0===t?r:s,key:"".concat(i,"-").concat(t)},i)}))});M.displayName="Text"},9129:(e,t,r)=>{"use strict";r.d(t,{Cj:()=>a,Pg:()=>o,Ub:()=>l});var n=r(1971),i=r(4890),a=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.RD)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},o=e=>{var t=(0,n.j)();return(r,n)=>a=>{null==e||e(r,n,a),t((0,i.oP)())}},l=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.ML)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},9226:(e,t,r)=>{"use strict";r.d(t,{G9:()=>f,_S:()=>d,pU:()=>p,zk:()=>s});var n=r(2115),i=r(7119),a=r(1807),o=["children"],l=()=>{},c=(0,n.createContext)({addErrorBar:l,removeErrorBar:l}),u=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function s(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o);return n.createElement(u.Provider,{value:r},t)}var f=()=>(0,n.useContext)(u),d=e=>{var{children:t,xAxisId:r,yAxisId:o,zAxisId:l,dataKey:u,data:s,stackId:f,hide:d,type:p,barSize:h}=e,[y,v]=n.useState([]),g=(0,n.useCallback)(e=>{v(t=>[...t,e])},[v]),m=(0,n.useCallback)(e=>{v(t=>t.filter(t=>t!==e))},[v]),b=(0,a.r)();return n.createElement(c.Provider,{value:{addErrorBar:g,removeErrorBar:m}},n.createElement(i.p,{type:p,data:s,xAxisId:r,yAxisId:o,zAxisId:l,dataKey:u,errorBars:y,stackId:f,hide:d,barSize:h,isPanorama:b}),t)};function p(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(c);return(0,n.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}},9279:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},9426:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var n=r(2115),i=r(6377);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.NF)(t)),a=(0,n.useRef)(e);return a.current!==e&&(r.current=(0,i.NF)(t),a.current=e),r.current}},9452:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},9584:(e,t,r)=>{"use strict";r.d(t,{u:()=>O});var n=r(2115),i=r(5672),a=r.n(i),o=r(2596);function l(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var c=r(2348),u=r(9095),s=r(379),f=r(6377),d=r(3597),p=r(788),h=r(9035),y=["viewBox"],v=["viewBox"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=x(e,y),i=this.props,{viewBox:a}=i,o=x(i,v);return!l(r,a)||!l(n,o)||!l(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:u,height:s,orientation:d,tickSize:p,mirror:h,tickMargin:y}=this.props,v=h?-1:1,g=e.tickSize||p,m=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=r=e.coordinate,o=(n=(i=c+!h*s)-v*g)-v*y,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=l+!h*u)-v*g)-v*y,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=l+h*u)+v*g)+v*y,o=m;break;default:t=r=e.coordinate,o=(n=(i=c+h*s)+v*g)+v*y,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:l,mirror:c,axisLine:u}=this.props,s=b(b(b({},(0,p.J9)(this.props,!1)),(0,p.J9)(u,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!c||"bottom"===l&&c);s=b(b({},s),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var d=+("left"===l&&!c||"right"===l&&c);s=b(b({},s),{},{x1:e+d*r,y1:t,x2:e+d*r,y2:t+i})}return n.createElement("line",g({},s,{className:(0,o.$)("recharts-cartesian-axis-line",a()(u,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,b(b({},t),{},{className:a}));else if("function"==typeof e)i=e(b(b({},t),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,o.$)(l,e.className)),i=n.createElement(u.E,g({},t,{className:l}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:u,tickFormatter:s,unit:f}=this.props,y=(0,h.f)(b(b({},this.props),{},{ticks:r}),e,t),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),x=(0,p.J9)(this.props,!1),w=(0,p.J9)(u,!1),j=b(b({},x),{},{fill:"none"},(0,p.J9)(i,!1)),P=y.map((e,t)=>{var{line:r,tick:p}=this.getTickLineCoord(e),h=b(b(b(b({textAnchor:v,verticalAnchor:m},x),{},{stroke:"none",fill:l},w),p),{},{index:t,payload:e,visibleTicksCount:y.length,tickFormatter:s});return n.createElement(c.W,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,d.XC)(this.props,e,t)),i&&n.createElement("line",g({},j,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),u&&O.renderTickItem(u,h,"".concat("function"==typeof s?s(e.value,t):e.value).concat(f||"")))});return P.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},P):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(c.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),s.J.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}w(O,"displayName","CartesianAxis"),w(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},9738:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4117),i=r(2721);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let c=t?.(r,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},9819:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},9827:(e,t,r)=>{"use strict";r.d(t,{qx:()=>D,IH:()=>C,s0:()=>b,gH:()=>m,SW:()=>$,YB:()=>j,bk:()=>R,Hj:()=>N,DW:()=>_,y2:()=>k,nb:()=>S,PW:()=>w,Mk:()=>T,$8:()=>M,yy:()=>A,Rh:()=>O,GF:()=>I,uM:()=>z,kr:()=>g,r4:()=>L,_L:()=>x,_f:()=>P});var n=r(241),i=r.n(n),a=r(5672),o=r.n(a);function l(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var c=r(9819),u=r(5654);function s(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function d(e){let t=[];return t.key=e,t}var p=r(6377),h=r(5641);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e,t,r){return(0,p.uy)(e)||(0,p.uy)(t)?r:(0,p.vh)(t)?o()(e,t,r):"function"==typeof t?t(e):r}var m=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var c=0;c<l;c++){var u=c>0?r[c-1].coordinate:r[l-1].coordinate,s=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,d=void 0;if((0,p.sA)(s-u)!==(0,p.sA)(f-s)){var h=[];if((0,p.sA)(f-s)===(0,p.sA)(i[1]-i[0])){d=f;var y=s+i[1]-i[0];h[0]=Math.min(y,(y+u)/2),h[1]=Math.max(y,(y+u)/2)}else{d=u;var v=f+i[1]-i[0];h[0]=Math.min(s,(v+s)/2),h[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(d+s)/2),Math.max(s,(d+s)/2)];if(e>g[0]&&e<=g[1]||e>=h[0]&&e<=h[1]){({index:o}=r[c]);break}}else{var m=Math.min(u,f),b=Math.max(u,f);if(e>(m+s)/2&&e<=(b+s)/2){({index:o}=r[c]);break}}}else if(t){for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}}return o},b=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,p.Et)(e[a]))return v(v({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,p.Et)(e[o]))return v(v({},e),{},{[o]:e[o]+(i||0)})}return e},x=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,w=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},O=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:u,tickCount:s,ticks:f,niceTicks:d,axisType:h}=e;if(!o)return null;var y="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,v=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return(v="angleAxis"===h&&a&&a.length>=2?2*(0,p.sA)(a[0]-a[1])*v:v,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+v,value:e,offset:v,index:t})).filter(e=>!(0,p.M8)(e.coordinate)):c&&u?u.map((e,t)=>({coordinate:o(e)+v,value:e,index:t,offset:v})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+v,value:e,offset:v,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+v,value:n?n[e]:e,index:t,offset:v}))},j=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},P=(e,t)=>{if(!t||2!==t.length||!(0,p.Et)(t[0])||!(0,p.Et)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,p.Et)(e[0])||e[0]<r)&&(i[0]=r),(!(0,p.Et)(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},E={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=(0,p.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,c=0;o<r;++o)c+=e[o][n][1]||0;i[n][1]+=i[n][0]=-c/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var c=0,u=0,s=0;c<i;++c){for(var f=e[t[c]],d=f[o][1]||0,p=(d-(f[o-1][1]||0))/2,h=0;h<c;++h){var y=e[t[h]];p+=(y[o][1]||0)-(y[o-1][1]||0)}u+=d,s+=p*d}r[o-1][1]+=r[o-1][0]=a,u&&(a-=s/u)}r[o-1][1]+=r[o-1][0]=a,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,p.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},A=(e,t,r)=>{var n=E[r];return(function(){var e=(0,u.A)([]),t=s,r=l,n=f;function i(i){var a,o,l=Array.from(e.apply(this,arguments),d),u=l.length,s=-1;for(let e of i)for(a=0,++s;a<u;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=(0,c.A)(t(l));a<u;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,u.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,u.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?s:"function"==typeof e?e:(0,u.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+g(e,t,0)).order(s).offset(n)(e)};function M(e){return null==e?void 0:String(e)}function S(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!(0,p.uy)(i[t.dataKey])){var l=(0,p.eP)(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=g(i,(0,p.uy)(o)?t.dataKey:o);return(0,p.uy)(c)?null:t.scale(c)}var k=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=g(a,t.dataKey,t.scale.domain()[o]);return(0,p.uy)(l)?null:t.scale(l)-i/2+n},_=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},T=(e,t,r)=>{if(null!=e)return(e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]])(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=(e=>{var t=e.flat(2).filter(p.Et);return[Math.min(...t),Math.max(...t)]})(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},C=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,D=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,N=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,l=1,c=a.length;l<c;l++){var u=a[l],s=a[l-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function I(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return v(v({},t),{},{dataKey:r,payload:n,value:i,name:a})}function z(e,t){return e?String(e):"string"==typeof t?t:void 0}function L(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,h.yy)({x:e,y:t},n):null}var R=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return v(v(v({},n),(0,h.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return v(v(v({},n),(0,h.IZ)(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}},$=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},9901:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(4373),i=r(4664);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},9972:(e,t,r)=>{"use strict";r.d(t,{J:()=>J});var n=r(2115);r(1992);var i={notify(){},get:()=>[]},a="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,o="undefined"!=typeof navigator&&"ReactNative"===navigator.product,l=a||o?n.useLayoutEffect:n.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var c=Symbol.for("react-redux-context"),u="undefined"!=typeof globalThis?globalThis:{},s=function(){if(!n.createContext)return{};let e=u[c]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),f=function(e){let{children:t,context:r,serverState:a,store:o}=e,c=n.useMemo(()=>{let e=function(e,t){let r,n=i,a=0,o=!1;function l(){s.onStateChange&&s.onStateChange()}function c(){if(a++,!r){let t,i;r=e.subscribe(l),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function u(){a--,r&&0===a&&(r(),r=void 0,n.clear(),n=i)}let s={addNestedSub:function(e){c();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),u())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:l,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,c())},tryUnsubscribe:function(){o&&(o=!1,u())},getListeners:()=>n};return s}(o);return{store:o,subscription:e,getServerState:a?()=>a:void 0}},[o,a]),u=n.useMemo(()=>o.getState(),[o]);return l(()=>{let{subscription:e}=c;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==o.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[c,u]),n.createElement((r||s).Provider,{value:c},t)},d=r(52),p=r(5710),h=r(6641),y=r(4890),v=r(4487),g=r(8627),m=r(4685);function b(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var x=r(5306),w=r(2248),O=r(4532),j=(0,p.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,O.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,O.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,O.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:P,removeDot:E,addArea:A,removeArea:M,addLine:S,removeLine:k}=j.actions,_=j.reducer,T={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},C=(0,p.Z0)({name:"brush",initialState:T,reducers:{setBrushSettings:(e,t)=>null==t.payload?T:t.payload}}),{setBrushSettings:D}=C.actions,N=C.reducer,I=r(2634),z=r(429),L=(0,p.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,O.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,O.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:R,removeRadiusAxis:$,addAngleAxis:B,removeAngleAxis:U}=L.actions,F=L.reducer,K=r(2267),H=r(3222),G=r(7335),Z=r(6144),W=(0,d.HY)({brush:N,cartesianAxis:x.CA,chartData:v.LV,graphicalItems:w.iZ,layout:g.Vp,legend:I.CU,options:h.lJ,polarAxis:F,polarOptions:K.J,referenceElements:_,rootProps:z.vE,tooltip:y.En}),V=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,p.U1)({reducer:W,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([m.YF.middleware,m.fP.middleware,H.$7.middleware,G.x.middleware,Z.k.middleware]),devTools:{serialize:{replacer:b},name:"recharts-".concat(t)}})},q=r(1807),Y=r(5064);function J(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,a=(0,q.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=V(t,i));var l=Y.E;return n.createElement(f,{context:l,store:o.current},r)}}}]);