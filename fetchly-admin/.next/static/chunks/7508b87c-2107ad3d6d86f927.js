"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[135],{9745:(e,t,r)=>{r.d(t,{H9:()=>sN,aU:()=>sx,x7:()=>sG});var n,s,i,a,o=r(1055),l=r(2881),u=r(6702),h=r(1280),c=r(2107),d=r(927);r(9509);var m=r(9641).Buffer;let f="@firebase/firestore",g="4.9.0";class p{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}p.UNAUTHENTICATED=new p(null),p.GOOGLE_CREDENTIALS=new p("google-credentials-uid"),p.FIRST_PARTY=new p("first-party-uid"),p.MOCK_USER=new p("mock-user");let y="12.0.0",v=new u.Vy("@firebase/firestore");function w(){return v.logLevel}function E(e,...t){if(v.logLevel<=u.$b.DEBUG){let r=t.map(C);v.debug(`Firestore (${y}): ${e}`,...r)}}function _(e,...t){if(v.logLevel<=u.$b.ERROR){let r=t.map(C);v.error(`Firestore (${y}): ${e}`,...r)}}function T(e,...t){if(v.logLevel<=u.$b.WARN){let r=t.map(C);v.warn(`Firestore (${y}): ${e}`,...r)}}function C(e){if("string"==typeof e)return e;try{return JSON.stringify(e)}catch(t){return e}}function S(e,t,r){let n="Unexpected state";"string"==typeof t?n=t:r=t,I(e,n,r)}function I(e,t,r){let n=`FIRESTORE (${y}) INTERNAL ASSERTION FAILED: ${t} (ID: ${e.toString(16)})`;if(void 0!==r)try{n+=" CONTEXT: "+JSON.stringify(r)}catch(e){n+=" CONTEXT: "+r}throw _(n),Error(n)}function N(e,t,r,n){let s="Unexpected state";"string"==typeof r?s=r:n=r,e||I(t,s,n)}let A={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class b extends h.g{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}}class k{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}class D{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${e}`)}}class x{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable(()=>t(p.UNAUTHENTICATED))}shutdown(){}}class R{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable(()=>t(this.token.user))}shutdown(){this.changeListener=null}}class V{constructor(e){this.t=e,this.currentUser=p.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(e,t){N(void 0===this.o,42304);let r=this.i,n=e=>this.i!==r?(r=this.i,t(e)):Promise.resolve(),s=new k;this.o=()=>{this.i++,this.currentUser=this.u(),s.resolve(),s=new k,e.enqueueRetryable(()=>n(this.currentUser))};let i=()=>{let t=s;e.enqueueRetryable(async()=>{await t.promise,await n(this.currentUser)})},a=e=>{E("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=e,this.o&&(this.auth.addAuthTokenListener(this.o),i())};this.t.onInit(e=>a(e)),setTimeout(()=>{if(!this.auth){let e=this.t.getImmediate({optional:!0});e?a(e):(E("FirebaseAuthCredentialsProvider","Auth not yet detected"),s.resolve(),s=new k)}},0),i()}getToken(){let e=this.i,t=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(t).then(t=>this.i!==e?(E("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):t?(N("string"==typeof t.accessToken,31837,{l:t}),new D(t.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){let e=this.auth&&this.auth.getUid();return N(null===e||"string"==typeof e,2055,{h:e}),new p(e)}}class O{constructor(e,t,r){this.P=e,this.T=t,this.I=r,this.type="FirstParty",this.user=p.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);let e=this.R();return e&&this.A.set("Authorization",e),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}}class L{constructor(e,t,r){this.P=e,this.T=t,this.I=r}getToken(){return Promise.resolve(new O(this.P,this.T,this.I))}start(e,t){e.enqueueRetryable(()=>t(p.FIRST_PARTY))}shutdown(){}invalidateToken(){}}class M{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&e.length>0&&this.headers.set("x-firebase-appcheck",this.value)}}class P{constructor(e,t){this.V=t,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,(0,o.xZ)(e)&&e.settings.appCheckToken&&(this.p=e.settings.appCheckToken)}start(e,t){N(void 0===this.o,3512);let r=e=>{null!=e.error&&E("FirebaseAppCheckTokenProvider",`Error getting App Check token; using placeholder token instead. Error: ${e.error.message}`);let r=e.token!==this.m;return this.m=e.token,E("FirebaseAppCheckTokenProvider",`Received ${r?"new":"existing"} token.`),r?t(e.token):Promise.resolve()};this.o=t=>{e.enqueueRetryable(()=>r(t))};let n=e=>{E("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=e,this.o&&this.appCheck.addTokenListener(this.o)};this.V.onInit(e=>n(e)),setTimeout(()=>{if(!this.appCheck){let e=this.V.getImmediate({optional:!0});e?n(e):E("FirebaseAppCheckTokenProvider","AppCheck not yet detected")}},0)}getToken(){if(this.p)return Promise.resolve(new M(this.p));let e=this.forceRefresh;return this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then(e=>e?(N("string"==typeof e.token,44558,{tokenResult:e}),this.m=e.token,new M(e.token)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}}class F{static newId(){let e=62*Math.floor(256/62),t="";for(;t.length<20;){let r=function(e){let t="undefined"!=typeof self&&(self.crypto||self.msCrypto),r=new Uint8Array(40);if(t&&"function"==typeof t.getRandomValues)t.getRandomValues(r);else for(let e=0;e<40;e++)r[e]=Math.floor(256*Math.random());return r}(40);for(let n=0;n<r.length;++n)t.length<20&&r[n]<e&&(t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(r[n]%62))}return t}}function U(e,t){return e<t?-1:+(e>t)}function q(e,t){let r=Math.min(e.length,t.length);for(let n=0;n<r;n++){let r=e.charAt(n),s=t.charAt(n);if(r!==s)return z(r)===z(s)?U(r,s):z(r)?1:-1}return U(e.length,t.length)}function z(e){let t=e.charCodeAt(0);return t>=55296&&t<=57343}function $(e,t,r){return e.length===t.length&&e.every((e,n)=>r(e,t[n]))}let B="__name__";class j{constructor(e,t,r){void 0===t?t=0:t>e.length&&S(637,{offset:t,range:e.length}),void 0===r?r=e.length-t:r>e.length-t&&S(1746,{length:r,range:e.length-t}),this.segments=e,this.offset=t,this.len=r}get length(){return this.len}isEqual(e){return 0===j.comparator(this,e)}child(e){let t=this.segments.slice(this.offset,this.limit());return e instanceof j?e.forEach(e=>{t.push(e)}):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return e=void 0===e?1:e,this.construct(this.segments,this.offset+e,this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,r=this.limit();t<r;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(e,t){let r=Math.min(e.length,t.length);for(let n=0;n<r;n++){let r=j.compareSegments(e.get(n),t.get(n));if(0!==r)return r}return U(e.length,t.length)}static compareSegments(e,t){let r=j.isNumericId(e),n=j.isNumericId(t);return r&&!n?-1:!r&&n?1:r&&n?j.extractNumericId(e).compare(j.extractNumericId(t)):q(e,t)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return c.jz.fromString(e.substring(4,e.length-2))}}class K extends j{construct(e,t,r){return new K(e,t,r)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){let t=[];for(let r of e){if(r.indexOf("//")>=0)throw new b(A.INVALID_ARGUMENT,`Invalid segment (${r}). Paths must not contain // in them.`);t.push(...r.split("/").filter(e=>e.length>0))}return new K(t)}static emptyPath(){return new K([])}}let G=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class Q extends j{construct(e,t,r){return new Q(e,t,r)}static isValidIdentifier(e){return G.test(e)}canonicalString(){return this.toArray().map(e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),Q.isValidIdentifier(e)||(e="`"+e+"`"),e)).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&this.get(0)===B}static keyField(){return new Q([B])}static fromServerFormat(e){let t=[],r="",n=0,s=()=>{if(0===r.length)throw new b(A.INVALID_ARGUMENT,`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);t.push(r),r=""},i=!1;for(;n<e.length;){let t=e[n];if("\\"===t){if(n+1===e.length)throw new b(A.INVALID_ARGUMENT,"Path has trailing escape character: "+e);let t=e[n+1];if("\\"!==t&&"."!==t&&"`"!==t)throw new b(A.INVALID_ARGUMENT,"Path has invalid escape sequence: "+e);r+=t,n+=2}else"`"===t?i=!i:"."!==t||i?r+=t:s(),n++}if(s(),i)throw new b(A.INVALID_ARGUMENT,"Unterminated ` in path: "+e);return new Q(t)}static emptyPath(){return new Q([])}}class H{constructor(e){this.path=e}static fromPath(e){return new H(K.fromString(e))}static fromName(e){return new H(K.fromString(e).popFirst(5))}static empty(){return new H(K.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return this.path.length>=2&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===K.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,t){return K.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new H(new K(e.slice()))}}function W(e){if(!H.isDocumentKey(e))throw new b(A.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function Y(e,t){if("_delegate"in e&&(e=e._delegate),!(e instanceof t)){if(t.name===e.constructor.name)throw new b(A.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{let r=function(e){if(void 0===e)return"undefined";if(null===e)return"null";if("string"==typeof e)return e.length>20&&(e=`${e.substring(0,20)}...`),JSON.stringify(e);if("number"==typeof e||"boolean"==typeof e)return""+e;if("object"==typeof e){if(e instanceof Array)return"an array";{var t;let r=(t=e).constructor?t.constructor.name:null;return r?`a custom ${r} object`:"an object"}}return"function"==typeof e?"a function":S(12329,{type:typeof e})}(e);throw new b(A.INVALID_ARGUMENT,`Expected type '${t.name}', but it was: ${r}`)}}return e}function X(e,t){let r={typeString:e};return t&&(r.value=t),r}function J(e,t){let r;if("object"!=typeof e||null===e||Object.getPrototypeOf(e)!==Object.prototype&&null!==Object.getPrototypeOf(e))throw new b(A.INVALID_ARGUMENT,"JSON must be an object");for(let n in t)if(t[n]){let s=t[n].typeString,i="value"in t[n]?{value:t[n].value}:void 0;if(!(n in e)){r=`JSON missing required field: '${n}'`;break}let a=e[n];if(s&&typeof a!==s){r=`JSON field '${n}' must be a ${s}.`;break}if(void 0!==i&&a!==i.value){r=`Expected '${n}' field to equal '${i.value}'`;break}}if(r)throw new b(A.INVALID_ARGUMENT,r);return!0}class Z{static now(){return Z.fromMillis(Date.now())}static fromDate(e){return Z.fromMillis(e.getTime())}static fromMillis(e){let t=Math.floor(e/1e3),r=Math.floor((e-1e3*t)*1e6);return new Z(t,r)}constructor(e,t){if(this.seconds=e,this.nanoseconds=t,t<0||t>=1e9)throw new b(A.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<-0xe7791f700||e>=0x3afff44180)throw new b(A.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/1e6}_compareTo(e){return this.seconds===e.seconds?U(this.nanoseconds,e.nanoseconds):U(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{type:Z._jsonSchemaVersion,seconds:this.seconds,nanoseconds:this.nanoseconds}}static fromJSON(e){if(J(e,Z._jsonSchema))return new Z(e.seconds,e.nanoseconds)}valueOf(){return String(this.seconds- -0xe7791f700).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}Z._jsonSchemaVersion="firestore/timestamp/1.0",Z._jsonSchema={type:X("string",Z._jsonSchemaVersion),seconds:X("number"),nanoseconds:X("number")};class ee{static fromTimestamp(e){return new ee(e)}static min(){return new ee(new Z(0,0))}static max(){return new ee(new Z(0x3afff4417f,0x3b9ac9ff))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}class et{constructor(e,t,r,n){this.indexId=e,this.collectionGroup=t,this.fields=r,this.indexState=n}}et.UNKNOWN_ID=-1;class er{constructor(e,t,r){this.readTime=e,this.documentKey=t,this.largestBatchId=r}static min(){return new er(ee.min(),H.empty(),-1)}static max(){return new er(ee.max(),H.empty(),-1)}}class en{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach(e=>e())}}async function es(e){if(e.code!==A.FAILED_PRECONDITION||"The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab."!==e.message)throw e;E("LocalStore","Unexpectedly lost primary lease")}class ei{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e(e=>{this.isDone=!0,this.result=e,this.nextCallback&&this.nextCallback(e)},e=>{this.isDone=!0,this.error=e,this.catchCallback&&this.catchCallback(e)})}catch(e){return this.next(void 0,e)}next(e,t){return this.callbackAttached&&S(59440),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(t,this.error):this.wrapSuccess(e,this.result):new ei((r,n)=>{this.nextCallback=t=>{this.wrapSuccess(e,t).next(r,n)},this.catchCallback=e=>{this.wrapFailure(t,e).next(r,n)}})}toPromise(){return new Promise((e,t)=>{this.next(e,t)})}wrapUserFunction(e){try{let t=e();return t instanceof ei?t:ei.resolve(t)}catch(e){return ei.reject(e)}}wrapSuccess(e,t){return e?this.wrapUserFunction(()=>e(t)):ei.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction(()=>e(t)):ei.reject(t)}static resolve(e){return new ei((t,r)=>{t(e)})}static reject(e){return new ei((t,r)=>{r(e)})}static waitFor(e){return new ei((t,r)=>{let n=0,s=0,i=!1;e.forEach(e=>{++n,e.next(()=>{++s,i&&s===n&&t()},e=>r(e))}),i=!0,s===n&&t()})}static or(e){let t=ei.resolve(!1);for(let r of e)t=t.next(e=>e?ei.resolve(e):r());return t}static forEach(e,t){let r=[];return e.forEach((e,n)=>{r.push(t.call(this,e,n))}),this.waitFor(r)}static mapArray(e,t){return new ei((r,n)=>{let s=e.length,i=Array(s),a=0;for(let o=0;o<s;o++){let l=o;t(e[l]).next(e=>{i[l]=e,++a===s&&r(i)},e=>n(e))}})}static doWhile(e,t){return new ei((r,n)=>{let s=()=>{!0===e()?t().next(()=>{s()},n):r()};s()})}}function ea(e){return"IndexedDbTransactionError"===e.name}class eo{constructor(e,t){this.previousValue=e,t&&(t.sequenceNumberHandler=e=>this.ae(e),this.ue=e=>t.writeSequenceNumber(e))}ae(e){return this.previousValue=Math.max(e,this.previousValue),this.previousValue}next(){let e=++this.previousValue;return this.ue&&this.ue(e),e}}eo.ce=-1;function el(e){return 0===e&&1/e==-1/0}function eu(e){let t=0;for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t++;return t}function eh(e,t){for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t(r,e[r])}class ec{constructor(e,t){this.comparator=e,this.root=t||em.EMPTY}insert(e,t){return new ec(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,em.BLACK,null,null))}remove(e){return new ec(this.comparator,this.root.remove(e,this.comparator).copy(null,null,em.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){let r=this.comparator(e,t.key);if(0===r)return t.value;r<0?t=t.left:r>0&&(t=t.right)}return null}indexOf(e){let t=0,r=this.root;for(;!r.isEmpty();){let n=this.comparator(e,r.key);if(0===n)return t+r.left.size;n<0?r=r.left:(t+=r.left.size+1,r=r.right)}return -1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(e){this.inorderTraversal((t,r)=>(e(t,r),!1))}toString(){let e=[];return this.inorderTraversal((t,r)=>(e.push(`${t}:${r}`),!1)),`{${e.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new ed(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new ed(this.root,e,this.comparator,!1)}getReverseIterator(){return new ed(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new ed(this.root,e,this.comparator,!0)}}class ed{constructor(e,t,r,n){this.isReverse=n,this.nodeStack=[];let s=1;for(;!e.isEmpty();)if(s=t?r(e.key,t):1,t&&n&&(s*=-1),s<0)e=this.isReverse?e.left:e.right;else{if(0===s){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop(),t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return this.nodeStack.length>0}peek(){if(0===this.nodeStack.length)return null;let e=this.nodeStack[this.nodeStack.length-1];return{key:e.key,value:e.value}}}class em{constructor(e,t,r,n,s){this.key=e,this.value=t,this.color=null!=r?r:em.RED,this.left=null!=n?n:em.EMPTY,this.right=null!=s?s:em.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,r,n,s){return new em(null!=e?e:this.key,null!=t?t:this.value,null!=r?r:this.color,null!=n?n:this.left,null!=s?s:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,r){let n=this,s=r(e,n.key);return(n=s<0?n.copy(null,null,null,n.left.insert(e,t,r),null):0===s?n.copy(null,t,null,null,null):n.copy(null,null,null,null,n.right.insert(e,t,r))).fixUp()}removeMin(){if(this.left.isEmpty())return em.EMPTY;let e=this;return e.left.isRed()||e.left.left.isRed()||(e=e.moveRedLeft()),(e=e.copy(null,null,null,e.left.removeMin(),null)).fixUp()}remove(e,t){let r,n=this;if(0>t(e,n.key))n.left.isEmpty()||n.left.isRed()||n.left.left.isRed()||(n=n.moveRedLeft()),n=n.copy(null,null,null,n.left.remove(e,t),null);else{if(n.left.isRed()&&(n=n.rotateRight()),n.right.isEmpty()||n.right.isRed()||n.right.left.isRed()||(n=n.moveRedRight()),0===t(e,n.key)){if(n.right.isEmpty())return em.EMPTY;r=n.right.min(),n=n.copy(r.key,r.value,null,null,n.right.removeMin())}n=n.copy(null,null,null,null,n.right.remove(e,t))}return n.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e.right.isRed()&&!e.left.isRed()&&(e=e.rotateLeft()),e.left.isRed()&&e.left.left.isRed()&&(e=e.rotateRight()),e.left.isRed()&&e.right.isRed()&&(e=e.colorFlip()),e}moveRedLeft(){let e=this.colorFlip();return e.right.left.isRed()&&(e=(e=(e=e.copy(null,null,null,null,e.right.rotateRight())).rotateLeft()).colorFlip()),e}moveRedRight(){let e=this.colorFlip();return e.left.left.isRed()&&(e=(e=e.rotateRight()).colorFlip()),e}rotateLeft(){let e=this.copy(null,null,em.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){let e=this.copy(null,null,em.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){let e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){return Math.pow(2,this.check())<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw S(43730,{key:this.key,value:this.value});if(this.right.isRed())throw S(14113,{key:this.key,value:this.value});let e=this.left.check();if(e!==this.right.check())throw S(27949);return e+ +!this.isRed()}}em.EMPTY=null,em.RED=!0,em.BLACK=!1,em.EMPTY=new class{constructor(){this.size=0}get key(){throw S(57766)}get value(){throw S(16141)}get color(){throw S(16727)}get left(){throw S(29726)}get right(){throw S(36894)}copy(e,t,r,n,s){return this}insert(e,t,r){return new em(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class ef{constructor(e){this.comparator=e,this.data=new ec(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(e){this.data.inorderTraversal((t,r)=>(e(t),!1))}forEachInRange(e,t){let r=this.data.getIteratorFrom(e[0]);for(;r.hasNext();){let n=r.getNext();if(this.comparator(n.key,e[1])>=0)return;t(n.key)}}forEachWhile(e,t){let r;for(r=void 0!==t?this.data.getIteratorFrom(t):this.data.getIterator();r.hasNext();)if(!e(r.getNext().key))return}firstAfterOrEqual(e){let t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new eg(this.data.getIterator())}getIteratorFrom(e){return new eg(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach(e=>{t=t.add(e)}),t}isEqual(e){if(!(e instanceof ef)||this.size!==e.size)return!1;let t=this.data.getIterator(),r=e.data.getIterator();for(;t.hasNext();){let e=t.getNext().key,n=r.getNext().key;if(0!==this.comparator(e,n))return!1}return!0}toArray(){let e=[];return this.forEach(t=>{e.push(t)}),e}toString(){let e=[];return this.forEach(t=>e.push(t)),"SortedSet("+e.toString()+")"}copy(e){let t=new ef(this.comparator);return t.data=e,t}}class eg{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}class ep{constructor(e){this.fields=e,e.sort(Q.comparator)}static empty(){return new ep([])}unionWith(e){let t=new ef(Q.comparator);for(let e of this.fields)t=t.add(e);for(let r of e)t=t.add(r);return new ep(t.toArray())}covers(e){for(let t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return $(this.fields,e.fields,(e,t)=>e.isEqual(t))}}class ey extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class ev{constructor(e){this.binaryString=e}static fromBase64String(e){return new ev(function(e){try{return atob(e)}catch(e){throw"undefined"!=typeof DOMException&&e instanceof DOMException?new ey("Invalid base64 string: "+e):e}}(e))}static fromUint8Array(e){return new ev(function(e){let t="";for(let r=0;r<e.length;++r)t+=String.fromCharCode(e[r]);return t}(e))}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return btoa(this.binaryString)}toUint8Array(){var e=this.binaryString;let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return U(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}ev.EMPTY_BYTE_STRING=new ev("");let ew=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function eE(e){if(N(!!e,39018),"string"==typeof e){let t=0,r=ew.exec(e);if(N(!!r,46558,{timestamp:e}),r[1]){let e=r[1];t=Number(e=(e+"000000000").substr(0,9))}return{seconds:Math.floor(new Date(e).getTime()/1e3),nanos:t}}return{seconds:e_(e.seconds),nanos:e_(e.nanos)}}function e_(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function eT(e){return"string"==typeof e?ev.fromBase64String(e):ev.fromUint8Array(e)}let eC="server_timestamp",eS="__type__",eI="__previous_value__",eN="__local_write_time__";function eA(e){return(e?.mapValue?.fields||{})[eS]?.stringValue===eC}function eb(e){let t=e.mapValue.fields[eI];return eA(t)?eb(t):t}function ek(e){let t=eE(e.mapValue.fields[eN].timestampValue);return new Z(t.seconds,t.nanos)}class eD{constructor(e,t,r,n,s,i,a,o,l,u){this.databaseId=e,this.appId=t,this.persistenceKey=r,this.host=n,this.ssl=s,this.forceLongPolling=i,this.autoDetectLongPolling=a,this.longPollingOptions=o,this.useFetchStreams=l,this.isUsingEmulator=u}}let ex="(default)";class eR{constructor(e,t){this.projectId=e,this.database=t||ex}static empty(){return new eR("","")}get isDefaultDatabase(){return this.database===ex}isEqual(e){return e instanceof eR&&e.projectId===this.projectId&&e.database===this.database}}let eV="__max__",eO={mapValue:{fields:{__type__:{stringValue:eV}}}},eL="value";function eM(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?eA(e)?4:eY(e)?0x1fffffffffffff:eH(e)?10:11:S(28295,{value:e})}function eP(e,t){if(e===t)return!0;let r=eM(e);if(r!==eM(t))return!1;switch(r){case 0:case 0x1fffffffffffff:return!0;case 1:return e.booleanValue===t.booleanValue;case 4:return ek(e).isEqual(ek(t));case 3:if("string"==typeof e.timestampValue&&"string"==typeof t.timestampValue&&e.timestampValue.length===t.timestampValue.length)return e.timestampValue===t.timestampValue;let n=eE(e.timestampValue),s=eE(t.timestampValue);return n.seconds===s.seconds&&n.nanos===s.nanos;case 5:return e.stringValue===t.stringValue;case 6:return eT(e.bytesValue).isEqual(eT(t.bytesValue));case 7:return e.referenceValue===t.referenceValue;case 8:return e_(e.geoPointValue.latitude)===e_(t.geoPointValue.latitude)&&e_(e.geoPointValue.longitude)===e_(t.geoPointValue.longitude);case 2:if("integerValue"in e&&"integerValue"in t)return e_(e.integerValue)===e_(t.integerValue);if("doubleValue"in e&&"doubleValue"in t){let r=e_(e.doubleValue),n=e_(t.doubleValue);return r===n?el(r)===el(n):isNaN(r)&&isNaN(n)}return!1;case 9:return $(e.arrayValue.values||[],t.arrayValue.values||[],eP);case 10:case 11:let i=e.mapValue.fields||{},a=t.mapValue.fields||{};if(eu(i)!==eu(a))return!1;for(let e in i)if(i.hasOwnProperty(e)&&(void 0===a[e]||!eP(i[e],a[e])))return!1;return!0;default:return S(52216,{left:e})}}function eF(e,t){return void 0!==(e.values||[]).find(e=>eP(e,t))}function eU(e,t){if(e===t)return 0;let r=eM(e),n=eM(t);if(r!==n)return U(r,n);switch(r){case 0:case 0x1fffffffffffff:return 0;case 1:return U(e.booleanValue,t.booleanValue);case 2:let s=e_(e.integerValue||e.doubleValue),i=e_(t.integerValue||t.doubleValue);return s<i?-1:s>i?1:s===i?0:isNaN(s)?isNaN(i)?0:-1:1;case 3:return eq(e.timestampValue,t.timestampValue);case 4:return eq(ek(e),ek(t));case 5:return q(e.stringValue,t.stringValue);case 6:return function(e,t){let r=eT(e),n=eT(t);return r.compareTo(n)}(e.bytesValue,t.bytesValue);case 7:return function(e,t){let r=e.split("/"),n=t.split("/");for(let e=0;e<r.length&&e<n.length;e++){let t=U(r[e],n[e]);if(0!==t)return t}return U(r.length,n.length)}(e.referenceValue,t.referenceValue);case 8:return function(e,t){let r=U(e_(e.latitude),e_(t.latitude));return 0!==r?r:U(e_(e.longitude),e_(t.longitude))}(e.geoPointValue,t.geoPointValue);case 9:return ez(e.arrayValue,t.arrayValue);case 10:return function(e,t){let r=e.fields||{},n=t.fields||{},s=r[eL]?.arrayValue,i=n[eL]?.arrayValue,a=U(s?.values?.length||0,i?.values?.length||0);return 0!==a?a:ez(s,i)}(e.mapValue,t.mapValue);case 11:return function(e,t){if(e===eO.mapValue&&t===eO.mapValue)return 0;if(e===eO.mapValue)return 1;if(t===eO.mapValue)return -1;let r=e.fields||{},n=Object.keys(r),s=t.fields||{},i=Object.keys(s);n.sort(),i.sort();for(let e=0;e<n.length&&e<i.length;++e){let t=q(n[e],i[e]);if(0!==t)return t;let a=eU(r[n[e]],s[i[e]]);if(0!==a)return a}return U(n.length,i.length)}(e.mapValue,t.mapValue);default:throw S(23264,{he:r})}}function eq(e,t){if("string"==typeof e&&"string"==typeof t&&e.length===t.length)return U(e,t);let r=eE(e),n=eE(t),s=U(r.seconds,n.seconds);return 0!==s?s:U(r.nanos,n.nanos)}function ez(e,t){let r=e.values||[],n=t.values||[];for(let e=0;e<r.length&&e<n.length;++e){let t=eU(r[e],n[e]);if(t)return t}return U(r.length,n.length)}function e$(e){var t,r;return"nullValue"in e?"null":"booleanValue"in e?""+e.booleanValue:"integerValue"in e?""+e.integerValue:"doubleValue"in e?""+e.doubleValue:"timestampValue"in e?function(e){let t=eE(e);return`time(${t.seconds},${t.nanos})`}(e.timestampValue):"stringValue"in e?e.stringValue:"bytesValue"in e?eT(e.bytesValue).toBase64():"referenceValue"in e?(t=e.referenceValue,H.fromName(t).toString()):"geoPointValue"in e?(r=e.geoPointValue,`geo(${r.latitude},${r.longitude})`):"arrayValue"in e?function(e){let t="[",r=!0;for(let n of e.values||[])r?r=!1:t+=",",t+=e$(n);return t+"]"}(e.arrayValue):"mapValue"in e?function(e){let t=Object.keys(e.fields||{}).sort(),r="{",n=!0;for(let s of t)n?n=!1:r+=",",r+=`${s}:${e$(e.fields[s])}`;return r+"}"}(e.mapValue):S(61005,{value:e})}function eB(e){return!!e&&"integerValue"in e}function ej(e){return!!e&&"arrayValue"in e}function eK(e){return!!e&&"nullValue"in e}function eG(e){return!!e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function eQ(e){return!!e&&"mapValue"in e}function eH(e){return(e?.mapValue?.fields||{}).__type__?.stringValue==="__vector__"}function eW(e){if(e.geoPointValue)return{geoPointValue:{...e.geoPointValue}};if(e.timestampValue&&"object"==typeof e.timestampValue)return{timestampValue:{...e.timestampValue}};if(e.mapValue){let t={mapValue:{fields:{}}};return eh(e.mapValue.fields,(e,r)=>t.mapValue.fields[e]=eW(r)),t}if(e.arrayValue){let t={arrayValue:{values:[]}};for(let r=0;r<(e.arrayValue.values||[]).length;++r)t.arrayValue.values[r]=eW(e.arrayValue.values[r]);return t}return{...e}}function eY(e){return(((e.mapValue||{}).fields||{}).__type__||{}).stringValue===eV}class eX{constructor(e){this.value=e}static empty(){return new eX({mapValue:{}})}field(e){if(e.isEmpty())return this.value;{let t=this.value;for(let r=0;r<e.length-1;++r)if(!eQ(t=(t.mapValue.fields||{})[e.get(r)]))return null;return(t=(t.mapValue.fields||{})[e.lastSegment()])||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=eW(t)}setAll(e){let t=Q.emptyPath(),r={},n=[];e.forEach((e,s)=>{if(!t.isImmediateParentOf(s)){let e=this.getFieldsMap(t);this.applyChanges(e,r,n),r={},n=[],t=s.popLast()}e?r[s.lastSegment()]=eW(e):n.push(s.lastSegment())});let s=this.getFieldsMap(t);this.applyChanges(s,r,n)}delete(e){let t=this.field(e.popLast());eQ(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return eP(this.value,e.value)}getFieldsMap(e){let t=this.value;t.mapValue.fields||(t.mapValue={fields:{}});for(let r=0;r<e.length;++r){let n=t.mapValue.fields[e.get(r)];eQ(n)&&n.mapValue.fields||(n={mapValue:{fields:{}}},t.mapValue.fields[e.get(r)]=n),t=n}return t.mapValue.fields}applyChanges(e,t,r){for(let n of(eh(t,(t,r)=>e[t]=r),r))delete e[n]}clone(){return new eX(eW(this.value))}}class eJ{constructor(e,t,r,n,s,i,a){this.key=e,this.documentType=t,this.version=r,this.readTime=n,this.createTime=s,this.data=i,this.documentState=a}static newInvalidDocument(e){return new eJ(e,0,ee.min(),ee.min(),ee.min(),eX.empty(),0)}static newFoundDocument(e,t,r,n){return new eJ(e,1,t,ee.min(),r,n,0)}static newNoDocument(e,t){return new eJ(e,2,t,ee.min(),ee.min(),eX.empty(),0)}static newUnknownDocument(e,t){return new eJ(e,3,t,ee.min(),ee.min(),eX.empty(),2)}convertToFoundDocument(e,t){return this.createTime.isEqual(ee.min())&&(2===this.documentType||0===this.documentType)&&(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=eX.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=eX.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=ee.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof eJ&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new eJ(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class eZ{constructor(e,t){this.position=e,this.inclusive=t}}function e0(e,t,r){let n=0;for(let s=0;s<e.position.length;s++){let i=t[s],a=e.position[s];if(n=i.field.isKeyField()?H.comparator(H.fromName(a.referenceValue),r.key):eU(a,r.data.field(i.field)),"desc"===i.dir&&(n*=-1),0!==n)break}return n}function e1(e,t){if(null===e)return null===t;if(null===t||e.inclusive!==t.inclusive||e.position.length!==t.position.length)return!1;for(let r=0;r<e.position.length;r++)if(!eP(e.position[r],t.position[r]))return!1;return!0}class e2{constructor(e,t="asc"){this.field=e,this.dir=t}}class e3{}class e4 extends e3{constructor(e,t,r){super(),this.field=e,this.op=t,this.value=r}static create(e,t,r){return e.isKeyField()?"in"===t||"not-in"===t?this.createKeyFieldInFilter(e,t,r):new e9(e,t,r):"array-contains"===t?new tr(e,r):"in"===t?new tn(e,r):"not-in"===t?new ts(e,r):"array-contains-any"===t?new ti(e,r):new e4(e,t,r)}static createKeyFieldInFilter(e,t,r){return"in"===t?new e7(e,r):new te(e,r)}matches(e){let t=e.data.field(this.field);return"!="===this.op?null!==t&&void 0===t.nullValue&&this.matchesComparison(eU(t,this.value)):null!==t&&eM(this.value)===eM(t)&&this.matchesComparison(eU(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return e>0;case">=":return e>=0;default:return S(47266,{operator:this.op})}}isInequality(){return["<","<=",">",">=","!=","not-in"].indexOf(this.op)>=0}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class e6 extends e3{constructor(e,t){super(),this.filters=e,this.op=t,this.Pe=null}static create(e,t){return new e6(e,t)}matches(e){return e5(this)?void 0===this.filters.find(t=>!t.matches(e)):void 0!==this.filters.find(t=>t.matches(e))}getFlattenedFilters(){return null!==this.Pe||(this.Pe=this.filters.reduce((e,t)=>e.concat(t.getFlattenedFilters()),[])),this.Pe}getFilters(){return Object.assign([],this.filters)}}function e5(e){return"and"===e.op}function e8(e){for(let t of e.filters)if(t instanceof e6)return!1;return!0}class e9 extends e4{constructor(e,t,r){super(e,t,r),this.key=H.fromName(r.referenceValue)}matches(e){let t=H.comparator(e.key,this.key);return this.matchesComparison(t)}}class e7 extends e4{constructor(e,t){super(e,"in",t),this.keys=tt("in",t)}matches(e){return this.keys.some(t=>t.isEqual(e.key))}}class te extends e4{constructor(e,t){super(e,"not-in",t),this.keys=tt("not-in",t)}matches(e){return!this.keys.some(t=>t.isEqual(e.key))}}function tt(e,t){return(t.arrayValue?.values||[]).map(e=>H.fromName(e.referenceValue))}class tr extends e4{constructor(e,t){super(e,"array-contains",t)}matches(e){let t=e.data.field(this.field);return ej(t)&&eF(t.arrayValue,this.value)}}class tn extends e4{constructor(e,t){super(e,"in",t)}matches(e){let t=e.data.field(this.field);return null!==t&&eF(this.value.arrayValue,t)}}class ts extends e4{constructor(e,t){super(e,"not-in",t)}matches(e){if(eF(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;let t=e.data.field(this.field);return null!==t&&void 0===t.nullValue&&!eF(this.value.arrayValue,t)}}class ti extends e4{constructor(e,t){super(e,"array-contains-any",t)}matches(e){let t=e.data.field(this.field);return!(!ej(t)||!t.arrayValue.values)&&t.arrayValue.values.some(e=>eF(this.value.arrayValue,e))}}class ta{constructor(e,t=null,r=[],n=[],s=null,i=null,a=null){this.path=e,this.collectionGroup=t,this.orderBy=r,this.filters=n,this.limit=s,this.startAt=i,this.endAt=a,this.Te=null}}function to(e,t=null,r=[],n=[],s=null,i=null,a=null){return new ta(e,t,r,n,s,i,a)}function tl(e){if(null===e.Te){let t=e.path.canonicalString();null!==e.collectionGroup&&(t+="|cg:"+e.collectionGroup),t+="|f:",t+=e.filters.map(e=>(function e(t){if(t instanceof e4)return t.field.canonicalString()+t.op.toString()+e$(t.value);if(e8(t)&&e5(t))return t.filters.map(t=>e(t)).join(",");{let r=t.filters.map(t=>e(t)).join(",");return`${t.op}(${r})`}})(e)).join(","),t+="|ob:",t+=e.orderBy.map(e=>e.field.canonicalString()+e.dir).join(","),null==e.limit||(t+="|l:",t+=e.limit),e.startAt&&(t+="|lb:",t+=e.startAt.inclusive?"b:":"a:",t+=e.startAt.position.map(e=>e$(e)).join(",")),e.endAt&&(t+="|ub:",t+=e.endAt.inclusive?"a:":"b:",t+=e.endAt.position.map(e=>e$(e)).join(",")),e.Te=t}return e.Te}function tu(e,t){if(e.limit!==t.limit||e.orderBy.length!==t.orderBy.length)return!1;for(let s=0;s<e.orderBy.length;s++){var r,n;if(r=e.orderBy[s],n=t.orderBy[s],!(r.dir===n.dir&&r.field.isEqual(n.field)))return!1}if(e.filters.length!==t.filters.length)return!1;for(let r=0;r<e.filters.length;r++)if(!function e(t,r){return t instanceof e4?r instanceof e4&&t.op===r.op&&t.field.isEqual(r.field)&&eP(t.value,r.value):t instanceof e6?r instanceof e6&&t.op===r.op&&t.filters.length===r.filters.length&&t.filters.reduce((t,n,s)=>t&&e(n,r.filters[s]),!0):void S(19439)}(e.filters[r],t.filters[r]))return!1;return e.collectionGroup===t.collectionGroup&&!!e.path.isEqual(t.path)&&!!e1(e.startAt,t.startAt)&&e1(e.endAt,t.endAt)}function th(e){return H.isDocumentKey(e.path)&&null===e.collectionGroup&&0===e.filters.length}class tc{constructor(e,t=null,r=[],n=[],s=null,i="F",a=null,o=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=r,this.filters=n,this.limit=s,this.limitType=i,this.startAt=a,this.endAt=o,this.Ie=null,this.Ee=null,this.de=null,this.startAt,this.endAt}}function td(e){return 0===e.filters.length&&null===e.limit&&null==e.startAt&&null==e.endAt&&(0===e.explicitOrderBy.length||1===e.explicitOrderBy.length&&e.explicitOrderBy[0].field.isKeyField())}function tm(e){if(null===e.Ie){let t;e.Ie=[];let r=new Set;for(let t of e.explicitOrderBy)e.Ie.push(t),r.add(t.field.canonicalString());let n=e.explicitOrderBy.length>0?e.explicitOrderBy[e.explicitOrderBy.length-1].dir:"asc";(t=new ef(Q.comparator),e.filters.forEach(e=>{e.getFlattenedFilters().forEach(e=>{e.isInequality()&&(t=t.add(e.field))})}),t).forEach(t=>{r.has(t.canonicalString())||t.isKeyField()||e.Ie.push(new e2(t,n))}),r.has(Q.keyField().canonicalString())||e.Ie.push(new e2(Q.keyField(),n))}return e.Ie}function tf(e){return e.Ee||(e.Ee=function(e,t){if("F"===e.limitType)return to(e.path,e.collectionGroup,t,e.filters,e.limit,e.startAt,e.endAt);{t=t.map(e=>{let t="desc"===e.dir?"asc":"desc";return new e2(e.field,t)});let r=e.endAt?new eZ(e.endAt.position,e.endAt.inclusive):null,n=e.startAt?new eZ(e.startAt.position,e.startAt.inclusive):null;return to(e.path,e.collectionGroup,t,e.filters,e.limit,r,n)}}(e,tm(e))),e.Ee}function tg(e,t,r){return new tc(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),t,r,e.startAt,e.endAt)}function tp(e,t){return tu(tf(e),tf(t))&&e.limitType===t.limitType}function ty(e){return`${tl(tf(e))}|lt:${e.limitType}`}function tv(e){var t;let r;return`Query(target=${r=(t=tf(e)).path.canonicalString(),null!==t.collectionGroup&&(r+=" collectionGroup="+t.collectionGroup),t.filters.length>0&&(r+=`, filters: [${t.filters.map(e=>(function e(t){return t instanceof e4?`${t.field.canonicalString()} ${t.op} ${e$(t.value)}`:t instanceof e6?t.op.toString()+" {"+t.getFilters().map(e).join(" ,")+"}":"Filter"})(e)).join(", ")}]`),null==t.limit||(r+=", limit: "+t.limit),t.orderBy.length>0&&(r+=`, orderBy: [${t.orderBy.map(e=>`${e.field.canonicalString()} (${e.dir})`).join(", ")}]`),t.startAt&&(r+=", startAt: ",r+=t.startAt.inclusive?"b:":"a:",r+=t.startAt.position.map(e=>e$(e)).join(",")),t.endAt&&(r+=", endAt: ",r+=t.endAt.inclusive?"a:":"b:",r+=t.endAt.position.map(e=>e$(e)).join(",")),`Target(${r})`}; limitType=${e.limitType})`}function tw(e,t){return t.isFoundDocument()&&function(e,t){let r=t.key.path;return null!==e.collectionGroup?t.key.hasCollectionId(e.collectionGroup)&&e.path.isPrefixOf(r):H.isDocumentKey(e.path)?e.path.isEqual(r):e.path.isImmediateParentOf(r)}(e,t)&&function(e,t){for(let r of tm(e))if(!r.field.isKeyField()&&null===t.data.field(r.field))return!1;return!0}(e,t)&&function(e,t){for(let r of e.filters)if(!r.matches(t))return!1;return!0}(e,t)&&(!e.startAt||!!function(e,t,r){let n=e0(e,t,r);return e.inclusive?n<=0:n<0}(e.startAt,tm(e),t))&&(!e.endAt||!!function(e,t,r){let n=e0(e,t,r);return e.inclusive?n>=0:n>0}(e.endAt,tm(e),t))}function tE(e){return(t,r)=>{let n=!1;for(let s of tm(e)){let e=function(e,t,r){let n=e.field.isKeyField()?H.comparator(t.key,r.key):function(e,t,r){let n=t.data.field(e),s=r.data.field(e);return null!==n&&null!==s?eU(n,s):S(42886)}(e.field,t,r);switch(e.dir){case"asc":return n;case"desc":return -1*n;default:return S(19790,{direction:e.dir})}}(s,t,r);if(0!==e)return e;n=n||s.field.isKeyField()}return 0}}class t_{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(e){let t=this.mapKeyFn(e),r=this.inner[t];if(void 0!==r){for(let[t,n]of r)if(this.equalsFn(t,e))return n}}has(e){return void 0!==this.get(e)}set(e,t){let r=this.mapKeyFn(e),n=this.inner[r];if(void 0===n)return this.inner[r]=[[e,t]],void this.innerSize++;for(let r=0;r<n.length;r++)if(this.equalsFn(n[r][0],e))return void(n[r]=[e,t]);n.push([e,t]),this.innerSize++}delete(e){let t=this.mapKeyFn(e),r=this.inner[t];if(void 0===r)return!1;for(let n=0;n<r.length;n++)if(this.equalsFn(r[n][0],e))return 1===r.length?delete this.inner[t]:r.splice(n,1),this.innerSize--,!0;return!1}forEach(e){eh(this.inner,(t,r)=>{for(let[t,n]of r)e(t,n)})}isEmpty(){var e=this.inner;for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}size(){return this.innerSize}}let tT=new ec(H.comparator),tC=new ec(H.comparator);function tS(...e){let t=tC;for(let r of e)t=t.insert(r.key,r);return t}function tI(){return new t_(e=>e.toString(),(e,t)=>e.isEqual(t))}new ec(H.comparator);let tN=new ef(H.comparator);function tA(...e){let t=tN;for(let r of e)t=t.add(r);return t}let tb=new ef(U);class tk{constructor(){this._=void 0}}class tD extends tk{}class tx extends tk{constructor(e){super(),this.elements=e}}function tR(e,t){let r=tP(t);for(let t of e.elements)r.some(e=>eP(e,t))||r.push(t);return{arrayValue:{values:r}}}class tV extends tk{constructor(e){super(),this.elements=e}}function tO(e,t){let r=tP(t);for(let t of e.elements)r=r.filter(e=>!eP(e,t));return{arrayValue:{values:r}}}class tL extends tk{constructor(e,t){super(),this.serializer=e,this.Ae=t}}function tM(e){return e_(e.integerValue||e.doubleValue)}function tP(e){return ej(e)&&e.arrayValue.values?e.arrayValue.values.slice():[]}class tF{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new tF}static exists(e){return new tF(void 0,e)}static updateTime(e){return new tF(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}function tU(e,t){return void 0!==e.updateTime?t.isFoundDocument()&&t.version.isEqual(e.updateTime):void 0===e.exists||e.exists===t.isFoundDocument()}class tq{}function tz(e,t){if(!e.hasLocalMutations||t&&0===t.fields.length)return null;if(null===t)return e.isNoDocument()?new tW(e.key,tF.none()):new tj(e.key,e.data,tF.none());{let r=e.data,n=eX.empty(),s=new ef(Q.comparator);for(let e of t.fields)if(!s.has(e)){let t=r.field(e);null===t&&e.length>1&&(e=e.popLast(),t=r.field(e)),null===t?n.delete(e):n.set(e,t),s=s.add(e)}return new tK(e.key,n,new ep(s.toArray()),tF.none())}}function t$(e,t,r,n){return e instanceof tj?function(e,t,r,n){if(!tU(e.precondition,t))return r;let s=e.value.clone(),i=tH(e.fieldTransforms,n,t);return s.setAll(i),t.convertToFoundDocument(t.version,s).setHasLocalMutations(),null}(e,t,r,n):e instanceof tK?function(e,t,r,n){if(!tU(e.precondition,t))return r;let s=tH(e.fieldTransforms,n,t),i=t.data;return(i.setAll(tG(e)),i.setAll(s),t.convertToFoundDocument(t.version,i).setHasLocalMutations(),null===r)?null:r.unionWith(e.fieldMask.fields).unionWith(e.fieldTransforms.map(e=>e.field))}(e,t,r,n):tU(e.precondition,t)?(t.convertToNoDocument(t.version).setHasLocalMutations(),null):r}function tB(e,t){var r,n;return e.type===t.type&&!!e.key.isEqual(t.key)&&!!e.precondition.isEqual(t.precondition)&&(r=e.fieldTransforms,n=t.fieldTransforms,!!(void 0===r&&void 0===n||!(!r||!n)&&$(r,n,(e,t)=>{var r,n;return e.field.isEqual(t.field)&&(r=e.transform,n=t.transform,r instanceof tx&&n instanceof tx||r instanceof tV&&n instanceof tV?$(r.elements,n.elements,eP):r instanceof tL&&n instanceof tL?eP(r.Ae,n.Ae):r instanceof tD&&n instanceof tD)})))&&(0===e.type?e.value.isEqual(t.value):1!==e.type||e.data.isEqual(t.data)&&e.fieldMask.isEqual(t.fieldMask))}class tj extends tq{constructor(e,t,r,n=[]){super(),this.key=e,this.value=t,this.precondition=r,this.fieldTransforms=n,this.type=0}getFieldMask(){return null}}class tK extends tq{constructor(e,t,r,n,s=[]){super(),this.key=e,this.data=t,this.fieldMask=r,this.precondition=n,this.fieldTransforms=s,this.type=1}getFieldMask(){return this.fieldMask}}function tG(e){let t=new Map;return e.fieldMask.fields.forEach(r=>{if(!r.isEmpty()){let n=e.data.field(r);t.set(r,n)}}),t}function tQ(e,t,r){let n=new Map;N(e.length===r.length,32656,{Re:r.length,Ve:e.length});for(let i=0;i<r.length;i++){var s;let a=e[i],o=a.transform,l=t.data.field(a.field);n.set(a.field,(s=r[i],o instanceof tx?tR(o,l):o instanceof tV?tO(o,l):s))}return n}function tH(e,t,r){let n=new Map;for(let s of e){let e=s.transform,i=r.data.field(s.field);n.set(s.field,e instanceof tD?function(e,t){let r={fields:{[eS]:{stringValue:eC},[eN]:{timestampValue:{seconds:e.seconds,nanos:e.nanoseconds}}}};return t&&eA(t)&&(t=eb(t)),t&&(r.fields[eI]=t),{mapValue:r}}(t,i):e instanceof tx?tR(e,i):e instanceof tV?tO(e,i):function(e,t){let r=function(e,t){return e instanceof tL?eB(t)||t&&"doubleValue"in t?t:{integerValue:0}:null}(e,t),n=tM(r)+tM(e.Ae);return eB(r)&&eB(e.Ae)?{integerValue:""+n}:function(e,t){if(e.useProto3Json){if(isNaN(t))return{doubleValue:"NaN"};if(t===1/0)return{doubleValue:"Infinity"};if(t===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:el(t)?"-0":t}}(e.serializer,n)}(e,i))}return n}class tW extends tq{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class tY{constructor(e,t,r,n){this.batchId=e,this.localWriteTime=t,this.baseMutations=r,this.mutations=n}applyToRemoteDocument(e,t){let r=t.mutationResults;for(let t=0;t<this.mutations.length;t++){let n=this.mutations[t];n.key.isEqual(e.key)&&function(e,t,r){e instanceof tj?function(e,t,r){let n=e.value.clone(),s=tQ(e.fieldTransforms,t,r.transformResults);n.setAll(s),t.convertToFoundDocument(r.version,n).setHasCommittedMutations()}(e,t,r):e instanceof tK?function(e,t,r){if(!tU(e.precondition,t))return t.convertToUnknownDocument(r.version);let n=tQ(e.fieldTransforms,t,r.transformResults),s=t.data;s.setAll(tG(e)),s.setAll(n),t.convertToFoundDocument(r.version,s).setHasCommittedMutations()}(e,t,r):t.convertToNoDocument(r.version).setHasCommittedMutations()}(n,e,r[t])}}applyToLocalView(e,t){for(let r of this.baseMutations)r.key.isEqual(e.key)&&(t=t$(r,e,t,this.localWriteTime));for(let r of this.mutations)r.key.isEqual(e.key)&&(t=t$(r,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(e,t){let r=tI();return this.mutations.forEach(n=>{let s=e.get(n.key),i=s.overlayedDocument,a=this.applyToLocalView(i,s.mutatedFields),o=tz(i,a=t.has(n.key)?null:a);null!==o&&r.set(n.key,o),i.isValidDocument()||i.convertToNoDocument(ee.min())}),r}keys(){return this.mutations.reduce((e,t)=>e.add(t.key),tA())}isEqual(e){return this.batchId===e.batchId&&$(this.mutations,e.mutations,(e,t)=>tB(e,t))&&$(this.baseMutations,e.baseMutations,(e,t)=>tB(e,t))}}class tX{constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return null!==e&&this.mutation===e.mutation}toString(){return`Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`}}class tJ{constructor(e,t){this.count=e,this.unchangedNames=t}}function tZ(e){if(void 0===e)return _("GRPC error has no .code"),A.UNKNOWN;switch(e){case n.OK:return A.OK;case n.CANCELLED:return A.CANCELLED;case n.UNKNOWN:return A.UNKNOWN;case n.DEADLINE_EXCEEDED:return A.DEADLINE_EXCEEDED;case n.RESOURCE_EXHAUSTED:return A.RESOURCE_EXHAUSTED;case n.INTERNAL:return A.INTERNAL;case n.UNAVAILABLE:return A.UNAVAILABLE;case n.UNAUTHENTICATED:return A.UNAUTHENTICATED;case n.INVALID_ARGUMENT:return A.INVALID_ARGUMENT;case n.NOT_FOUND:return A.NOT_FOUND;case n.ALREADY_EXISTS:return A.ALREADY_EXISTS;case n.PERMISSION_DENIED:return A.PERMISSION_DENIED;case n.FAILED_PRECONDITION:return A.FAILED_PRECONDITION;case n.ABORTED:return A.ABORTED;case n.OUT_OF_RANGE:return A.OUT_OF_RANGE;case n.UNIMPLEMENTED:return A.UNIMPLEMENTED;case n.DATA_LOSS:return A.DATA_LOSS;default:return S(39323,{code:e})}}(s=n||(n={}))[s.OK=0]="OK",s[s.CANCELLED=1]="CANCELLED",s[s.UNKNOWN=2]="UNKNOWN",s[s.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",s[s.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",s[s.NOT_FOUND=5]="NOT_FOUND",s[s.ALREADY_EXISTS=6]="ALREADY_EXISTS",s[s.PERMISSION_DENIED=7]="PERMISSION_DENIED",s[s.UNAUTHENTICATED=16]="UNAUTHENTICATED",s[s.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",s[s.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",s[s.ABORTED=10]="ABORTED",s[s.OUT_OF_RANGE=11]="OUT_OF_RANGE",s[s.UNIMPLEMENTED=12]="UNIMPLEMENTED",s[s.INTERNAL=13]="INTERNAL",s[s.UNAVAILABLE=14]="UNAVAILABLE",s[s.DATA_LOSS=15]="DATA_LOSS";let t0=new c.jz([0xffffffff,0xffffffff],0);function t1(e){let t=(new TextEncoder).encode(e),r=new c.VV;return r.update(t),new Uint8Array(r.digest())}function t2(e){let t=new DataView(e.buffer),r=t.getUint32(0,!0),n=t.getUint32(4,!0),s=t.getUint32(8,!0),i=t.getUint32(12,!0);return[new c.jz([r,n],0),new c.jz([s,i],0)]}class t3{constructor(e,t,r){if(this.bitmap=e,this.padding=t,this.hashCount=r,t<0||t>=8)throw new t4(`Invalid padding: ${t}`);if(r<0||e.length>0&&0===this.hashCount)throw new t4(`Invalid hash count: ${r}`);if(0===e.length&&0!==t)throw new t4(`Invalid padding when bitmap length is 0: ${t}`);this.ge=8*e.length-t,this.pe=c.jz.fromNumber(this.ge)}ye(e,t,r){let n=e.add(t.multiply(c.jz.fromNumber(r)));return 1===n.compare(t0)&&(n=new c.jz([n.getBits(0),n.getBits(1)],0)),n.modulo(this.pe).toNumber()}we(e){return!!(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(0===this.ge)return!1;let[t,r]=t2(t1(e));for(let e=0;e<this.hashCount;e++){let n=this.ye(t,r,e);if(!this.we(n))return!1}return!0}static create(e,t,r){let n=new t3(new Uint8Array(Math.ceil(e/8)),e%8==0?0:8-e%8,t);return r.forEach(e=>n.insert(e)),n}insert(e){if(0===this.ge)return;let[t,r]=t2(t1(e));for(let e=0;e<this.hashCount;e++){let n=this.ye(t,r,e);this.Se(n)}}Se(e){let t=Math.floor(e/8);this.bitmap[t]|=1<<e%8}}class t4 extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}class t6{constructor(e,t,r,n,s){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=r,this.documentUpdates=n,this.resolvedLimboDocuments=s}static createSynthesizedRemoteEventForCurrentChange(e,t,r){let n=new Map;return n.set(e,t5.createSynthesizedTargetChangeForCurrentChange(e,t,r)),new t6(ee.min(),n,new ec(U),tT,tA())}}class t5{constructor(e,t,r,n,s){this.resumeToken=e,this.current=t,this.addedDocuments=r,this.modifiedDocuments=n,this.removedDocuments=s}static createSynthesizedTargetChangeForCurrentChange(e,t,r){return new t5(r,t,tA(),tA(),tA())}}class t8{constructor(e,t,r,n){this.be=e,this.removedTargetIds=t,this.key=r,this.De=n}}class t9{constructor(e,t){this.targetId=e,this.Ce=t}}class t7{constructor(e,t,r=ev.EMPTY_BYTE_STRING,n=null){this.state=e,this.targetIds=t,this.resumeToken=r,this.cause=n}}class re{constructor(){this.ve=0,this.Fe=rn(),this.Me=ev.EMPTY_BYTE_STRING,this.xe=!1,this.Oe=!0}get current(){return this.xe}get resumeToken(){return this.Me}get Ne(){return 0!==this.ve}get Be(){return this.Oe}Le(e){e.approximateByteSize()>0&&(this.Oe=!0,this.Me=e)}ke(){let e=tA(),t=tA(),r=tA();return this.Fe.forEach((n,s)=>{switch(s){case 0:e=e.add(n);break;case 2:t=t.add(n);break;case 1:r=r.add(n);break;default:S(38017,{changeType:s})}}),new t5(this.Me,this.xe,e,t,r)}qe(){this.Oe=!1,this.Fe=rn()}Qe(e,t){this.Oe=!0,this.Fe=this.Fe.insert(e,t)}$e(e){this.Oe=!0,this.Fe=this.Fe.remove(e)}Ue(){this.ve+=1}Ke(){this.ve-=1,N(this.ve>=0,3241,{ve:this.ve})}We(){this.Oe=!0,this.xe=!0}}class rt{constructor(e){this.Ge=e,this.ze=new Map,this.je=tT,this.Je=rr(),this.He=rr(),this.Ye=new ec(U)}Ze(e){for(let t of e.be)e.De&&e.De.isFoundDocument()?this.Xe(t,e.De):this.et(t,e.key,e.De);for(let t of e.removedTargetIds)this.et(t,e.key,e.De)}tt(e){this.forEachTarget(e,t=>{let r=this.nt(t);switch(e.state){case 0:this.rt(t)&&r.Le(e.resumeToken);break;case 1:r.Ke(),r.Ne||r.qe(),r.Le(e.resumeToken);break;case 2:r.Ke(),r.Ne||this.removeTarget(t);break;case 3:this.rt(t)&&(r.We(),r.Le(e.resumeToken));break;case 4:this.rt(t)&&(this.it(t),r.Le(e.resumeToken));break;default:S(56790,{state:e.state})}})}forEachTarget(e,t){e.targetIds.length>0?e.targetIds.forEach(t):this.ze.forEach((e,r)=>{this.rt(r)&&t(r)})}st(e){let t=e.targetId,r=e.Ce.count,n=this.ot(t);if(n){let s=n.target;if(th(s))if(0===r){let e=new H(s.path);this.et(t,e,eJ.newNoDocument(e,ee.min()))}else N(1===r,20013,{expectedCount:r});else{let n=this._t(t);if(n!==r){let r=this.ut(e),s=r?this.ct(r,e,n):1;0!==s&&(this.it(t),this.Ye=this.Ye.insert(t,2===s?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch"))}}}}ut(e){let t,r,n=e.Ce.unchangedNames;if(!n||!n.bits)return null;let{bits:{bitmap:s="",padding:i=0},hashCount:a=0}=n;try{t=eT(s).toUint8Array()}catch(e){if(e instanceof ey)return T("Decoding the base64 bloom filter in existence filter failed ("+e.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw e}try{r=new t3(t,i,a)}catch(e){return T(e instanceof t4?"BloomFilter error: ":"Applying bloom filter failed: ",e),null}return 0===r.ge?null:r}ct(e,t,r){return 2*(t.Ce.count!==r-this.Pt(e,t.targetId))}Pt(e,t){let r=this.Ge.getRemoteKeysForTarget(t),n=0;return r.forEach(r=>{let s=this.Ge.ht(),i=`projects/${s.projectId}/databases/${s.database}/documents/${r.path.canonicalString()}`;e.mightContain(i)||(this.et(t,r,null),n++)}),n}Tt(e){let t=new Map;this.ze.forEach((r,n)=>{let s=this.ot(n);if(s){if(r.current&&th(s.target)){let t=new H(s.target.path);this.It(t).has(n)||this.Et(n,t)||this.et(n,t,eJ.newNoDocument(t,e))}r.Be&&(t.set(n,r.ke()),r.qe())}});let r=tA();this.He.forEach((e,t)=>{let n=!0;t.forEachWhile(e=>{let t=this.ot(e);return!t||"TargetPurposeLimboResolution"===t.purpose||(n=!1,!1)}),n&&(r=r.add(e))}),this.je.forEach((t,r)=>r.setReadTime(e));let n=new t6(e,t,this.Ye,this.je,r);return this.je=tT,this.Je=rr(),this.He=rr(),this.Ye=new ec(U),n}Xe(e,t){if(!this.rt(e))return;let r=2*!!this.Et(e,t.key);this.nt(e).Qe(t.key,r),this.je=this.je.insert(t.key,t),this.Je=this.Je.insert(t.key,this.It(t.key).add(e)),this.He=this.He.insert(t.key,this.dt(t.key).add(e))}et(e,t,r){if(!this.rt(e))return;let n=this.nt(e);this.Et(e,t)?n.Qe(t,1):n.$e(t),this.He=this.He.insert(t,this.dt(t).delete(e)),this.He=this.He.insert(t,this.dt(t).add(e)),r&&(this.je=this.je.insert(t,r))}removeTarget(e){this.ze.delete(e)}_t(e){let t=this.nt(e).ke();return this.Ge.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}Ue(e){this.nt(e).Ue()}nt(e){let t=this.ze.get(e);return t||(t=new re,this.ze.set(e,t)),t}dt(e){let t=this.He.get(e);return t||(t=new ef(U),this.He=this.He.insert(e,t)),t}It(e){let t=this.Je.get(e);return t||(t=new ef(U),this.Je=this.Je.insert(e,t)),t}rt(e){let t=null!==this.ot(e);return t||E("WatchChangeAggregator","Detected inactive target",e),t}ot(e){let t=this.ze.get(e);return t&&t.Ne?null:this.Ge.At(e)}it(e){this.ze.set(e,new re),this.Ge.getRemoteKeysForTarget(e).forEach(t=>{this.et(e,t,null)})}Et(e,t){return this.Ge.getRemoteKeysForTarget(e).has(t)}}function rr(){return new ec(H.comparator)}function rn(){return new ec(H.comparator)}let rs={asc:"ASCENDING",desc:"DESCENDING"},ri={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},ra={and:"AND",or:"OR"};class ro{constructor(e,t){this.databaseId=e,this.useProto3Json=t}}function rl(e,t){return e.useProto3Json||null==t?t:{value:t}}function ru(e){return N(!!e,49232),ee.fromTimestamp(function(e){let t=eE(e);return new Z(t.seconds,t.nanos)}(e))}function rh(e,t){let r=new K(["projects",e.projectId,"databases",e.database]).child("documents");return void 0===t?r:r.child(t)}function rc(e){let t=K.fromString(e);return N(rv(t),10190,{key:t.toString()}),t}function rd(e,t){let r=rc(t);if(r.get(1)!==e.databaseId.projectId)throw new b(A.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+r.get(1)+" vs "+e.databaseId.projectId);if(r.get(3)!==e.databaseId.database)throw new b(A.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+r.get(3)+" vs "+e.databaseId.database);return new H(rg(r))}function rm(e,t){return rh(e.databaseId,t).canonicalString()}function rf(e){return new K(["projects",e.databaseId.projectId,"databases",e.databaseId.database]).canonicalString()}function rg(e){return N(e.length>4&&"documents"===e.get(4),29091,{key:e.toString()}),e.popFirst(5)}function rp(e){return{fieldPath:e.canonicalString()}}function ry(e){return Q.fromServerFormat(e.fieldPath)}function rv(e){return e.length>=4&&"projects"===e.get(0)&&"databases"===e.get(2)}class rw{constructor(e,t,r,n,s=ee.min(),i=ee.min(),a=ev.EMPTY_BYTE_STRING,o=null){this.target=e,this.targetId=t,this.purpose=r,this.sequenceNumber=n,this.snapshotVersion=s,this.lastLimboFreeSnapshotVersion=i,this.resumeToken=a,this.expectedCount=o}withSequenceNumber(e){return new rw(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new rw(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new rw(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new rw(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}}class rE{constructor(e){this.yt=e}}class r_{constructor(){}Dt(e,t){this.Ct(e,t),t.vt()}Ct(e,t){if("nullValue"in e)this.Ft(t,5);else if("booleanValue"in e)this.Ft(t,10),t.Mt(+!!e.booleanValue);else if("integerValue"in e)this.Ft(t,15),t.Mt(e_(e.integerValue));else if("doubleValue"in e){let r=e_(e.doubleValue);isNaN(r)?this.Ft(t,13):(this.Ft(t,15),el(r)?t.Mt(0):t.Mt(r))}else if("timestampValue"in e){let r=e.timestampValue;this.Ft(t,20),"string"==typeof r&&(r=eE(r)),t.xt(`${r.seconds||""}`),t.Mt(r.nanos||0)}else if("stringValue"in e)this.Ot(e.stringValue,t),this.Nt(t);else if("bytesValue"in e)this.Ft(t,30),t.Bt(eT(e.bytesValue)),this.Nt(t);else if("referenceValue"in e)this.Lt(e.referenceValue,t);else if("geoPointValue"in e){let r=e.geoPointValue;this.Ft(t,45),t.Mt(r.latitude||0),t.Mt(r.longitude||0)}else"mapValue"in e?eY(e)?this.Ft(t,Number.MAX_SAFE_INTEGER):eH(e)?this.kt(e.mapValue,t):(this.qt(e.mapValue,t),this.Nt(t)):"arrayValue"in e?(this.Qt(e.arrayValue,t),this.Nt(t)):S(19022,{$t:e})}Ot(e,t){this.Ft(t,25),this.Ut(e,t)}Ut(e,t){t.xt(e)}qt(e,t){let r=e.fields||{};for(let e of(this.Ft(t,55),Object.keys(r)))this.Ot(e,t),this.Ct(r[e],t)}kt(e,t){let r=e.fields||{};this.Ft(t,53);let n=r[eL].arrayValue?.values?.length||0;this.Ft(t,15),t.Mt(e_(n)),this.Ot(eL,t),this.Ct(r[eL],t)}Qt(e,t){let r=e.values||[];for(let e of(this.Ft(t,50),r))this.Ct(e,t)}Lt(e,t){this.Ft(t,37),H.fromName(e).path.forEach(e=>{this.Ft(t,60),this.Ut(e,t)})}Ft(e,t){e.Mt(t)}Nt(e){e.Mt(2)}}r_.Kt=new r_;class rT{constructor(){this.Cn=new rC}addToCollectionParentIndex(e,t){return this.Cn.add(t),ei.resolve()}getCollectionParents(e,t){return ei.resolve(this.Cn.getEntries(t))}addFieldIndex(e,t){return ei.resolve()}deleteFieldIndex(e,t){return ei.resolve()}deleteAllFieldIndexes(e){return ei.resolve()}createTargetIndexes(e,t){return ei.resolve()}getDocumentsMatchingTarget(e,t){return ei.resolve(null)}getIndexType(e,t){return ei.resolve(0)}getFieldIndexes(e,t){return ei.resolve([])}getNextCollectionGroupToUpdate(e){return ei.resolve(null)}getMinOffset(e,t){return ei.resolve(er.min())}getMinOffsetFromCollectionGroup(e,t){return ei.resolve(er.min())}updateCollectionGroup(e,t,r){return ei.resolve()}updateIndexEntries(e,t){return ei.resolve()}}class rC{constructor(){this.index={}}add(e){let t=e.lastSegment(),r=e.popLast(),n=this.index[t]||new ef(K.comparator),s=!n.has(r);return this.index[t]=n.add(r),s}has(e){let t=e.lastSegment(),r=e.popLast(),n=this.index[t];return n&&n.has(r)}getEntries(e){return(this.index[e]||new ef(K.comparator)).toArray()}}new Uint8Array(0);let rS={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0};class rI{static withCacheSize(e){return new rI(e,rI.DEFAULT_COLLECTION_PERCENTILE,rI.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(e,t,r){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=r}}rI.DEFAULT_COLLECTION_PERCENTILE=10,rI.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,rI.DEFAULT=new rI(0x2800000,rI.DEFAULT_COLLECTION_PERCENTILE,rI.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),rI.DISABLED=new rI(-1,0,0);class rN{constructor(e){this.ar=e}next(){return this.ar+=2,this.ar}static ur(){return new rN(0)}static cr(){return new rN(-1)}}let rA="LruGarbageCollector";function rb([e,t],[r,n]){let s=U(e,r);return 0===s?U(t,n):s}class rk{constructor(e){this.Ir=e,this.buffer=new ef(rb),this.Er=0}dr(){return++this.Er}Ar(e){let t=[e,this.dr()];if(this.buffer.size<this.Ir)this.buffer=this.buffer.add(t);else{let e=this.buffer.last();0>rb(t,e)&&(this.buffer=this.buffer.delete(e).add(t))}}get maxValue(){return this.buffer.last()[0]}}class rD{constructor(e,t,r){this.garbageCollector=e,this.asyncQueue=t,this.localStore=r,this.Rr=null}start(){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.Vr(6e4)}stop(){this.Rr&&(this.Rr.cancel(),this.Rr=null)}get started(){return null!==this.Rr}Vr(e){E(rA,`Garbage collection scheduled in ${e}ms`),this.Rr=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,async()=>{this.Rr=null;try{await this.localStore.collectGarbage(this.garbageCollector)}catch(e){ea(e)?E(rA,"Ignoring IndexedDB error during garbage collection: ",e):await es(e)}await this.Vr(3e5)})}}class rx{constructor(e,t){this.mr=e,this.params=t}calculateTargetCount(e,t){return this.mr.gr(e).next(e=>Math.floor(t/100*e))}nthSequenceNumber(e,t){if(0===t)return ei.resolve(eo.ce);let r=new rk(t);return this.mr.forEachTarget(e,e=>r.Ar(e.sequenceNumber)).next(()=>this.mr.pr(e,e=>r.Ar(e))).next(()=>r.maxValue)}removeTargets(e,t,r){return this.mr.removeTargets(e,t,r)}removeOrphanedDocuments(e,t){return this.mr.removeOrphanedDocuments(e,t)}collect(e,t){return -1===this.params.cacheSizeCollectionThreshold?(E("LruGarbageCollector","Garbage collection skipped; disabled"),ei.resolve(rS)):this.getCacheSize(e).next(r=>r<this.params.cacheSizeCollectionThreshold?(E("LruGarbageCollector",`Garbage collection skipped; Cache size ${r} is lower than threshold ${this.params.cacheSizeCollectionThreshold}`),rS):this.yr(e,t))}getCacheSize(e){return this.mr.getCacheSize(e)}yr(e,t){let r,n,s,i,a,o,l,h=Date.now();return this.calculateTargetCount(e,this.params.percentileToCollect).next(t=>(t>this.params.maximumSequenceNumbersToCollect?(E("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from ${t}`),n=this.params.maximumSequenceNumbersToCollect):n=t,i=Date.now(),this.nthSequenceNumber(e,n))).next(n=>(r=n,a=Date.now(),this.removeTargets(e,r,t))).next(t=>(s=t,o=Date.now(),this.removeOrphanedDocuments(e,r))).next(e=>(l=Date.now(),w()<=u.$b.DEBUG&&E("LruGarbageCollector",`LRU Garbage Collection
	Counted targets in ${i-h}ms
	Determined least recently used ${n} in `+(a-i)+"ms\n"+`	Removed ${s} targets in `+(o-a)+"ms\n"+`	Removed ${e} documents in `+(l-o)+"ms\n"+`Total Duration: ${l-h}ms`),ei.resolve({didRun:!0,sequenceNumbersCollected:n,targetsRemoved:s,documentsRemoved:e})))}}class rR{constructor(){this.changes=new t_(e=>e.toString(),(e,t)=>e.isEqual(t)),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,eJ.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();let r=this.changes.get(t);return void 0!==r?ei.resolve(r):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}}class rV{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}}class rO{constructor(e,t,r,n){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=r,this.indexManager=n}getDocument(e,t){let r=null;return this.documentOverlayCache.getOverlay(e,t).next(n=>(r=n,this.remoteDocumentCache.getEntry(e,t))).next(e=>(null!==r&&t$(r.mutation,e,ep.empty(),Z.now()),e))}getDocuments(e,t){return this.remoteDocumentCache.getEntries(e,t).next(t=>this.getLocalViewOfDocuments(e,t,tA()).next(()=>t))}getLocalViewOfDocuments(e,t,r=tA()){let n=tI();return this.populateOverlays(e,n,t).next(()=>this.computeViews(e,t,n,r).next(e=>{let t=tS();return e.forEach((e,r)=>{t=t.insert(e,r.overlayedDocument)}),t}))}getOverlayedDocuments(e,t){let r=tI();return this.populateOverlays(e,r,t).next(()=>this.computeViews(e,t,r,tA()))}populateOverlays(e,t,r){let n=[];return r.forEach(e=>{t.has(e)||n.push(e)}),this.documentOverlayCache.getOverlays(e,n).next(e=>{e.forEach((e,r)=>{t.set(e,r)})})}computeViews(e,t,r,n){let s=tT,i=tI(),a=tI();return t.forEach((e,t)=>{let a=r.get(t.key);n.has(t.key)&&(void 0===a||a.mutation instanceof tK)?s=s.insert(t.key,t):void 0!==a?(i.set(t.key,a.mutation.getFieldMask()),t$(a.mutation,t,a.mutation.getFieldMask(),Z.now())):i.set(t.key,ep.empty())}),this.recalculateAndSaveOverlays(e,s).next(e=>(e.forEach((e,t)=>i.set(e,t)),t.forEach((e,t)=>a.set(e,new rV(t,i.get(e)??null))),a))}recalculateAndSaveOverlays(e,t){let r=tI(),n=new ec((e,t)=>e-t),s=tA();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(e,t).next(e=>{for(let s of e)s.keys().forEach(e=>{let i=t.get(e);if(null===i)return;let a=r.get(e)||ep.empty();a=s.applyToLocalView(i,a),r.set(e,a);let o=(n.get(s.batchId)||tA()).add(e);n=n.insert(s.batchId,o)})}).next(()=>{let i=[],a=n.getReverseIterator();for(;a.hasNext();){let n=a.getNext(),o=n.key,l=n.value,u=tI();l.forEach(e=>{if(!s.has(e)){let n=tz(t.get(e),r.get(e));null!==n&&u.set(e,n),s=s.add(e)}}),i.push(this.documentOverlayCache.saveOverlays(e,o,u))}return ei.waitFor(i)}).next(()=>r)}recalculateAndSaveOverlaysForDocumentKeys(e,t){return this.remoteDocumentCache.getEntries(e,t).next(t=>this.recalculateAndSaveOverlays(e,t))}getDocumentsMatchingQuery(e,t,r,n){return H.isDocumentKey(t.path)&&null===t.collectionGroup&&0===t.filters.length?this.getDocumentsMatchingDocumentQuery(e,t.path):null!==t.collectionGroup?this.getDocumentsMatchingCollectionGroupQuery(e,t,r,n):this.getDocumentsMatchingCollectionQuery(e,t,r,n)}getNextDocuments(e,t,r,n){return this.remoteDocumentCache.getAllFromCollectionGroup(e,t,r,n).next(s=>{let i=n-s.size>0?this.documentOverlayCache.getOverlaysForCollectionGroup(e,t,r.largestBatchId,n-s.size):ei.resolve(tI()),a=-1,o=s;return i.next(t=>ei.forEach(t,(t,r)=>(a<r.largestBatchId&&(a=r.largestBatchId),s.get(t)?ei.resolve():this.remoteDocumentCache.getEntry(e,t).next(e=>{o=o.insert(t,e)}))).next(()=>this.populateOverlays(e,t,s)).next(()=>this.computeViews(e,o,t,tA())).next(e=>{let t;return{batchId:a,changes:(t=tC,e.forEach((e,r)=>t=t.insert(e,r.overlayedDocument)),t)}}))})}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new H(t)).next(e=>{let t=tS();return e.isFoundDocument()&&(t=t.insert(e.key,e)),t})}getDocumentsMatchingCollectionGroupQuery(e,t,r,n){let s=t.collectionGroup,i=tS();return this.indexManager.getCollectionParents(e,s).next(a=>ei.forEach(a,a=>{let o=new tc(a.child(s),null,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt);return this.getDocumentsMatchingCollectionQuery(e,o,r,n).next(e=>{e.forEach((e,t)=>{i=i.insert(e,t)})})}).next(()=>i))}getDocumentsMatchingCollectionQuery(e,t,r,n){let s;return this.documentOverlayCache.getOverlaysForCollection(e,t.path,r.largestBatchId).next(i=>(s=i,this.remoteDocumentCache.getDocumentsMatchingQuery(e,t,r,s,n))).next(e=>{s.forEach((t,r)=>{let n=r.getKey();null===e.get(n)&&(e=e.insert(n,eJ.newInvalidDocument(n)))});let r=tS();return e.forEach((e,n)=>{let i=s.get(e);void 0!==i&&t$(i.mutation,n,ep.empty(),Z.now()),tw(t,n)&&(r=r.insert(e,n))}),r})}}class rL{constructor(e){this.serializer=e,this.Lr=new Map,this.kr=new Map}getBundleMetadata(e,t){return ei.resolve(this.Lr.get(t))}saveBundleMetadata(e,t){return this.Lr.set(t.id,{id:t.id,version:t.version,createTime:ru(t.createTime)}),ei.resolve()}getNamedQuery(e,t){return ei.resolve(this.kr.get(t))}saveNamedQuery(e,t){return this.kr.set(t.name,{name:t.name,query:function(e){let t=function(e){var t;let r,n=function(e){let t=rc(e);return 4===t.length?K.emptyPath():rg(t)}(e.parent),s=e.structuredQuery,i=s.from?s.from.length:0,a=null;if(i>0){N(1===i,65062);let e=s.from[0];e.allDescendants?a=e.collectionId:n=n.child(e.collectionId)}let o=[];s.where&&(o=function(e){var t;let r=function e(t){return void 0!==t.unaryFilter?function(e){switch(e.unaryFilter.op){case"IS_NAN":let t=ry(e.unaryFilter.field);return e4.create(t,"==",{doubleValue:NaN});case"IS_NULL":let r=ry(e.unaryFilter.field);return e4.create(r,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":let n=ry(e.unaryFilter.field);return e4.create(n,"!=",{doubleValue:NaN});case"IS_NOT_NULL":let s=ry(e.unaryFilter.field);return e4.create(s,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":return S(61313);default:return S(60726)}}(t):void 0!==t.fieldFilter?e4.create(ry(t.fieldFilter.field),function(e){switch(e){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":return S(58110);default:return S(50506)}}(t.fieldFilter.op),t.fieldFilter.value):void 0!==t.compositeFilter?e6.create(t.compositeFilter.filters.map(t=>e(t)),function(e){switch(e){case"AND":return"and";case"OR":return"or";default:return S(1026)}}(t.compositeFilter.op)):S(30097,{filter:t})}(e);return r instanceof e6&&e8(t=r)&&e5(t)?r.getFilters():[r]}(s.where));let l=[];s.orderBy&&(l=s.orderBy.map(e=>new e2(ry(e.field),function(e){switch(e){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}}(e.direction))));let u=null;s.limit&&(u=null==(r="object"==typeof(t=s.limit)?t.value:t)?null:r);let h=null;s.startAt&&(h=function(e){let t=!!e.before;return new eZ(e.values||[],t)}(s.startAt));let c=null;return s.endAt&&(c=function(e){let t=!e.before;return new eZ(e.values||[],t)}(s.endAt)),new tc(n,a,l,o,u,"F",h,c)}({parent:e.parent,structuredQuery:e.structuredQuery});return"LAST"===e.limitType?tg(t,t.limit,"L"):t}(t.bundledQuery),readTime:ru(t.readTime)}),ei.resolve()}}class rM{constructor(){this.overlays=new ec(H.comparator),this.qr=new Map}getOverlay(e,t){return ei.resolve(this.overlays.get(t))}getOverlays(e,t){let r=tI();return ei.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&r.set(t,e)})).next(()=>r)}saveOverlays(e,t,r){return r.forEach((r,n)=>{this.St(e,t,n)}),ei.resolve()}removeOverlaysForBatchId(e,t,r){let n=this.qr.get(r);return void 0!==n&&(n.forEach(e=>this.overlays=this.overlays.remove(e)),this.qr.delete(r)),ei.resolve()}getOverlaysForCollection(e,t,r){let n=tI(),s=t.length+1,i=new H(t.child("")),a=this.overlays.getIteratorFrom(i);for(;a.hasNext();){let e=a.getNext().value,i=e.getKey();if(!t.isPrefixOf(i.path))break;i.path.length===s&&e.largestBatchId>r&&n.set(e.getKey(),e)}return ei.resolve(n)}getOverlaysForCollectionGroup(e,t,r,n){let s=new ec((e,t)=>e-t),i=this.overlays.getIterator();for(;i.hasNext();){let e=i.getNext().value;if(e.getKey().getCollectionGroup()===t&&e.largestBatchId>r){let t=s.get(e.largestBatchId);null===t&&(t=tI(),s=s.insert(e.largestBatchId,t)),t.set(e.getKey(),e)}}let a=tI(),o=s.getIterator();for(;o.hasNext()&&(o.getNext().value.forEach((e,t)=>a.set(e,t)),!(a.size()>=n)););return ei.resolve(a)}St(e,t,r){let n=this.overlays.get(r.key);if(null!==n){let e=this.qr.get(n.largestBatchId).delete(r.key);this.qr.set(n.largestBatchId,e)}this.overlays=this.overlays.insert(r.key,new tX(t,r));let s=this.qr.get(t);void 0===s&&(s=tA(),this.qr.set(t,s)),this.qr.set(t,s.add(r.key))}}class rP{constructor(){this.sessionToken=ev.EMPTY_BYTE_STRING}getSessionToken(e){return ei.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,ei.resolve()}}class rF{constructor(){this.Qr=new ef(rU.$r),this.Ur=new ef(rU.Kr)}isEmpty(){return this.Qr.isEmpty()}addReference(e,t){let r=new rU(e,t);this.Qr=this.Qr.add(r),this.Ur=this.Ur.add(r)}Wr(e,t){e.forEach(e=>this.addReference(e,t))}removeReference(e,t){this.Gr(new rU(e,t))}zr(e,t){e.forEach(e=>this.removeReference(e,t))}jr(e){let t=new H(new K([])),r=new rU(t,e),n=new rU(t,e+1),s=[];return this.Ur.forEachInRange([r,n],e=>{this.Gr(e),s.push(e.key)}),s}Jr(){this.Qr.forEach(e=>this.Gr(e))}Gr(e){this.Qr=this.Qr.delete(e),this.Ur=this.Ur.delete(e)}Hr(e){let t=new H(new K([])),r=new rU(t,e),n=new rU(t,e+1),s=tA();return this.Ur.forEachInRange([r,n],e=>{s=s.add(e.key)}),s}containsKey(e){let t=new rU(e,0),r=this.Qr.firstAfterOrEqual(t);return null!==r&&e.isEqual(r.key)}}class rU{constructor(e,t){this.key=e,this.Yr=t}static $r(e,t){return H.comparator(e.key,t.key)||U(e.Yr,t.Yr)}static Kr(e,t){return U(e.Yr,t.Yr)||H.comparator(e.key,t.key)}}class rq{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.tr=1,this.Zr=new ef(rU.$r)}checkEmpty(e){return ei.resolve(0===this.mutationQueue.length)}addMutationBatch(e,t,r,n){let s=this.tr;this.tr++,this.mutationQueue.length>0&&this.mutationQueue[this.mutationQueue.length-1];let i=new tY(s,t,r,n);for(let t of(this.mutationQueue.push(i),n))this.Zr=this.Zr.add(new rU(t.key,s)),this.indexManager.addToCollectionParentIndex(e,t.key.path.popLast());return ei.resolve(i)}lookupMutationBatch(e,t){return ei.resolve(this.Xr(t))}getNextMutationBatchAfterBatchId(e,t){let r=this.ei(t+1),n=r<0?0:r;return ei.resolve(this.mutationQueue.length>n?this.mutationQueue[n]:null)}getHighestUnacknowledgedBatchId(){return ei.resolve(0===this.mutationQueue.length?-1:this.tr-1)}getAllMutationBatches(e){return ei.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){let r=new rU(t,0),n=new rU(t,1/0),s=[];return this.Zr.forEachInRange([r,n],e=>{let t=this.Xr(e.Yr);s.push(t)}),ei.resolve(s)}getAllMutationBatchesAffectingDocumentKeys(e,t){let r=new ef(U);return t.forEach(e=>{let t=new rU(e,0),n=new rU(e,1/0);this.Zr.forEachInRange([t,n],e=>{r=r.add(e.Yr)})}),ei.resolve(this.ti(r))}getAllMutationBatchesAffectingQuery(e,t){let r=t.path,n=r.length+1,s=r;H.isDocumentKey(s)||(s=s.child(""));let i=new rU(new H(s),0),a=new ef(U);return this.Zr.forEachWhile(e=>{let t=e.key.path;return!!r.isPrefixOf(t)&&(t.length===n&&(a=a.add(e.Yr)),!0)},i),ei.resolve(this.ti(a))}ti(e){let t=[];return e.forEach(e=>{let r=this.Xr(e);null!==r&&t.push(r)}),t}removeMutationBatch(e,t){N(0===this.ni(t.batchId,"removed"),55003),this.mutationQueue.shift();let r=this.Zr;return ei.forEach(t.mutations,n=>{let s=new rU(n.key,t.batchId);return r=r.delete(s),this.referenceDelegate.markPotentiallyOrphaned(e,n.key)}).next(()=>{this.Zr=r})}ir(e){}containsKey(e,t){let r=new rU(t,0),n=this.Zr.firstAfterOrEqual(r);return ei.resolve(t.isEqual(n&&n.key))}performConsistencyCheck(e){return this.mutationQueue.length,ei.resolve()}ni(e,t){return this.ei(e)}ei(e){return 0===this.mutationQueue.length?0:e-this.mutationQueue[0].batchId}Xr(e){let t=this.ei(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}}class rz{constructor(e){this.ri=e,this.docs=new ec(H.comparator),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){let r=t.key,n=this.docs.get(r),s=n?n.size:0,i=this.ri(t);return this.docs=this.docs.insert(r,{document:t.mutableCopy(),size:i}),this.size+=i-s,this.indexManager.addToCollectionParentIndex(e,r.path.popLast())}removeEntry(e){let t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){let r=this.docs.get(t);return ei.resolve(r?r.document.mutableCopy():eJ.newInvalidDocument(t))}getEntries(e,t){let r=tT;return t.forEach(e=>{let t=this.docs.get(e);r=r.insert(e,t?t.document.mutableCopy():eJ.newInvalidDocument(e))}),ei.resolve(r)}getDocumentsMatchingQuery(e,t,r,n){let s=tT,i=t.path,a=new H(i.child("__id-9223372036854775808__")),o=this.docs.getIteratorFrom(a);for(;o.hasNext();){let{key:e,value:{document:a}}=o.getNext();if(!i.isPrefixOf(e.path))break;e.path.length>i.length+1||0>=function(e,t){let r=e.readTime.compareTo(t.readTime);return 0!==r||0!==(r=H.comparator(e.documentKey,t.documentKey))?r:U(e.largestBatchId,t.largestBatchId)}(new er(a.readTime,a.key,-1),r)||(n.has(a.key)||tw(t,a))&&(s=s.insert(a.key,a.mutableCopy()))}return ei.resolve(s)}getAllFromCollectionGroup(e,t,r,n){S(9500)}ii(e,t){return ei.forEach(this.docs,e=>t(e))}newChangeBuffer(e){return new r$(this)}getSize(e){return ei.resolve(this.size)}}class r$ extends rR{constructor(e){super(),this.Nr=e}applyChanges(e){let t=[];return this.changes.forEach((r,n)=>{n.isValidDocument()?t.push(this.Nr.addEntry(e,n)):this.Nr.removeEntry(r)}),ei.waitFor(t)}getFromCache(e,t){return this.Nr.getEntry(e,t)}getAllFromCache(e,t){return this.Nr.getEntries(e,t)}}class rB{constructor(e){this.persistence=e,this.si=new t_(e=>tl(e),tu),this.lastRemoteSnapshotVersion=ee.min(),this.highestTargetId=0,this.oi=0,this._i=new rF,this.targetCount=0,this.ai=rN.ur()}forEachTarget(e,t){return this.si.forEach((e,r)=>t(r)),ei.resolve()}getLastRemoteSnapshotVersion(e){return ei.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return ei.resolve(this.oi)}allocateTargetId(e){return this.highestTargetId=this.ai.next(),ei.resolve(this.highestTargetId)}setTargetsMetadata(e,t,r){return r&&(this.lastRemoteSnapshotVersion=r),t>this.oi&&(this.oi=t),ei.resolve()}Pr(e){this.si.set(e.target,e);let t=e.targetId;t>this.highestTargetId&&(this.ai=new rN(t),this.highestTargetId=t),e.sequenceNumber>this.oi&&(this.oi=e.sequenceNumber)}addTargetData(e,t){return this.Pr(t),this.targetCount+=1,ei.resolve()}updateTargetData(e,t){return this.Pr(t),ei.resolve()}removeTargetData(e,t){return this.si.delete(t.target),this._i.jr(t.targetId),this.targetCount-=1,ei.resolve()}removeTargets(e,t,r){let n=0,s=[];return this.si.forEach((i,a)=>{a.sequenceNumber<=t&&null===r.get(a.targetId)&&(this.si.delete(i),s.push(this.removeMatchingKeysForTargetId(e,a.targetId)),n++)}),ei.waitFor(s).next(()=>n)}getTargetCount(e){return ei.resolve(this.targetCount)}getTargetData(e,t){let r=this.si.get(t)||null;return ei.resolve(r)}addMatchingKeys(e,t,r){return this._i.Wr(t,r),ei.resolve()}removeMatchingKeys(e,t,r){this._i.zr(t,r);let n=this.persistence.referenceDelegate,s=[];return n&&t.forEach(t=>{s.push(n.markPotentiallyOrphaned(e,t))}),ei.waitFor(s)}removeMatchingKeysForTargetId(e,t){return this._i.jr(t),ei.resolve()}getMatchingKeysForTargetId(e,t){let r=this._i.Hr(t);return ei.resolve(r)}containsKey(e,t){return ei.resolve(this._i.containsKey(t))}}class rj{constructor(e,t){this.ui={},this.overlays={},this.ci=new eo(0),this.li=!1,this.li=!0,this.hi=new rP,this.referenceDelegate=e(this),this.Pi=new rB(this),this.indexManager=new rT,this.remoteDocumentCache=new rz(e=>this.referenceDelegate.Ti(e)),this.serializer=new rE(t),this.Ii=new rL(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.li=!1,Promise.resolve()}get started(){return this.li}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new rM,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let r=this.ui[e.toKey()];return r||(r=new rq(t,this.referenceDelegate),this.ui[e.toKey()]=r),r}getGlobalsCache(){return this.hi}getTargetCache(){return this.Pi}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.Ii}runTransaction(e,t,r){E("MemoryPersistence","Starting transaction:",e);let n=new rK(this.ci.next());return this.referenceDelegate.Ei(),r(n).next(e=>this.referenceDelegate.di(n).next(()=>e)).toPromise().then(e=>(n.raiseOnCommittedEvent(),e))}Ai(e,t){return ei.or(Object.values(this.ui).map(r=>()=>r.containsKey(e,t)))}}class rK extends en{constructor(e){super(),this.currentSequenceNumber=e}}class rG{constructor(e){this.persistence=e,this.Ri=new rF,this.Vi=null}static mi(e){return new rG(e)}get fi(){if(this.Vi)return this.Vi;throw S(60996)}addReference(e,t,r){return this.Ri.addReference(r,t),this.fi.delete(r.toString()),ei.resolve()}removeReference(e,t,r){return this.Ri.removeReference(r,t),this.fi.add(r.toString()),ei.resolve()}markPotentiallyOrphaned(e,t){return this.fi.add(t.toString()),ei.resolve()}removeTarget(e,t){this.Ri.jr(t.targetId).forEach(e=>this.fi.add(e.toString()));let r=this.persistence.getTargetCache();return r.getMatchingKeysForTargetId(e,t.targetId).next(e=>{e.forEach(e=>this.fi.add(e.toString()))}).next(()=>r.removeTargetData(e,t))}Ei(){this.Vi=new Set}di(e){let t=this.persistence.getRemoteDocumentCache().newChangeBuffer();return ei.forEach(this.fi,r=>{let n=H.fromPath(r);return this.gi(e,n).next(e=>{e||t.removeEntry(n,ee.min())})}).next(()=>(this.Vi=null,t.apply(e)))}updateLimboDocument(e,t){return this.gi(e,t).next(e=>{e?this.fi.delete(t.toString()):this.fi.add(t.toString())})}Ti(e){return 0}gi(e,t){return ei.or([()=>ei.resolve(this.Ri.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Ai(e,t)])}}class rQ{constructor(e,t){this.persistence=e,this.pi=new t_(e=>(function(e){let t="";for(let r=0;r<e.length;r++)t.length>0&&(t+="\x01\x01"),t=function(e,t){let r=t,n=e.length;for(let t=0;t<n;t++){let n=e.charAt(t);switch(n){case"\0":r+="\x01\x10";break;case"\x01":r+="\x01\x11";break;default:r+=n}}return r}(e.get(r),t);return t+"\x01\x01"})(e.path),(e,t)=>e.isEqual(t)),this.garbageCollector=new rx(this,t)}static mi(e,t){return new rQ(e,t)}Ei(){}di(e){return ei.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}gr(e){let t=this.wr(e);return this.persistence.getTargetCache().getTargetCount(e).next(e=>t.next(t=>e+t))}wr(e){let t=0;return this.pr(e,e=>{t++}).next(()=>t)}pr(e,t){return ei.forEach(this.pi,(r,n)=>this.br(e,r,n).next(e=>e?ei.resolve():t(n)))}removeTargets(e,t,r){return this.persistence.getTargetCache().removeTargets(e,t,r)}removeOrphanedDocuments(e,t){let r=0,n=this.persistence.getRemoteDocumentCache(),s=n.newChangeBuffer();return n.ii(e,n=>this.br(e,n,t).next(e=>{e||(r++,s.removeEntry(n,ee.min()))})).next(()=>s.apply(e)).next(()=>r)}markPotentiallyOrphaned(e,t){return this.pi.set(t,e.currentSequenceNumber),ei.resolve()}removeTarget(e,t){let r=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,r)}addReference(e,t,r){return this.pi.set(r,e.currentSequenceNumber),ei.resolve()}removeReference(e,t,r){return this.pi.set(r,e.currentSequenceNumber),ei.resolve()}updateLimboDocument(e,t){return this.pi.set(t,e.currentSequenceNumber),ei.resolve()}Ti(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=function e(t){switch(eM(t)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:let r=eb(t);return r?16+e(r):16;case 5:return 2*t.stringValue.length;case 6:return eT(t.bytesValue).approximateByteSize();case 7:return t.referenceValue.length;case 9:return(t.arrayValue.values||[]).reduce((t,r)=>t+e(r),0);case 10:case 11:var n;let s;return n=t.mapValue,s=0,eh(n.fields,(t,r)=>{s+=t.length+e(r)}),s;default:throw S(13486,{value:t})}}(e.data.value)),t}br(e,t,r){return ei.or([()=>this.persistence.Ai(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{let e=this.pi.get(t);return ei.resolve(void 0!==e&&e>r)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}}class rH{constructor(e,t,r,n){this.targetId=e,this.fromCache=t,this.Es=r,this.ds=n}static As(e,t){let r=tA(),n=tA();for(let e of t.docChanges)switch(e.type){case 0:r=r.add(e.doc.key);break;case 1:n=n.add(e.doc.key)}return new rH(e,t.fromCache,r,n)}}class rW{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}}class rY{constructor(){this.Rs=!1,this.Vs=!1,this.fs=100,this.gs=(0,h.nr)()?8:function(e){let t=e.match(/Android ([\d.]+)/i);return Number(t?t[1].split(".").slice(0,2).join("."):"-1")}((0,h.ZQ)())>0?6:4}initialize(e,t){this.ps=e,this.indexManager=t,this.Rs=!0}getDocumentsMatchingQuery(e,t,r,n){let s={result:null};return this.ys(e,t).next(e=>{s.result=e}).next(()=>{if(!s.result)return this.ws(e,t,n,r).next(e=>{s.result=e})}).next(()=>{if(s.result)return;let r=new rW;return this.Ss(e,t,r).next(n=>{if(s.result=n,this.Vs)return this.bs(e,t,r,n.size)})}).next(()=>s.result)}bs(e,t,r,n){return r.documentReadCount<this.fs?(w()<=u.$b.DEBUG&&E("QueryEngine","SDK will not create cache indexes for query:",tv(t),"since it only creates cache indexes for collection contains","more than or equal to",this.fs,"documents"),ei.resolve()):(w()<=u.$b.DEBUG&&E("QueryEngine","Query:",tv(t),"scans",r.documentReadCount,"local documents and returns",n,"documents as results."),r.documentReadCount>this.gs*n?(w()<=u.$b.DEBUG&&E("QueryEngine","The SDK decides to create cache indexes for query:",tv(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,tf(t))):ei.resolve())}ys(e,t){if(td(t))return ei.resolve(null);let r=tf(t);return this.indexManager.getIndexType(e,r).next(n=>0===n?null:(null!==t.limit&&1===n&&(r=tf(t=tg(t,null,"F"))),this.indexManager.getDocumentsMatchingTarget(e,r).next(n=>{let s=tA(...n);return this.ps.getDocuments(e,s).next(n=>this.indexManager.getMinOffset(e,r).next(r=>{let i=this.Ds(t,n);return this.Cs(t,i,s,r.readTime)?this.ys(e,tg(t,null,"F")):this.vs(e,i,t,r)}))})))}ws(e,t,r,n){return td(t)||n.isEqual(ee.min())?ei.resolve(null):this.ps.getDocuments(e,r).next(s=>{let i=this.Ds(t,s);return this.Cs(t,i,r,n)?ei.resolve(null):(w()<=u.$b.DEBUG&&E("QueryEngine","Re-using previous result from %s to execute query: %s",n.toString(),tv(t)),this.vs(e,i,t,function(e,t){let r=e.toTimestamp().seconds,n=e.toTimestamp().nanoseconds+1;return new er(ee.fromTimestamp(1e9===n?new Z(r+1,0):new Z(r,n)),H.empty(),-1)}(n,0)).next(e=>e))})}Ds(e,t){let r=new ef(tE(e));return t.forEach((t,n)=>{tw(e,n)&&(r=r.add(n))}),r}Cs(e,t,r,n){if(null===e.limit)return!1;if(r.size!==t.size)return!0;let s="F"===e.limitType?t.last():t.first();return!!s&&(s.hasPendingWrites||s.version.compareTo(n)>0)}Ss(e,t,r){return w()<=u.$b.DEBUG&&E("QueryEngine","Using full collection scan to execute query:",tv(t)),this.ps.getDocumentsMatchingQuery(e,t,er.min(),r)}vs(e,t,r,n){return this.ps.getDocumentsMatchingQuery(e,r,n).next(e=>(t.forEach(t=>{e=e.insert(t.key,t)}),e))}}let rX="LocalStore";class rJ{constructor(e,t,r,n){this.persistence=e,this.Fs=t,this.serializer=n,this.Ms=new ec(U),this.xs=new t_(e=>tl(e),tu),this.Os=new Map,this.Ns=e.getRemoteDocumentCache(),this.Pi=e.getTargetCache(),this.Ii=e.getBundleCache(),this.Bs(r)}Bs(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new rO(this.Ns,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Ns.setIndexManager(this.indexManager),this.Fs.initialize(this.localDocuments,this.indexManager)}collectGarbage(e){return this.persistence.runTransaction("Collect garbage","readwrite-primary",t=>e.collect(t,this.Ms))}}async function rZ(e,t){return await e.persistence.runTransaction("Handle user change","readonly",r=>{let n;return e.mutationQueue.getAllMutationBatches(r).next(s=>(n=s,e.Bs(t),e.mutationQueue.getAllMutationBatches(r))).next(t=>{let s=[],i=[],a=tA();for(let e of n)for(let t of(s.push(e.batchId),e.mutations))a=a.add(t.key);for(let e of t)for(let t of(i.push(e.batchId),e.mutations))a=a.add(t.key);return e.localDocuments.getDocuments(r,a).next(e=>({Ls:e,removedBatchIds:s,addedBatchIds:i}))})})}function r0(e){return e.persistence.runTransaction("Get last remote snapshot version","readonly",t=>e.Pi.getLastRemoteSnapshotVersion(t))}async function r1(e,t,r){let n=e.Ms.get(t);try{r||await e.persistence.runTransaction("Release target",r?"readwrite":"readwrite-primary",t=>e.persistence.referenceDelegate.removeTarget(t,n))}catch(e){if(!ea(e))throw e;E(rX,`Failed to update sequence numbers for target ${t}: ${e}`)}e.Ms=e.Ms.remove(t),e.xs.delete(n.target)}function r2(e,t,r){let n=ee.min(),s=tA();return e.persistence.runTransaction("Execute query","readwrite",i=>(function(e,t,r){let n=e.xs.get(r);return void 0!==n?ei.resolve(e.Ms.get(n)):e.Pi.getTargetData(t,r)})(e,i,tf(t)).next(t=>{if(t)return n=t.lastLimboFreeSnapshotVersion,e.Pi.getMatchingKeysForTargetId(i,t.targetId).next(e=>{s=e})}).next(()=>e.Fs.getDocumentsMatchingQuery(i,t,r?n:ee.min(),r?s:tA())).next(r=>{var n,i,a;let o;return n=e,i=t.collectionGroup||(t.path.length%2==1?t.path.lastSegment():t.path.get(t.path.length-2)),a=r,o=n.Os.get(i)||ee.min(),a.forEach((e,t)=>{t.readTime.compareTo(o)>0&&(o=t.readTime)}),n.Os.set(i,o),{documents:r,Qs:s}}))}class r3{constructor(){this.activeTargetIds=tb}zs(e){this.activeTargetIds=this.activeTargetIds.add(e)}js(e){this.activeTargetIds=this.activeTargetIds.delete(e)}Gs(){return JSON.stringify({activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()})}}class r4{constructor(){this.Mo=new r3,this.xo={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,r){}addLocalQueryTarget(e,t=!0){return t&&this.Mo.zs(e),this.xo[e]||"not-current"}updateQueryState(e,t,r){this.xo[e]=t}removeLocalQueryTarget(e){this.Mo.js(e)}isLocalQueryTarget(e){return this.Mo.activeTargetIds.has(e)}clearQueryState(e){delete this.xo[e]}getAllActiveQueryTargets(){return this.Mo.activeTargetIds}isActiveQueryTarget(e){return this.Mo.activeTargetIds.has(e)}start(){return this.Mo=new r3,Promise.resolve()}handleUserChange(e,t,r){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}}class r6{Oo(e){}shutdown(){}}let r5="ConnectivityMonitor";class r8{constructor(){this.No=()=>this.Bo(),this.Lo=()=>this.ko(),this.qo=[],this.Qo()}Oo(e){this.qo.push(e)}shutdown(){window.removeEventListener("online",this.No),window.removeEventListener("offline",this.Lo)}Qo(){window.addEventListener("online",this.No),window.addEventListener("offline",this.Lo)}Bo(){for(let e of(E(r5,"Network connectivity changed: AVAILABLE"),this.qo))e(0)}ko(){for(let e of(E(r5,"Network connectivity changed: UNAVAILABLE"),this.qo))e(1)}static v(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}let r9=null;function r7(){return null===r9?r9=0x10000000+Math.round(0x80000000*Math.random()):r9++,"0x"+r9.toString(16)}let ne="RestConnection",nt={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class nr{get $o(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;let t=e.ssl?"https":"http",r=encodeURIComponent(this.databaseId.projectId),n=encodeURIComponent(this.databaseId.database);this.Uo=t+"://"+e.host,this.Ko=`projects/${r}/databases/${n}`,this.Wo=this.databaseId.database===ex?`project_id=${r}`:`project_id=${r}&database_id=${n}`}Go(e,t,r,n,s){let i=r7(),a=this.zo(e,t.toUriEncodedString());E(ne,`Sending RPC '${e}' ${i}:`,a,r);let o={"google-cloud-resource-prefix":this.Ko,"x-goog-request-params":this.Wo};this.jo(o,n,s);let{host:l}=new URL(a),u=(0,h.zJ)(l);return this.Jo(e,a,o,r,u).then(t=>(E(ne,`Received RPC '${e}' ${i}: `,t),t),t=>{throw T(ne,`RPC '${e}' ${i} failed with error: `,t,"url: ",a,"request:",r),t})}Ho(e,t,r,n,s,i){return this.Go(e,t,r,n,s)}jo(e,t,r){e["X-Goog-Api-Client"]="gl-js/ fire/"+y,e["Content-Type"]="text/plain",this.databaseInfo.appId&&(e["X-Firebase-GMPID"]=this.databaseInfo.appId),t&&t.headers.forEach((t,r)=>e[r]=t),r&&r.headers.forEach((t,r)=>e[r]=t)}zo(e,t){let r=nt[e];return`${this.Uo}/v1/${t}:${r}`}terminate(){}}class nn{constructor(e){this.Yo=e.Yo,this.Zo=e.Zo}Xo(e){this.e_=e}t_(e){this.n_=e}r_(e){this.i_=e}onMessage(e){this.s_=e}close(){this.Zo()}send(e){this.Yo(e)}o_(){this.e_()}__(){this.n_()}a_(e){this.i_(e)}u_(e){this.s_(e)}}let ns="WebChannelConnection";class ni extends nr{constructor(e){super(e),this.c_=[],this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}Jo(e,t,r,n,s){let i=r7();return new Promise((s,a)=>{let o=new d.ZS;o.setWithCredentials(!0),o.listenOnce(d.Bx.COMPLETE,()=>{try{switch(o.getLastErrorCode()){case d.O4.NO_ERROR:let t=o.getResponseJson();E(ns,`XHR for RPC '${e}' ${i} received:`,JSON.stringify(t)),s(t);break;case d.O4.TIMEOUT:E(ns,`RPC '${e}' ${i} timed out`),a(new b(A.DEADLINE_EXCEEDED,"Request time out"));break;case d.O4.HTTP_ERROR:let r=o.getStatus();if(E(ns,`RPC '${e}' ${i} failed with status:`,r,"response text:",o.getResponseText()),r>0){let e=o.getResponseJson();Array.isArray(e)&&(e=e[0]);let t=e?.error;if(t&&t.status&&t.message){let e=function(e){let t=e.toLowerCase().replace(/_/g,"-");return Object.values(A).indexOf(t)>=0?t:A.UNKNOWN}(t.status);a(new b(e,t.message))}else a(new b(A.UNKNOWN,"Server responded with status "+o.getStatus()))}else a(new b(A.UNAVAILABLE,"Connection failed."));break;default:S(9055,{l_:e,streamId:i,h_:o.getLastErrorCode(),P_:o.getLastError()})}}finally{E(ns,`RPC '${e}' ${i} completed.`)}});let l=JSON.stringify(n);E(ns,`RPC '${e}' ${i} sending request:`,n),o.send(t,"POST",l,r,15)})}T_(e,t,r){let s=r7(),i=[this.Uo,"/","google.firestore.v1.Firestore","/",e,"/channel"],a=(0,d.fF)(),o=(0,d.Ao)(),l={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/${this.databaseId.database}`},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},u=this.longPollingOptions.timeoutSeconds;void 0!==u&&(l.longPollingTimeout=Math.round(1e3*u)),this.useFetchStreams&&(l.useFetchStreams=!0),this.jo(l.initMessageHeaders,t,r),l.encodeInitMessageHeaders=!0;let h=i.join("");E(ns,`Creating RPC '${e}' stream ${s}: ${h}`,l);let c=a.createWebChannel(h,l);this.I_(c);let m=!1,f=!1,g=new nn({Yo:t=>{f?E(ns,`Not sending because RPC '${e}' stream ${s} is closed:`,t):(m||(E(ns,`Opening RPC '${e}' stream ${s} transport.`),c.open(),m=!0),E(ns,`RPC '${e}' stream ${s} sending:`,t),c.send(t))},Zo:()=>c.close()}),p=(e,t,r)=>{e.listen(t,e=>{try{r(e)}catch(e){setTimeout(()=>{throw e},0)}})};return p(c,d.iO.EventType.OPEN,()=>{f||(E(ns,`RPC '${e}' stream ${s} transport opened.`),g.o_())}),p(c,d.iO.EventType.CLOSE,()=>{f||(f=!0,E(ns,`RPC '${e}' stream ${s} transport closed`),g.a_(),this.E_(c))}),p(c,d.iO.EventType.ERROR,t=>{f||(f=!0,T(ns,`RPC '${e}' stream ${s} transport errored. Name:`,t.name,"Message:",t.message),g.a_(new b(A.UNAVAILABLE,"The operation could not be completed")))}),p(c,d.iO.EventType.MESSAGE,t=>{if(!f){let r=t.data[0];N(!!r,16349);let i=r?.error||r[0]?.error;if(i){E(ns,`RPC '${e}' stream ${s} received error:`,i);let t=i.status,r=function(e){let t=n[e];if(void 0!==t)return tZ(t)}(t),a=i.message;void 0===r&&(r=A.INTERNAL,a="Unknown error status: "+t+" with message "+i.message),f=!0,g.a_(new b(r,a)),c.close()}else E(ns,`RPC '${e}' stream ${s} received:`,r),g.u_(r)}}),p(o,d.Jh.STAT_EVENT,t=>{t.stat===d.ro.PROXY?E(ns,`RPC '${e}' stream ${s} detected buffering proxy`):t.stat===d.ro.NOPROXY&&E(ns,`RPC '${e}' stream ${s} detected no buffering proxy`)}),setTimeout(()=>{g.__()},0),g}terminate(){this.c_.forEach(e=>e.close()),this.c_=[]}I_(e){this.c_.push(e)}E_(e){this.c_=this.c_.filter(t=>t===e)}}function na(){return"undefined"!=typeof document?document:null}class no{constructor(e,t,r=1e3,n=1.5,s=6e4){this.Mi=e,this.timerId=t,this.d_=r,this.A_=n,this.R_=s,this.V_=0,this.m_=null,this.f_=Date.now(),this.reset()}reset(){this.V_=0}g_(){this.V_=this.R_}p_(e){this.cancel();let t=Math.floor(this.V_+this.y_()),r=Math.max(0,Date.now()-this.f_),n=Math.max(0,t-r);n>0&&E("ExponentialBackoff",`Backing off for ${n} ms (base delay: ${this.V_} ms, delay with jitter: ${t} ms, last attempt: ${r} ms ago)`),this.m_=this.Mi.enqueueAfterDelay(this.timerId,n,()=>(this.f_=Date.now(),e())),this.V_*=this.A_,this.V_<this.d_&&(this.V_=this.d_),this.V_>this.R_&&(this.V_=this.R_)}w_(){null!==this.m_&&(this.m_.skipDelay(),this.m_=null)}cancel(){null!==this.m_&&(this.m_.cancel(),this.m_=null)}y_(){return(Math.random()-.5)*this.V_}}let nl="PersistentStream";class nu{constructor(e,t,r,n,s,i,a,o){this.Mi=e,this.S_=r,this.b_=n,this.connection=s,this.authCredentialsProvider=i,this.appCheckCredentialsProvider=a,this.listener=o,this.state=0,this.D_=0,this.C_=null,this.v_=null,this.stream=null,this.F_=0,this.M_=new no(e,t)}x_(){return 1===this.state||5===this.state||this.O_()}O_(){return 2===this.state||3===this.state}start(){this.F_=0,4!==this.state?this.auth():this.N_()}async stop(){this.x_()&&await this.close(0)}B_(){this.state=0,this.M_.reset()}L_(){this.O_()&&null===this.C_&&(this.C_=this.Mi.enqueueAfterDelay(this.S_,6e4,()=>this.k_()))}q_(e){this.Q_(),this.stream.send(e)}async k_(){if(this.O_())return this.close(0)}Q_(){this.C_&&(this.C_.cancel(),this.C_=null)}U_(){this.v_&&(this.v_.cancel(),this.v_=null)}async close(e,t){this.Q_(),this.U_(),this.M_.cancel(),this.D_++,4!==e?this.M_.reset():t&&t.code===A.RESOURCE_EXHAUSTED?(_(t.toString()),_("Using maximum backoff delay to prevent overloading the backend."),this.M_.g_()):t&&t.code===A.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.K_(),this.stream.close(),this.stream=null),this.state=e,await this.listener.r_(t)}K_(){}auth(){this.state=1;let e=this.W_(this.D_),t=this.D_;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then(([e,r])=>{this.D_===t&&this.G_(e,r)},t=>{e(()=>{let e=new b(A.UNKNOWN,"Fetching auth token failed: "+t.message);return this.z_(e)})})}G_(e,t){let r=this.W_(this.D_);this.stream=this.j_(e,t),this.stream.Xo(()=>{r(()=>this.listener.Xo())}),this.stream.t_(()=>{r(()=>(this.state=2,this.v_=this.Mi.enqueueAfterDelay(this.b_,1e4,()=>(this.O_()&&(this.state=3),Promise.resolve())),this.listener.t_()))}),this.stream.r_(e=>{r(()=>this.z_(e))}),this.stream.onMessage(e=>{r(()=>1==++this.F_?this.J_(e):this.onNext(e))})}N_(){this.state=5,this.M_.p_(async()=>{this.state=0,this.start()})}z_(e){return E(nl,`close with error: ${e}`),this.stream=null,this.close(4,e)}W_(e){return t=>{this.Mi.enqueueAndForget(()=>this.D_===e?t():(E(nl,"stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve()))}}}class nh extends nu{constructor(e,t,r,n,s,i){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,r,n,i),this.serializer=s}j_(e,t){return this.connection.T_("Listen",e,t)}J_(e){return this.onNext(e)}onNext(e){this.M_.reset();let t=function(e,t){let r;if("targetChange"in t){var n,s;t.targetChange;let i="NO_CHANGE"===(n=t.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===n?1:"REMOVE"===n?2:"CURRENT"===n?3:"RESET"===n?4:S(39313,{state:n}),a=t.targetChange.targetIds||[],o=(s=t.targetChange.resumeToken,e.useProto3Json?(N(void 0===s||"string"==typeof s,58123),ev.fromBase64String(s||"")):(N(void 0===s||s instanceof m||s instanceof Uint8Array,16193),ev.fromUint8Array(s||new Uint8Array))),l=t.targetChange.cause;r=new t7(i,a,o,l&&new b(void 0===l.code?A.UNKNOWN:tZ(l.code),l.message||"")||null)}else if("documentChange"in t){t.documentChange;let n=t.documentChange;n.document,n.document.name,n.document.updateTime;let s=rd(e,n.document.name),i=ru(n.document.updateTime),a=n.document.createTime?ru(n.document.createTime):ee.min(),o=new eX({mapValue:{fields:n.document.fields}}),l=eJ.newFoundDocument(s,i,a,o);r=new t8(n.targetIds||[],n.removedTargetIds||[],l.key,l)}else if("documentDelete"in t){t.documentDelete;let n=t.documentDelete;n.document;let s=rd(e,n.document),i=n.readTime?ru(n.readTime):ee.min(),a=eJ.newNoDocument(s,i);r=new t8([],n.removedTargetIds||[],a.key,a)}else if("documentRemove"in t){t.documentRemove;let n=t.documentRemove;n.document;let s=rd(e,n.document);r=new t8([],n.removedTargetIds||[],s,null)}else{if(!("filter"in t))return S(11601,{Rt:t});{t.filter;let e=t.filter;e.targetId;let{count:n=0,unchangedNames:s}=e,i=new tJ(n,s);r=new t9(e.targetId,i)}}return r}(this.serializer,e),r=function(e){if(!("targetChange"in e))return ee.min();let t=e.targetChange;return t.targetIds&&t.targetIds.length?ee.min():t.readTime?ru(t.readTime):ee.min()}(e);return this.listener.H_(t,r)}Y_(e){let t={};t.database=rf(this.serializer),t.addTarget=function(e,t){var r,n;let s,i=t.target;if((s=th(i)?{documents:{documents:[rm(e,i.path)]}}:{query:function(e,t){var r,n;let s,i={structuredQuery:{}},a=t.path;null!==t.collectionGroup?(s=a,i.structuredQuery.from=[{collectionId:t.collectionGroup,allDescendants:!0}]):(s=a.popLast(),i.structuredQuery.from=[{collectionId:a.lastSegment()}]),i.parent=rm(e,s);let o=function(e){if(0!==e.length)return function e(t){return t instanceof e4?function(e){if("=="===e.op){if(eG(e.value))return{unaryFilter:{field:rp(e.field),op:"IS_NAN"}};if(eK(e.value))return{unaryFilter:{field:rp(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(eG(e.value))return{unaryFilter:{field:rp(e.field),op:"IS_NOT_NAN"}};if(eK(e.value))return{unaryFilter:{field:rp(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:rp(e.field),op:ri[e.op],value:e.value}}}(t):t instanceof e6?function(t){let r=t.getFilters().map(t=>e(t));return 1===r.length?r[0]:{compositeFilter:{op:ra[t.op],filters:r}}}(t):S(54877,{filter:t})}(e6.create(e,"and"))}(t.filters);o&&(i.structuredQuery.where=o);let l=function(e){if(0!==e.length)return e.map(e=>({field:rp(e.field),direction:rs[e.dir]}))}(t.orderBy);l&&(i.structuredQuery.orderBy=l);let u=rl(e,t.limit);return null!==u&&(i.structuredQuery.limit=u),t.startAt&&(i.structuredQuery.startAt={before:(r=t.startAt).inclusive,values:r.position}),t.endAt&&(i.structuredQuery.endAt={before:!(n=t.endAt).inclusive,values:n.position}),{ft:i,parent:s}}(e,i).ft}).targetId=t.targetId,t.resumeToken.approximateByteSize()>0){r=t.resumeToken,s.resumeToken=e.useProto3Json?r.toBase64():r.toUint8Array();let n=rl(e,t.expectedCount);null!==n&&(s.expectedCount=n)}else if(t.snapshotVersion.compareTo(ee.min())>0){n=t.snapshotVersion.toTimestamp(),s.readTime=e.useProto3Json?`${new Date(1e3*n.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+n.nanoseconds).slice(-9)}Z`:{seconds:""+n.seconds,nanos:n.nanoseconds};let r=rl(e,t.expectedCount);null!==r&&(s.expectedCount=r)}return s}(this.serializer,e);let r=function(e,t){let r=function(e){switch(e){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return S(28987,{purpose:e})}}(t.purpose);return null==r?null:{"goog-listen-tags":r}}(this.serializer,e);r&&(t.labels=r),this.q_(t)}Z_(e){let t={};t.database=rf(this.serializer),t.removeTarget=e,this.q_(t)}}class nc{}class nd extends nc{constructor(e,t,r,n){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=r,this.serializer=n,this.ia=!1}sa(){if(this.ia)throw new b(A.FAILED_PRECONDITION,"The client has already been terminated.")}Go(e,t,r,n){return this.sa(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([s,i])=>this.connection.Go(e,rh(t,r),n,s,i)).catch(e=>{throw"FirebaseError"===e.name?(e.code===A.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new b(A.UNKNOWN,e.toString())})}Ho(e,t,r,n,s){return this.sa(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([i,a])=>this.connection.Ho(e,rh(t,r),n,i,a,s)).catch(e=>{throw"FirebaseError"===e.name?(e.code===A.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new b(A.UNKNOWN,e.toString())})}terminate(){this.ia=!0,this.connection.terminate()}}class nm{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this.oa=0,this._a=null,this.aa=!0}ua(){0===this.oa&&(this.ca("Unknown"),this._a=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,()=>(this._a=null,this.la("Backend didn't respond within 10 seconds."),this.ca("Offline"),Promise.resolve())))}ha(e){"Online"===this.state?this.ca("Unknown"):(this.oa++,this.oa>=1&&(this.Pa(),this.la(`Connection failed 1 times. Most recent error: ${e.toString()}`),this.ca("Offline")))}set(e){this.Pa(),this.oa=0,"Online"===e&&(this.aa=!1),this.ca(e)}ca(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}la(e){let t=`Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.aa?(_(t),this.aa=!1):E("OnlineStateTracker",t)}Pa(){null!==this._a&&(this._a.cancel(),this._a=null)}}let nf="RemoteStore";class ng{constructor(e,t,r,n,s){this.localStore=e,this.datastore=t,this.asyncQueue=r,this.remoteSyncer={},this.Ta=[],this.Ia=new Map,this.Ea=new Set,this.da=[],this.Aa=s,this.Aa.Oo(e=>{r.enqueueAndForget(async()=>{nS(this)&&(E(nf,"Restarting streams for network reachability change."),await async function(e){e.Ea.add(4),await ny(e),e.Ra.set("Unknown"),e.Ea.delete(4),await np(e)}(this))})}),this.Ra=new nm(r,n)}}async function np(e){if(nS(e))for(let t of e.da)await t(!0)}async function ny(e){for(let t of e.da)await t(!1)}function nv(e,t){e.Ia.has(t.targetId)||(e.Ia.set(t.targetId,t),nC(e)?nT(e):nR(e).O_()&&nE(e,t))}function nw(e,t){let r=nR(e);e.Ia.delete(t),r.O_()&&n_(e,t),0===e.Ia.size&&(r.O_()?r.L_():nS(e)&&e.Ra.set("Unknown"))}function nE(e,t){if(e.Va.Ue(t.targetId),t.resumeToken.approximateByteSize()>0||t.snapshotVersion.compareTo(ee.min())>0){let r=e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size;t=t.withExpectedCount(r)}nR(e).Y_(t)}function n_(e,t){e.Va.Ue(t),nR(e).Z_(t)}function nT(e){e.Va=new rt({getRemoteKeysForTarget:t=>e.remoteSyncer.getRemoteKeysForTarget(t),At:t=>e.Ia.get(t)||null,ht:()=>e.datastore.serializer.databaseId}),nR(e).start(),e.Ra.ua()}function nC(e){return nS(e)&&!nR(e).x_()&&e.Ia.size>0}function nS(e){return 0===e.Ea.size}async function nI(e){e.Ra.set("Online")}async function nN(e){e.Ia.forEach((t,r)=>{nE(e,t)})}async function nA(e,t){e.Va=void 0,nC(e)?(e.Ra.ha(t),nT(e)):e.Ra.set("Unknown")}async function nb(e,t,r){if(e.Ra.set("Online"),t instanceof t7&&2===t.state&&t.cause)try{await async function(e,t){let r=t.cause;for(let n of t.targetIds)e.Ia.has(n)&&(await e.remoteSyncer.rejectListen(n,r),e.Ia.delete(n),e.Va.removeTarget(n))}(e,t)}catch(r){E(nf,"Failed to remove targets %s: %s ",t.targetIds.join(","),r),await nk(e,r)}else if(t instanceof t8?e.Va.Ze(t):t instanceof t9?e.Va.st(t):e.Va.tt(t),!r.isEqual(ee.min()))try{let t=await r0(e.localStore);r.compareTo(t)>=0&&await function(e,t){let r=e.Va.Tt(t);return r.targetChanges.forEach((r,n)=>{if(r.resumeToken.approximateByteSize()>0){let s=e.Ia.get(n);s&&e.Ia.set(n,s.withResumeToken(r.resumeToken,t))}}),r.targetMismatches.forEach((t,r)=>{let n=e.Ia.get(t);if(!n)return;e.Ia.set(t,n.withResumeToken(ev.EMPTY_BYTE_STRING,n.snapshotVersion)),n_(e,t);let s=new rw(n.target,t,r,n.sequenceNumber);nE(e,s)}),e.remoteSyncer.applyRemoteEvent(r)}(e,r)}catch(t){E(nf,"Failed to raise snapshot:",t),await nk(e,t)}}async function nk(e,t,r){if(!ea(t))throw t;e.Ea.add(1),await ny(e),e.Ra.set("Offline"),r||(r=()=>r0(e.localStore)),e.asyncQueue.enqueueRetryable(async()=>{E(nf,"Retrying IndexedDB access"),await r(),e.Ea.delete(1),await np(e)})}async function nD(e,t){e.asyncQueue.verifyOperationInProgress(),E(nf,"RemoteStore received new credentials");let r=nS(e);e.Ea.add(3),await ny(e),r&&e.Ra.set("Unknown"),await e.remoteSyncer.handleCredentialChange(t),e.Ea.delete(3),await np(e)}async function nx(e,t){t?(e.Ea.delete(2),await np(e)):t||(e.Ea.add(2),await ny(e),e.Ra.set("Unknown"))}function nR(e){var t,r,n;return e.ma||(t=e.datastore,r=e.asyncQueue,n={Xo:nI.bind(null,e),t_:nN.bind(null,e),r_:nA.bind(null,e),H_:nb.bind(null,e)},t.sa(),e.ma=new nh(r,t.connection,t.authCredentials,t.appCheckCredentials,t.serializer,n),e.da.push(async t=>{t?(e.ma.B_(),nC(e)?nT(e):e.Ra.set("Unknown")):(await e.ma.stop(),e.Va=void 0)})),e.ma}class nV{constructor(e,t,r,n,s){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=r,this.op=n,this.removalCallback=s,this.deferred=new k,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(e=>{})}get promise(){return this.deferred.promise}static createAndSchedule(e,t,r,n,s){let i=new nV(e,t,Date.now()+r,n,s);return i.start(r),i}start(e){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new b(A.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then(e=>this.deferred.resolve(e))):Promise.resolve())}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function nO(e,t){if(_("AsyncQueue",`${t}: ${e}`),ea(e))return new b(A.UNAVAILABLE,`${t}: ${e}`);throw e}class nL{static emptySet(e){return new nL(e.comparator)}constructor(e){this.comparator=e?(t,r)=>e(t,r)||H.comparator(t.key,r.key):(e,t)=>H.comparator(e.key,t.key),this.keyedMap=tS(),this.sortedSet=new ec(this.comparator)}has(e){return null!=this.keyedMap.get(e)}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){let t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(e){this.sortedSet.inorderTraversal((t,r)=>(e(t),!1))}add(e){let t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){let t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof nL)||this.size!==e.size)return!1;let t=this.sortedSet.getIterator(),r=e.sortedSet.getIterator();for(;t.hasNext();){let e=t.getNext().key,n=r.getNext().key;if(!e.isEqual(n))return!1}return!0}toString(){let e=[];return this.forEach(t=>{e.push(t.toString())}),0===e.length?"DocumentSet ()":"DocumentSet (\n  "+e.join("  \n")+"\n)"}copy(e,t){let r=new nL;return r.comparator=this.comparator,r.keyedMap=e,r.sortedSet=t,r}}class nM{constructor(){this.ga=new ec(H.comparator)}track(e){let t=e.doc.key,r=this.ga.get(t);r?0!==e.type&&3===r.type?this.ga=this.ga.insert(t,e):3===e.type&&1!==r.type?this.ga=this.ga.insert(t,{type:r.type,doc:e.doc}):2===e.type&&2===r.type?this.ga=this.ga.insert(t,{type:2,doc:e.doc}):2===e.type&&0===r.type?this.ga=this.ga.insert(t,{type:0,doc:e.doc}):1===e.type&&0===r.type?this.ga=this.ga.remove(t):1===e.type&&2===r.type?this.ga=this.ga.insert(t,{type:1,doc:r.doc}):0===e.type&&1===r.type?this.ga=this.ga.insert(t,{type:2,doc:e.doc}):S(63341,{Rt:e,pa:r}):this.ga=this.ga.insert(t,e)}ya(){let e=[];return this.ga.inorderTraversal((t,r)=>{e.push(r)}),e}}class nP{constructor(e,t,r,n,s,i,a,o,l){this.query=e,this.docs=t,this.oldDocs=r,this.docChanges=n,this.mutatedKeys=s,this.fromCache=i,this.syncStateChanged=a,this.excludesMetadataChanges=o,this.hasCachedResults=l}static fromInitialDocuments(e,t,r,n,s){let i=[];return t.forEach(e=>{i.push({type:0,doc:e})}),new nP(e,t,nL.emptySet(t),i,r,n,!0,!1,s)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&tp(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;let t=this.docChanges,r=e.docChanges;if(t.length!==r.length)return!1;for(let e=0;e<t.length;e++)if(t[e].type!==r[e].type||!t[e].doc.isEqual(r[e].doc))return!1;return!0}}class nF{constructor(){this.wa=void 0,this.Sa=[]}ba(){return this.Sa.some(e=>e.Da())}}class nU{constructor(){this.queries=nq(),this.onlineState="Unknown",this.Ca=new Set}terminate(){!function(e,t){let r=e.queries;e.queries=nq(),r.forEach((e,r)=>{for(let e of r.Sa)e.onError(t)})}(this,new b(A.ABORTED,"Firestore shutting down"))}}function nq(){return new t_(e=>ty(e),tp)}async function nz(e,t){let r=3,n=t.query,s=e.queries.get(n);s?!s.ba()&&t.Da()&&(r=2):(s=new nF,r=+!t.Da());try{switch(r){case 0:s.wa=await e.onListen(n,!0);break;case 1:s.wa=await e.onListen(n,!1);break;case 2:await e.onFirstRemoteStoreListen(n)}}catch(r){let e=nO(r,`Initialization of query '${tv(t.query)}' failed`);return void t.onError(e)}e.queries.set(n,s),s.Sa.push(t),t.va(e.onlineState),s.wa&&t.Fa(s.wa)&&nK(e)}async function n$(e,t){let r=t.query,n=3,s=e.queries.get(r);if(s){let e=s.Sa.indexOf(t);e>=0&&(s.Sa.splice(e,1),0===s.Sa.length?n=+!t.Da():!s.ba()&&t.Da()&&(n=2))}switch(n){case 0:return e.queries.delete(r),e.onUnlisten(r,!0);case 1:return e.queries.delete(r),e.onUnlisten(r,!1);case 2:return e.onLastRemoteStoreUnlisten(r);default:return}}function nB(e,t){let r=!1;for(let n of t){let t=n.query,s=e.queries.get(t);if(s){for(let e of s.Sa)e.Fa(n)&&(r=!0);s.wa=n}}r&&nK(e)}function nj(e,t,r){let n=e.queries.get(t);if(n)for(let e of n.Sa)e.onError(r);e.queries.delete(t)}function nK(e){e.Ca.forEach(e=>{e.next()})}(a=i||(i={})).Ma="default",a.Cache="cache";class nG{constructor(e,t,r){this.query=e,this.xa=t,this.Oa=!1,this.Na=null,this.onlineState="Unknown",this.options=r||{}}Fa(e){if(!this.options.includeMetadataChanges){let t=[];for(let r of e.docChanges)3!==r.type&&t.push(r);e=new nP(e.query,e.docs,e.oldDocs,t,e.mutatedKeys,e.fromCache,e.syncStateChanged,!0,e.hasCachedResults)}let t=!1;return this.Oa?this.Ba(e)&&(this.xa.next(e),t=!0):this.La(e,this.onlineState)&&(this.ka(e),t=!0),this.Na=e,t}onError(e){this.xa.error(e)}va(e){this.onlineState=e;let t=!1;return this.Na&&!this.Oa&&this.La(this.Na,e)&&(this.ka(this.Na),t=!0),t}La(e,t){return!(e.fromCache&&this.Da())||(!this.options.qa||"Offline"===t)&&(!e.docs.isEmpty()||e.hasCachedResults||"Offline"===t)}Ba(e){if(e.docChanges.length>0)return!0;let t=this.Na&&this.Na.hasPendingWrites!==e.hasPendingWrites;return!(!e.syncStateChanged&&!t)&&!0===this.options.includeMetadataChanges}ka(e){e=nP.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.Oa=!0,this.xa.next(e)}Da(){return this.options.source!==i.Cache}}class nQ{constructor(e){this.key=e}}class nH{constructor(e){this.key=e}}class nW{constructor(e,t){this.query=e,this.Ya=t,this.Za=null,this.hasCachedResults=!1,this.current=!1,this.Xa=tA(),this.mutatedKeys=tA(),this.eu=tE(e),this.tu=new nL(this.eu)}get nu(){return this.Ya}ru(e,t){let r=t?t.iu:new nM,n=t?t.tu:this.tu,s=t?t.mutatedKeys:this.mutatedKeys,i=n,a=!1,o="F"===this.query.limitType&&n.size===this.query.limit?n.last():null,l="L"===this.query.limitType&&n.size===this.query.limit?n.first():null;if(e.inorderTraversal((e,t)=>{let u=n.get(e),h=tw(this.query,t)?t:null,c=!!u&&this.mutatedKeys.has(u.key),d=!!h&&(h.hasLocalMutations||this.mutatedKeys.has(h.key)&&h.hasCommittedMutations),m=!1;u&&h?u.data.isEqual(h.data)?c!==d&&(r.track({type:3,doc:h}),m=!0):this.su(u,h)||(r.track({type:2,doc:h}),m=!0,(o&&this.eu(h,o)>0||l&&0>this.eu(h,l))&&(a=!0)):!u&&h?(r.track({type:0,doc:h}),m=!0):u&&!h&&(r.track({type:1,doc:u}),m=!0,(o||l)&&(a=!0)),m&&(h?(i=i.add(h),s=d?s.add(e):s.delete(e)):(i=i.delete(e),s=s.delete(e)))}),null!==this.query.limit)for(;i.size>this.query.limit;){let e="F"===this.query.limitType?i.last():i.first();i=i.delete(e.key),s=s.delete(e.key),r.track({type:1,doc:e})}return{tu:i,iu:r,Cs:a,mutatedKeys:s}}su(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,r,n){let s=this.tu;this.tu=e.tu,this.mutatedKeys=e.mutatedKeys;let i=e.iu.ya();i.sort((e,t)=>(function(e,t){let r=e=>{switch(e){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return S(20277,{Rt:e})}};return r(e)-r(t)})(e.type,t.type)||this.eu(e.doc,t.doc)),this.ou(r),n=n??!1;let a=t&&!n?this._u():[],o=0===this.Xa.size&&this.current&&!n?1:0,l=o!==this.Za;return(this.Za=o,0!==i.length||l)?{snapshot:new nP(this.query,e.tu,s,i,e.mutatedKeys,0===o,l,!1,!!r&&r.resumeToken.approximateByteSize()>0),au:a}:{au:a}}va(e){return this.current&&"Offline"===e?(this.current=!1,this.applyChanges({tu:this.tu,iu:new nM,mutatedKeys:this.mutatedKeys,Cs:!1},!1)):{au:[]}}uu(e){return!this.Ya.has(e)&&!!this.tu.has(e)&&!this.tu.get(e).hasLocalMutations}ou(e){e&&(e.addedDocuments.forEach(e=>this.Ya=this.Ya.add(e)),e.modifiedDocuments.forEach(e=>{}),e.removedDocuments.forEach(e=>this.Ya=this.Ya.delete(e)),this.current=e.current)}_u(){if(!this.current)return[];let e=this.Xa;this.Xa=tA(),this.tu.forEach(e=>{this.uu(e.key)&&(this.Xa=this.Xa.add(e.key))});let t=[];return e.forEach(e=>{this.Xa.has(e)||t.push(new nH(e))}),this.Xa.forEach(r=>{e.has(r)||t.push(new nQ(r))}),t}cu(e){this.Ya=e.Qs,this.Xa=tA();let t=this.ru(e.documents);return this.applyChanges(t,!0)}lu(){return nP.fromInitialDocuments(this.query,this.tu,this.mutatedKeys,0===this.Za,this.hasCachedResults)}}let nY="SyncEngine";class nX{constructor(e,t,r){this.query=e,this.targetId=t,this.view=r}}class nJ{constructor(e){this.key=e,this.hu=!1}}class nZ{constructor(e,t,r,n,s,i){this.localStore=e,this.remoteStore=t,this.eventManager=r,this.sharedClientState=n,this.currentUser=s,this.maxConcurrentLimboResolutions=i,this.Pu={},this.Tu=new t_(e=>ty(e),tp),this.Iu=new Map,this.Eu=new Set,this.du=new ec(H.comparator),this.Au=new Map,this.Ru=new rF,this.Vu={},this.mu=new Map,this.fu=rN.cr(),this.onlineState="Unknown",this.gu=void 0}get isPrimaryClient(){return!0===this.gu}}async function n0(e,t,r=!0){let n,s=sa(e),i=s.Tu.get(t);return i?(s.sharedClientState.addLocalQueryTarget(i.targetId),n=i.view.lu()):n=await n2(s,t,r,!0),n}async function n1(e,t){let r=sa(e);await n2(r,t,!0,!1)}async function n2(e,t,r,n){var s,i;let a,o=await (s=e.localStore,i=tf(t),s.persistence.runTransaction("Allocate target","readwrite",e=>{let t;return s.Pi.getTargetData(e,i).next(r=>r?(t=r,ei.resolve(t)):s.Pi.allocateTargetId(e).next(r=>(t=new rw(i,r,"TargetPurposeListen",e.currentSequenceNumber),s.Pi.addTargetData(e,t).next(()=>t))))}).then(e=>{let t=s.Ms.get(e.targetId);return(null===t||e.snapshotVersion.compareTo(t.snapshotVersion)>0)&&(s.Ms=s.Ms.insert(e.targetId,e),s.xs.set(i,e.targetId)),e})),l=o.targetId,u=e.sharedClientState.addLocalQueryTarget(l,r);return n&&(a=await n3(e,t,l,"current"===u,o.resumeToken)),e.isPrimaryClient&&r&&nv(e.remoteStore,o),a}async function n3(e,t,r,n,s){e.pu=(t,r,n)=>(async function(e,t,r,n){let s=t.view.ru(r);s.Cs&&(s=await r2(e.localStore,t.query,!1).then(({documents:e})=>t.view.ru(e,s)));let i=n&&n.targetChanges.get(t.targetId),a=n&&null!=n.targetMismatches.get(t.targetId),o=t.view.applyChanges(s,e.isPrimaryClient,i,a);return st(e,t.targetId,o.au),o.snapshot})(e,t,r,n);let i=await r2(e.localStore,t,!0),a=new nW(t,i.Qs),o=a.ru(i.documents),l=t5.createSynthesizedTargetChangeForCurrentChange(r,n&&"Offline"!==e.onlineState,s),u=a.applyChanges(o,e.isPrimaryClient,l);st(e,r,u.au);let h=new nX(t,r,a);return e.Tu.set(t,h),e.Iu.has(r)?e.Iu.get(r).push(t):e.Iu.set(r,[t]),u.snapshot}async function n4(e,t,r){let n=e.Tu.get(t),s=e.Iu.get(n.targetId);if(s.length>1)return e.Iu.set(n.targetId,s.filter(e=>!tp(e,t))),void e.Tu.delete(t);e.isPrimaryClient?(e.sharedClientState.removeLocalQueryTarget(n.targetId),e.sharedClientState.isActiveQueryTarget(n.targetId)||await r1(e.localStore,n.targetId,!1).then(()=>{e.sharedClientState.clearQueryState(n.targetId),r&&nw(e.remoteStore,n.targetId),n7(e,n.targetId)}).catch(es)):(n7(e,n.targetId),await r1(e.localStore,n.targetId,!0))}async function n6(e,t){let r=e.Tu.get(t),n=e.Iu.get(r.targetId);e.isPrimaryClient&&1===n.length&&(e.sharedClientState.removeLocalQueryTarget(r.targetId),nw(e.remoteStore,r.targetId))}async function n5(e,t){try{let r=await function(e,t){let r=t.snapshotVersion,n=e.Ms;return e.persistence.runTransaction("Apply remote event","readwrite-primary",s=>{var i,a,o;let l,u,h=e.Ns.newChangeBuffer({trackRemovals:!0});n=e.Ms;let c=[];t.targetChanges.forEach((i,a)=>{var o;let l=n.get(a);if(!l)return;c.push(e.Pi.removeMatchingKeys(s,i.removedDocuments,a).next(()=>e.Pi.addMatchingKeys(s,i.addedDocuments,a)));let u=l.withSequenceNumber(s.currentSequenceNumber);null!==t.targetMismatches.get(a)?u=u.withResumeToken(ev.EMPTY_BYTE_STRING,ee.min()).withLastLimboFreeSnapshotVersion(ee.min()):i.resumeToken.approximateByteSize()>0&&(u=u.withResumeToken(i.resumeToken,r)),n=n.insert(a,u),o=u,(0===l.resumeToken.approximateByteSize()||o.snapshotVersion.toMicroseconds()-l.snapshotVersion.toMicroseconds()>=3e8||i.addedDocuments.size+i.modifiedDocuments.size+i.removedDocuments.size>0)&&c.push(e.Pi.updateTargetData(s,u))});let d=tT,m=tA();if(t.documentUpdates.forEach(r=>{t.resolvedLimboDocuments.has(r)&&c.push(e.persistence.referenceDelegate.updateLimboDocument(s,r))}),c.push((i=s,a=h,o=t.documentUpdates,l=tA(),u=tA(),o.forEach(e=>l=l.add(e)),a.getEntries(i,l).next(e=>{let t=tT;return o.forEach((r,n)=>{let s=e.get(r);n.isFoundDocument()!==s.isFoundDocument()&&(u=u.add(r)),n.isNoDocument()&&n.version.isEqual(ee.min())?(a.removeEntry(r,n.readTime),t=t.insert(r,n)):!s.isValidDocument()||n.version.compareTo(s.version)>0||0===n.version.compareTo(s.version)&&s.hasPendingWrites?(a.addEntry(n),t=t.insert(r,n)):E(rX,"Ignoring outdated watch update for ",r,". Current version:",s.version," Watch version:",n.version)}),{ks:t,qs:u}})).next(e=>{d=e.ks,m=e.qs})),!r.isEqual(ee.min())){let t=e.Pi.getLastRemoteSnapshotVersion(s).next(t=>e.Pi.setTargetsMetadata(s,s.currentSequenceNumber,r));c.push(t)}return ei.waitFor(c).next(()=>h.apply(s)).next(()=>e.localDocuments.getLocalViewOfDocuments(s,d,m)).next(()=>d)}).then(t=>(e.Ms=n,t))}(e.localStore,t);t.targetChanges.forEach((t,r)=>{let n=e.Au.get(r);n&&(N(t.addedDocuments.size+t.modifiedDocuments.size+t.removedDocuments.size<=1,22616),t.addedDocuments.size>0?n.hu=!0:t.modifiedDocuments.size>0?N(n.hu,14607):t.removedDocuments.size>0&&(N(n.hu,42227),n.hu=!1))}),await sn(e,r,t)}catch(e){await es(e)}}function n8(e,t,r){var n;if(e.isPrimaryClient&&0===r||!e.isPrimaryClient&&1===r){let r,s=[];e.Tu.forEach((e,r)=>{let n=r.view.va(t);n.snapshot&&s.push(n.snapshot)}),(n=e.eventManager).onlineState=t,r=!1,n.queries.forEach((e,n)=>{for(let e of n.Sa)e.va(t)&&(r=!0)}),r&&nK(n),s.length&&e.Pu.H_(s),e.onlineState=t,e.isPrimaryClient&&e.sharedClientState.setOnlineState(t)}}async function n9(e,t,r){e.sharedClientState.updateQueryState(t,"rejected",r);let n=e.Au.get(t),s=n&&n.key;if(s){let r=new ec(H.comparator);r=r.insert(s,eJ.newNoDocument(s,ee.min()));let n=tA().add(s),i=new t6(ee.min(),new Map,new ec(U),r,n);await n5(e,i),e.du=e.du.remove(s),e.Au.delete(t),sr(e)}else await r1(e.localStore,t,!1).then(()=>n7(e,t,r)).catch(es)}function n7(e,t,r=null){for(let n of(e.sharedClientState.removeLocalQueryTarget(t),e.Iu.get(t)))e.Tu.delete(n),r&&e.Pu.yu(n,r);e.Iu.delete(t),e.isPrimaryClient&&e.Ru.jr(t).forEach(t=>{e.Ru.containsKey(t)||se(e,t)})}function se(e,t){e.Eu.delete(t.path.canonicalString());let r=e.du.get(t);null!==r&&(nw(e.remoteStore,r),e.du=e.du.remove(t),e.Au.delete(r),sr(e))}function st(e,t,r){for(let n of r)n instanceof nQ?(e.Ru.addReference(n.key,t),function(e,t){let r=t.key,n=r.path.canonicalString();e.du.get(r)||e.Eu.has(n)||(E(nY,"New document in limbo: "+r),e.Eu.add(n),sr(e))}(e,n)):n instanceof nH?(E(nY,"Document no longer in limbo: "+n.key),e.Ru.removeReference(n.key,t),e.Ru.containsKey(n.key)||se(e,n.key)):S(19791,{wu:n})}function sr(e){for(;e.Eu.size>0&&e.du.size<e.maxConcurrentLimboResolutions;){let t=e.Eu.values().next().value;e.Eu.delete(t);let r=new H(K.fromString(t)),n=e.fu.next();e.Au.set(n,new nJ(r)),e.du=e.du.insert(r,n),nv(e.remoteStore,new rw(tf(new tc(r.path)),n,"TargetPurposeLimboResolution",eo.ce))}}async function sn(e,t,r){let n=[],s=[],i=[];e.Tu.isEmpty()||(e.Tu.forEach((a,o)=>{i.push(e.pu(o,t,r).then(t=>{if((t||r)&&e.isPrimaryClient){let n=t?!t.fromCache:r?.targetChanges.get(o.targetId)?.current;e.sharedClientState.updateQueryState(o.targetId,n?"current":"not-current")}if(t){n.push(t);let e=rH.As(o.targetId,t);s.push(e)}}))}),await Promise.all(i),e.Pu.H_(n),await async function(e,t){try{await e.persistence.runTransaction("notifyLocalViewChanges","readwrite",r=>ei.forEach(t,t=>ei.forEach(t.Es,n=>e.persistence.referenceDelegate.addReference(r,t.targetId,n)).next(()=>ei.forEach(t.ds,n=>e.persistence.referenceDelegate.removeReference(r,t.targetId,n)))))}catch(e){if(!ea(e))throw e;E(rX,"Failed to update sequence numbers: "+e)}for(let r of t){let t=r.targetId;if(!r.fromCache){let r=e.Ms.get(t),n=r.snapshotVersion,s=r.withLastLimboFreeSnapshotVersion(n);e.Ms=e.Ms.insert(t,s)}}}(e.localStore,s))}async function ss(e,t){if(!e.currentUser.isEqual(t)){E(nY,"User change. New user:",t.toKey());let r=await rZ(e.localStore,t);e.currentUser=t,e.mu.forEach(e=>{e.forEach(e=>{e.reject(new b(A.CANCELLED,"'waitForPendingWrites' promise is rejected due to a user change."))})}),e.mu.clear(),e.sharedClientState.handleUserChange(t,r.removedBatchIds,r.addedBatchIds),await sn(e,r.Ls)}}function si(e,t){let r=e.Au.get(t);if(r&&r.hu)return tA().add(r.key);{let r=tA(),n=e.Iu.get(t);if(!n)return r;for(let t of n){let n=e.Tu.get(t);r=r.unionWith(n.view.nu)}return r}}function sa(e){return e.remoteStore.remoteSyncer.applyRemoteEvent=n5.bind(null,e),e.remoteStore.remoteSyncer.getRemoteKeysForTarget=si.bind(null,e),e.remoteStore.remoteSyncer.rejectListen=n9.bind(null,e),e.Pu.H_=nB.bind(null,e.eventManager),e.Pu.yu=nj.bind(null,e.eventManager),e}class so{constructor(){this.kind="memory",this.synchronizeTabs=!1}async initialize(e){this.serializer=new ro(e.databaseInfo.databaseId,!0),this.sharedClientState=this.Du(e),this.persistence=this.Cu(e),await this.persistence.start(),this.localStore=this.vu(e),this.gcScheduler=this.Fu(e,this.localStore),this.indexBackfillerScheduler=this.Mu(e,this.localStore)}Fu(e,t){return null}Mu(e,t){return null}vu(e){var t,r;return t=this.persistence,r=new rY,new rJ(t,r,e.initialUser,this.serializer)}Cu(e){return new rj(rG.mi,this.serializer)}Du(e){return new r4}async terminate(){this.gcScheduler?.stop(),this.indexBackfillerScheduler?.stop(),this.sharedClientState.shutdown(),await this.persistence.shutdown()}}so.provider={build:()=>new so};class sl extends so{constructor(e){super(),this.cacheSizeBytes=e}Fu(e,t){return N(this.persistence.referenceDelegate instanceof rQ,46915),new rD(this.persistence.referenceDelegate.garbageCollector,e.asyncQueue,t)}Cu(e){let t=void 0!==this.cacheSizeBytes?rI.withCacheSize(this.cacheSizeBytes):rI.DEFAULT;return new rj(e=>rQ.mi(e,t),this.serializer)}}class su{async initialize(e,t){this.localStore||(this.localStore=e.localStore,this.sharedClientState=e.sharedClientState,this.datastore=this.createDatastore(t),this.remoteStore=this.createRemoteStore(t),this.eventManager=this.createEventManager(t),this.syncEngine=this.createSyncEngine(t,!e.synchronizeTabs),this.sharedClientState.onlineStateHandler=e=>n8(this.syncEngine,e,1),this.remoteStore.remoteSyncer.handleCredentialChange=ss.bind(null,this.syncEngine),await nx(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(e){return new nU}createDatastore(e){var t;let r=new ro(e.databaseInfo.databaseId,!0),n=new ni(e.databaseInfo);return t=e.authCredentials,new nd(t,e.appCheckCredentials,n,r)}createRemoteStore(e){var t,r;return t=this.localStore,r=this.datastore,new ng(t,r,e.asyncQueue,e=>n8(this.syncEngine,e,0),r8.v()?new r8:new r6)}createSyncEngine(e,t){return function(e,t,r,n,s,i,a){let o=new nZ(e,t,r,n,s,i);return a&&(o.gu=!0),o}(this.localStore,this.remoteStore,this.eventManager,this.sharedClientState,e.initialUser,e.maxConcurrentLimboResolutions,t)}async terminate(){await async function(e){E(nf,"RemoteStore shutting down."),e.Ea.add(5),await ny(e),e.Aa.shutdown(),e.Ra.set("Unknown")}(this.remoteStore),this.datastore?.terminate(),this.eventManager?.terminate()}}su.provider={build:()=>new su};class sh{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.Ou(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.Ou(this.observer.error,e):_("Uncaught Error in snapshot listener:",e.toString()))}Nu(){this.muted=!0}Ou(e,t){setTimeout(()=>{this.muted||e(t)},0)}}let sc="FirestoreClient";class sd{constructor(e,t,r,n,s){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=r,this.databaseInfo=n,this.user=p.UNAUTHENTICATED,this.clientId=F.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=s,this.authCredentials.start(r,async e=>{E(sc,"Received user=",e.uid),await this.authCredentialListener(e),this.user=e}),this.appCheckCredentials.start(r,e=>(E(sc,"Received new app check token=",e),this.appCheckCredentialListener(e,this.user)))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();let e=new k;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),e.resolve()}catch(r){let t=nO(r,"Failed to shutdown persistence");e.reject(t)}}),e.promise}}async function sm(e,t){e.asyncQueue.verifyOperationInProgress(),E(sc,"Initializing OfflineComponentProvider");let r=e.configuration;await t.initialize(r);let n=r.initialUser;e.setCredentialChangeListener(async e=>{n.isEqual(e)||(await rZ(t.localStore,e),n=e)}),t.persistence.setDatabaseDeletedListener(()=>e.terminate()),e._offlineComponents=t}async function sf(e,t){e.asyncQueue.verifyOperationInProgress();let r=await sg(e);E(sc,"Initializing OnlineComponentProvider"),await t.initialize(r,e.configuration),e.setCredentialChangeListener(e=>nD(t.remoteStore,e)),e.setAppCheckTokenChangeListener((e,r)=>nD(t.remoteStore,r)),e._onlineComponents=t}async function sg(e){if(!e._offlineComponents)if(e._uninitializedComponentsProvider){E(sc,"Using user provided OfflineComponentProvider");try{await sm(e,e._uninitializedComponentsProvider._offline)}catch(t){if(!("FirebaseError"===t.name?t.code===A.FAILED_PRECONDITION||t.code===A.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&t instanceof DOMException)||22===t.code||20===t.code||11===t.code))throw t;T("Error using user provided cache. Falling back to memory cache: "+t),await sm(e,new so)}}else E(sc,"Using default OfflineComponentProvider"),await sm(e,new sl(void 0));return e._offlineComponents}async function sp(e){return e._onlineComponents||(e._uninitializedComponentsProvider?(E(sc,"Using user provided OnlineComponentProvider"),await sf(e,e._uninitializedComponentsProvider._online)):(E(sc,"Using default OnlineComponentProvider"),await sf(e,new su))),e._onlineComponents}async function sy(e){let t=await sp(e),r=t.eventManager;return r.onListen=n0.bind(null,t.syncEngine),r.onUnlisten=n4.bind(null,t.syncEngine),r.onFirstRemoteStoreListen=n1.bind(null,t.syncEngine),r.onLastRemoteStoreUnlisten=n6.bind(null,t.syncEngine),r}function sv(e){let t={};return void 0!==e.timeoutSeconds&&(t.timeoutSeconds=e.timeoutSeconds),t}let sw=new Map,sE="firestore.googleapis.com";class s_{constructor(e){if(void 0===e.host){if(void 0!==e.ssl)throw new b(A.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=sE,this.ssl=!0}else this.host=e.host,this.ssl=e.ssl??!0;if(this.isUsingEmulator=void 0!==e.emulatorOptions,this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=0x2800000;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new b(A.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}(function(e,t,r,n){if(!0===t&&!0===n)throw new b(A.INVALID_ARGUMENT,`${e} and ${r} cannot be used together.`)})("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=sv(e.experimentalLongPollingOptions??{}),function(e){if(void 0!==e.timeoutSeconds){if(isNaN(e.timeoutSeconds))throw new b(A.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (must not be NaN)`);if(e.timeoutSeconds<5)throw new b(A.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (minimum allowed value is 5)`);if(e.timeoutSeconds>30)throw new b(A.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!e.useFetchStreams}isEqual(e){var t,r;return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&(t=this.experimentalLongPollingOptions,r=e.experimentalLongPollingOptions,t.timeoutSeconds===r.timeoutSeconds)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams}}class sT{constructor(e,t,r,n){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=r,this._app=n,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new s_({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(!this._app)throw new b(A.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new b(A.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new s_(e),this._emulatorOptions=e.emulatorOptions||{},void 0!==e.credentials&&(this._authCredentials=function(e){if(!e)return new x;switch(e.type){case"firstParty":return new L(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new b(A.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}}(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function(e){let t=sw.get(e);t&&(E("ComponentProvider","Removing Datastore"),sw.delete(e),t.terminate())}(this),Promise.resolve()}}class sC{constructor(e,t,r){this.converter=t,this._query=r,this.type="query",this.firestore=e}withConverter(e){return new sC(this.firestore,e,this._query)}}class sS{constructor(e,t,r){this.converter=t,this._key=r,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new sI(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new sS(this.firestore,e,this._key)}toJSON(){return{type:sS._jsonSchemaVersion,referencePath:this._key.toString()}}static fromJSON(e,t,r){if(J(t,sS._jsonSchema))return new sS(e,r||null,new H(K.fromString(t.referencePath)))}}sS._jsonSchemaVersion="firestore/documentReference/1.0",sS._jsonSchema={type:X("string",sS._jsonSchemaVersion),referencePath:X("string")};class sI extends sC{constructor(e,t,r){super(e,t,new tc(r)),this._path=r,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){let e=this._path.popLast();return e.isEmpty()?null:new sS(this.firestore,null,new H(e))}withConverter(e){return new sI(this.firestore,e,this._path)}}function sN(e,t,...r){if(e=(0,h.Ku)(e),1==arguments.length&&(t=F.newId()),function(e,t,r){if(!r)throw new b(A.INVALID_ARGUMENT,`Function doc() cannot be called with an empty ${t}.`)}(0,"path",t),e instanceof sT){let n=K.fromString(t,...r);return W(n),new sS(e,null,new H(n))}{if(!(e instanceof sS||e instanceof sI))throw new b(A.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let n=e._path.child(K.fromString(t,...r));return W(n),new sS(e.firestore,e instanceof sI?e.converter:null,new H(n))}}let sA="AsyncQueue";class sb{constructor(e=Promise.resolve()){this.Xu=[],this.ec=!1,this.tc=[],this.nc=null,this.rc=!1,this.sc=!1,this.oc=[],this.M_=new no(this,"async_queue_retry"),this._c=()=>{let e=na();e&&E(sA,"Visibility state changed to "+e.visibilityState),this.M_.w_()},this.ac=e;let t=na();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this._c)}get isShuttingDown(){return this.ec}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.uc(),this.cc(e)}enterRestrictedMode(e){if(!this.ec){this.ec=!0,this.sc=e||!1;let t=na();t&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this._c)}}enqueue(e){if(this.uc(),this.ec)return new Promise(()=>{});let t=new k;return this.cc(()=>this.ec&&this.sc?Promise.resolve():(e().then(t.resolve,t.reject),t.promise)).then(()=>t.promise)}enqueueRetryable(e){this.enqueueAndForget(()=>(this.Xu.push(e),this.lc()))}async lc(){if(0!==this.Xu.length){try{await this.Xu[0](),this.Xu.shift(),this.M_.reset()}catch(e){if(!ea(e))throw e;E(sA,"Operation failed with retryable error: "+e)}this.Xu.length>0&&this.M_.p_(()=>this.lc())}}cc(e){let t=this.ac.then(()=>(this.rc=!0,e().catch(e=>{throw this.nc=e,this.rc=!1,_("INTERNAL UNHANDLED ERROR: ",sk(e)),e}).then(e=>(this.rc=!1,e))));return this.ac=t,t}enqueueAfterDelay(e,t,r){this.uc(),this.oc.indexOf(e)>-1&&(t=0);let n=nV.createAndSchedule(this,e,t,r,e=>this.hc(e));return this.tc.push(n),n}uc(){this.nc&&S(47125,{Pc:sk(this.nc)})}verifyOperationInProgress(){}async Tc(){let e;do e=this.ac,await e;while(e!==this.ac)}Ic(e){for(let t of this.tc)if(t.timerId===e)return!0;return!1}Ec(e){return this.Tc().then(()=>{for(let t of(this.tc.sort((e,t)=>e.targetTimeMs-t.targetTimeMs),this.tc))if(t.skipDelay(),"all"!==e&&t.timerId===e)break;return this.Tc()})}dc(e){this.oc.push(e)}hc(e){let t=this.tc.indexOf(e);this.tc.splice(t,1)}}function sk(e){let t=e.message||"";return e.stack&&(t=e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack),t}class sD extends sT{constructor(e,t,r,n){super(e,t,r,n),this.type="firestore",this._queue=new sb,this._persistenceKey=n?.name||"[DEFAULT]"}async _terminate(){if(this._firestoreClient){let e=this._firestoreClient.terminate();this._queue=new sb(e),this._firestoreClient=void 0,await e}}}function sx(e,t){let r="object"==typeof e?e:(0,o.Sx)(),n=(0,o.j6)(r,"firestore").getImmediate({identifier:"string"==typeof e?e:t||ex});if(!n._initialized){let e=(0,h.yU)("firestore");e&&function(e,t,r,n={}){e=Y(e,sT);let s=(0,h.zJ)(t),i=e._getSettings(),a={...i,emulatorOptions:e._getEmulatorOptions()},o=`${t}:${r}`;s&&((0,h.gE)(`https://${o}`),(0,h.P1)("Firestore",!0)),i.host!==sE&&i.host!==o&&T("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");let l={...i,host:o,ssl:s,emulatorOptions:n};if(!(0,h.bD)(l,a)&&(e._setSettings(l),n.mockUserToken)){let t,r;if("string"==typeof n.mockUserToken)t=n.mockUserToken,r=p.MOCK_USER;else{t=(0,h.Fy)(n.mockUserToken,e._app?.options.projectId);let s=n.mockUserToken.sub||n.mockUserToken.user_id;if(!s)throw new b(A.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");r=new p(s)}e._authCredentials=new R(new D(t,r))}}(n,...e)}return n}class sR{constructor(e){this._byteString=e}static fromBase64String(e){try{return new sR(ev.fromBase64String(e))}catch(e){throw new b(A.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+e)}}static fromUint8Array(e){return new sR(ev.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}toJSON(){return{type:sR._jsonSchemaVersion,bytes:this.toBase64()}}static fromJSON(e){if(J(e,sR._jsonSchema))return sR.fromBase64String(e.bytes)}}sR._jsonSchemaVersion="firestore/bytes/1.0",sR._jsonSchema={type:X("string",sR._jsonSchemaVersion),bytes:X("string")};class sV{constructor(...e){for(let t=0;t<e.length;++t)if(0===e[t].length)throw new b(A.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new Q(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}class sO{constructor(e,t){if(!isFinite(e)||e<-90||e>90)throw new b(A.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||t>180)throw new b(A.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}_compareTo(e){return U(this._lat,e._lat)||U(this._long,e._long)}toJSON(){return{latitude:this._lat,longitude:this._long,type:sO._jsonSchemaVersion}}static fromJSON(e){if(J(e,sO._jsonSchema))return new sO(e.latitude,e.longitude)}}sO._jsonSchemaVersion="firestore/geoPoint/1.0",sO._jsonSchema={type:X("string",sO._jsonSchemaVersion),latitude:X("number"),longitude:X("number")};class sL{constructor(e){this._values=(e||[]).map(e=>e)}toArray(){return this._values.map(e=>e)}isEqual(e){return function(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;++r)if(e[r]!==t[r])return!1;return!0}(this._values,e._values)}toJSON(){return{type:sL._jsonSchemaVersion,vectorValues:this._values}}static fromJSON(e){if(J(e,sL._jsonSchema)){if(Array.isArray(e.vectorValues)&&e.vectorValues.every(e=>"number"==typeof e))return new sL(e.vectorValues);throw new b(A.INVALID_ARGUMENT,"Expected 'vectorValues' field to be a number array")}}}sL._jsonSchemaVersion="firestore/vectorValue/1.0",sL._jsonSchema={type:X("string",sL._jsonSchemaVersion),vectorValues:X("object")};let sM=RegExp("[~\\*/\\[\\]]");function sP(e,t,r,n,s){let i=n&&!n.isEmpty(),a=void 0!==s,o=`Function ${t}() called with invalid data`;r&&(o+=" (via `toFirestore()`)"),o+=". ";let l="";return(i||a)&&(l+=" (found",i&&(l+=` in field ${n}`),a&&(l+=` in document ${s}`),l+=")"),new b(A.INVALID_ARGUMENT,o+e+l)}class sF{constructor(e,t,r,n,s){this._firestore=e,this._userDataWriter=t,this._key=r,this._document=n,this._converter=s}get id(){return this._key.path.lastSegment()}get ref(){return new sS(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){if(this._document){if(this._converter){let e=new sU(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(e)}return this._userDataWriter.convertValue(this._document.data.value)}}get(e){if(this._document){let t=this._document.data.field(sq("DocumentSnapshot.get",e));if(null!==t)return this._userDataWriter.convertValue(t)}}}class sU extends sF{data(){return super.data()}}function sq(e,t){return"string"==typeof t?function(e,t,r){if(t.search(sM)>=0)throw sP(`Invalid field path (${t}). Paths must not contain '~', '*', '/', '[', or ']'`,e,!1,void 0,void 0);try{return new sV(...t.split("."))._internalPath}catch(r){throw sP(`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,e,!1,void 0,void 0)}}(e,t):t instanceof sV?t._internalPath:t._delegate._internalPath}class sz{convertValue(e,t="none"){switch(eM(e)){case 0:return null;case 1:return e.booleanValue;case 2:return e_(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(eT(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw S(62114,{value:e})}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,t="none"){let r={};return eh(e,(e,n)=>{r[e]=this.convertValue(n,t)}),r}convertVectorValue(e){return new sL(e.fields?.[eL].arrayValue?.values?.map(e=>e_(e.doubleValue)))}convertGeoPoint(e){return new sO(e_(e.latitude),e_(e.longitude))}convertArray(e,t){return(e.values||[]).map(e=>this.convertValue(e,t))}convertServerTimestamp(e,t){switch(t){case"previous":let r=eb(e);return null==r?null:this.convertValue(r,t);case"estimate":return this.convertTimestamp(ek(e));default:return null}}convertTimestamp(e){let t=eE(e);return new Z(t.seconds,t.nanos)}convertDocumentKey(e,t){let r=K.fromString(e);N(rv(r),9688,{name:e});let n=new eR(r.get(1),r.get(3)),s=new H(r.popFirst(5));return n.isEqual(t)||_(`Document ${s} contains a document reference within a different database (${n.projectId}/${n.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),s}}class s${constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}}class sB extends sF{constructor(e,t,r,n,s,i){super(e,t,r,n,i),this._firestore=e,this._firestoreImpl=e,this.metadata=s}exists(){return super.exists()}data(e={}){if(this._document){if(this._converter){let t=new sj(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null);return this._converter.fromFirestore(t,e)}return this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}}get(e,t={}){if(this._document){let r=this._document.data.field(sq("DocumentSnapshot.get",e));if(null!==r)return this._userDataWriter.convertValue(r,t.serverTimestamps)}}toJSON(){if(this.metadata.hasPendingWrites)throw new b(A.FAILED_PRECONDITION,"DocumentSnapshot.toJSON() attempted to serialize a document with pending writes. Await waitForPendingWrites() before invoking toJSON().");let e=this._document,t={};return t.type=sB._jsonSchemaVersion,t.bundle="",t.bundleSource="DocumentSnapshot",t.bundleName=this._key.toString(),e&&e.isValidDocument()&&e.isFoundDocument()&&(this._userDataWriter.convertObjectMap(e.data.value.mapValue.fields,"previous"),this._firestore,this.ref.path,t.bundle="NOT SUPPORTED"),t}}sB._jsonSchemaVersion="firestore/documentSnapshot/1.0",sB._jsonSchema={type:X("string",sB._jsonSchemaVersion),bundleSource:X("string","DocumentSnapshot"),bundleName:X("string"),bundle:X("string")};class sj extends sB{data(e={}){return super.data(e)}}class sK{constructor(e,t,r,n){this._firestore=e,this._userDataWriter=t,this._snapshot=n,this.metadata=new s$(n.hasPendingWrites,n.fromCache),this.query=r}get docs(){let e=[];return this.forEach(t=>e.push(t)),e}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(e,t){this._snapshot.docs.forEach(r=>{e.call(t,new sj(this._firestore,this._userDataWriter,r.key,r,new s$(this._snapshot.mutatedKeys.has(r.key),this._snapshot.fromCache),this.query.converter))})}docChanges(e={}){let t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new b(A.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=function(e,t){if(e._snapshot.oldDocs.isEmpty()){let t=0;return e._snapshot.docChanges.map(r=>{let n=new sj(e._firestore,e._userDataWriter,r.doc.key,r.doc,new s$(e._snapshot.mutatedKeys.has(r.doc.key),e._snapshot.fromCache),e.query.converter);return r.doc,{type:"added",doc:n,oldIndex:-1,newIndex:t++}})}{let r=e._snapshot.oldDocs;return e._snapshot.docChanges.filter(e=>t||3!==e.type).map(t=>{let n=new sj(e._firestore,e._userDataWriter,t.doc.key,t.doc,new s$(e._snapshot.mutatedKeys.has(t.doc.key),e._snapshot.fromCache),e.query.converter),s=-1,i=-1;return 0!==t.type&&(s=r.indexOf(t.doc.key),r=r.delete(t.doc.key)),1!==t.type&&(i=(r=r.add(t.doc)).indexOf(t.doc.key)),{type:function(e){switch(e){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return S(61501,{type:e})}}(t.type),doc:n,oldIndex:s,newIndex:i}})}}(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}toJSON(){if(this.metadata.hasPendingWrites)throw new b(A.FAILED_PRECONDITION,"QuerySnapshot.toJSON() attempted to serialize a document with pending writes. Await waitForPendingWrites() before invoking toJSON().");let e={};e.type=sK._jsonSchemaVersion,e.bundleSource="QuerySnapshot",e.bundleName=F.newId(),this._firestore._databaseId.database,this._firestore._databaseId.projectId;let t=[],r=[],n=[];return this.docs.forEach(e=>{null!==e._document&&(t.push(e._document),r.push(this._userDataWriter.convertObjectMap(e._document.data.value.mapValue.fields,"previous")),n.push(e.ref.path))}),this._firestore,this.query._query,e.bundleName,e.bundle="NOT SUPPORTED",e}}function sG(e){e=Y(e,sS);let t=Y(e.firestore,sD);return(function(e,t,r={}){let n=new k;return e.asyncQueue.enqueueAndForget(async()=>(function(e,t,r,n,s){let i=new sh({next:o=>{i.Nu(),t.enqueueAndForget(()=>n$(e,a));let l=o.docs.has(r);!l&&o.fromCache?s.reject(new b(A.UNAVAILABLE,"Failed to get document because the client is offline.")):l&&o.fromCache&&n&&"server"===n.source?s.reject(new b(A.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):s.resolve(o)},error:e=>s.reject(e)}),a=new nG(new tc(r.path),i,{includeMetadataChanges:!0,qa:!0});return nz(e,a)})(await sy(e),e.asyncQueue,t,r,n)),n.promise})(function(e){if(e._terminated)throw new b(A.FAILED_PRECONDITION,"The client has already been terminated.");return e._firestoreClient||function(e){var t,r;let n=e._freezeSettings(),s=(t=e._databaseId,r=e._app?.options.appId||"",new eD(t,r,e._persistenceKey,n.host,n.ssl,n.experimentalForceLongPolling,n.experimentalAutoDetectLongPolling,sv(n.experimentalLongPollingOptions),n.useFetchStreams,n.isUsingEmulator));e._componentsProvider||n.localCache?._offlineComponentProvider&&n.localCache?._onlineComponentProvider&&(e._componentsProvider={_offline:n.localCache._offlineComponentProvider,_online:n.localCache._onlineComponentProvider}),e._firestoreClient=new sd(e._authCredentials,e._appCheckCredentials,e._queue,s,e._componentsProvider&&function(e){let t=e?._online.build();return{_offline:e?._offline.build(t),_online:t}}(e._componentsProvider))}(e),e._firestoreClient}(t),e._key).then(r=>(function(e,t,r){let n=r.docs.get(t._key),s=new sQ(e);return new sB(e,s,t._key,n,new s$(r.hasPendingWrites,r.fromCache),t.converter)})(t,e,r))}sK._jsonSchemaVersion="firestore/querySnapshot/1.0",sK._jsonSchema={type:X("string",sK._jsonSchemaVersion),bundleSource:X("string","QuerySnapshot"),bundleName:X("string"),bundle:X("string")};class sQ extends sz{constructor(e){super(),this.firestore=e}convertBytes(e){return new sR(e)}convertReference(e){let t=this.convertDocumentKey(e,this.firestore._databaseId);return new sS(this.firestore,null,t)}}new WeakMap,function(e=!0){y=o.MF,(0,o.om)(new l.uA("firestore",(t,{instanceIdentifier:r,options:n})=>{let s=t.getProvider("app").getImmediate(),i=new sD(new V(t.getProvider("auth-internal")),new P(s,t.getProvider("app-check-internal")),function(e,t){if(!Object.prototype.hasOwnProperty.apply(e.options,["projectId"]))throw new b(A.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new eR(e.options.projectId,t)}(s,r),s);return n={useFetchStreams:e,...n},i._setSettings(n),i},"PUBLIC").setMultipleInstances(!0)),(0,o.KO)(f,g,void 0),(0,o.KO)(f,g,"esm2020")}()}}]);