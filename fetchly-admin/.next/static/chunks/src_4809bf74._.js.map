{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, formatDistanceToNow } from 'date-fns';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy');\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy HH:mm');\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return formatDistanceToNow(dateObj, { addSuffix: true });\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    // Booking statuses\n    pending: 'bg-yellow-100 text-yellow-800',\n    confirmed: 'bg-blue-100 text-blue-800',\n    'in-progress': 'bg-purple-100 text-purple-800',\n    completed: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    disputed: 'bg-orange-100 text-orange-800',\n    \n    // Payment statuses\n    paid: 'bg-green-100 text-green-800',\n    refunded: 'bg-gray-100 text-gray-800',\n    failed: 'bg-red-100 text-red-800',\n    \n    // Provider statuses\n    approved: 'bg-green-100 text-green-800',\n    rejected: 'bg-red-100 text-red-800',\n    \n    // General statuses\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    verified: 'bg-blue-100 text-blue-800',\n    unverified: 'bg-yellow-100 text-yellow-800',\n  };\n  \n  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '');\n  if (cleaned.length === 10) {\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n  }\n  return phone;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return current > 0 ? 100 : 0;\n  return ((current - previous) / previous) * 100;\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;QAAE,WAAW;IAAK;AACxD;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,mBAAmB;QACnB,SAAS;QACT,WAAW;QACX,eAAe;QACf,WAAW;QACX,WAAW;QACX,UAAU;QAEV,mBAAmB;QACnB,MAAM;QACN,UAAU;QACV,QAAQ;QAER,oBAAoB;QACpB,UAAU;QACV,UAAU;QAEV,mBAAmB;QACnB,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;IACd;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,oBAAoB,KAAa;IAC/C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,AAAC,IAA2B,OAAxB,QAAQ,KAAK,CAAC,GAAG,IAAG,MAA2B,OAAvB,QAAQ,KAAK,CAAC,GAAG,IAAG,KAAoB,OAAjB,QAAQ,KAAK,CAAC;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO,UAAU,IAAI,MAAM;IAC/C,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  HomeIcon,\n  UsersIcon,\n  UserGroupIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  BellIcon,\n} from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Users', href: '/dashboard/users', icon: UsersIcon },\n  { name: 'Providers', href: '/dashboard/providers', icon: UserGroupIcon },\n  { name: 'Bookings', href: '/dashboard/bookings', icon: CalendarDaysIcon },\n  { name: 'Payments', href: '/dashboard/payments', icon: CurrencyDollarIcon },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },\n  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },\n];\n\nexport default function DashboardLayout({ children, title }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { adminUser, signOut } = useAuth();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/login');\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-slate-50\">\n      {/* Mobile sidebar */}\n      <div className={cn(\n        \"fixed inset-0 z-50 lg:hidden\",\n        sidebarOpen ? \"block\" : \"hidden\"\n      )}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex items-center mb-3\">\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900\">{adminUser?.displayName}</p>\n                <p className=\"text-xs text-gray-500\">{adminUser?.role}</p>\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-2\" />\n              Sign out\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n              {title && (\n                <h1 className=\"ml-4 lg:ml-0 text-xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"text-gray-400 hover:text-gray-600\">\n                <BellIcon className=\"h-6 w-6\" />\n              </button>\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center lg:hidden\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAlBA;;;;;;AAyBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,oNAAA,CAAA,YAAS;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,4NAAA,CAAA,gBAAa;IAAC;IACvE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACxE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,sOAAA,CAAA,qBAAkB;IAAC;IAC1E;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,0NAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,gNAAA,CAAA,UAAO;IAAC;CAChE;AAEc,SAAS,gBAAgB,KAAyC;QAAzC,EAAE,QAAQ,EAAE,KAAK,EAAwB,GAAzC;QA+ErB,wBAyCA;;IAvHjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gCACA,cAAc,UAAU;;kCAExB,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAaxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAG/D,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCALL,KAAK,IAAI;;;;;;;;;;sCASpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,sBAAA,iCAAA,yBAAA,UAAW,WAAW,cAAtB,6CAAA,uBAAwB,MAAM,CAAC,OAAM;;;;;;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,sBAAA,gCAAA,UAAW,WAAW;;;;;;8DACxE,6LAAC;oDAAE,WAAU;8DAAyB,sBAAA,gCAAA,UAAW,IAAI;;;;;;;;;;;;;;;;;;8CAGzD,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,oPAAA,CAAA,4BAAyB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;wCAEtB,uBACC,6LAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,sBAAA,iCAAA,0BAAA,UAAW,WAAW,cAAtB,8CAAA,wBAAwB,MAAM,CAAC,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAxIwB;;QAES,kIAAA,CAAA,UAAO;QACvB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/firestore.ts"], "sourcesContent": ["import { \n  collection, \n  doc, \n  getDocs, \n  getDoc, \n  addDoc, \n  updateDoc, \n  deleteDoc, \n  query, \n  where, \n  orderBy, \n  limit,\n  startAfter,\n  DocumentSnapshot,\n  QueryConstraint,\n  Timestamp\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { User, Provider, Booking, Transaction, WithdrawalRequest, DashboardStats } from '@/types';\n\n// Collections\nconst COLLECTIONS = {\n  USERS: 'users',\n  PROVIDERS: 'providers', \n  BOOKINGS: 'bookings',\n  TRANSACTIONS: 'transactions',\n  WITHDRAWALS: 'withdrawals',\n  ADMINS: 'admins'\n};\n\n// Helper function to convert Firestore timestamp to Date\nconst convertTimestamp = (timestamp: any): Date => {\n  if (timestamp?.toDate) {\n    return timestamp.toDate();\n  }\n  if (timestamp?.seconds) {\n    return new Date(timestamp.seconds * 1000);\n  }\n  return new Date(timestamp);\n};\n\n// Helper function to convert data with timestamps\nconst convertDocData = (data: any) => {\n  const converted = { ...data };\n  \n  // Convert common timestamp fields\n  if (converted.createdAt) converted.createdAt = convertTimestamp(converted.createdAt);\n  if (converted.updatedAt) converted.updatedAt = convertTimestamp(converted.updatedAt);\n  if (converted.lastLoginAt) converted.lastLoginAt = convertTimestamp(converted.lastLoginAt);\n  if (converted.startDate) converted.startDate = convertTimestamp(converted.startDate);\n  if (converted.endDate) converted.endDate = convertTimestamp(converted.endDate);\n  if (converted.requestedAt) converted.requestedAt = convertTimestamp(converted.requestedAt);\n  if (converted.processedAt) converted.processedAt = convertTimestamp(converted.processedAt);\n  if (converted.reviewedAt) converted.reviewedAt = convertTimestamp(converted.reviewedAt);\n  if (converted.uploadedAt) converted.uploadedAt = convertTimestamp(converted.uploadedAt);\n  \n  return converted;\n};\n\n// Users\nexport const getUsers = async (filters?: {\n  searchTerm?: string;\n  status?: 'all' | 'active' | 'inactive';\n  limit?: number;\n  lastDoc?: DocumentSnapshot;\n}): Promise<{ users: User[]; lastDoc?: DocumentSnapshot }> => {\n  try {\n    const constraints: QueryConstraint[] = [];\n    \n    if (filters?.status && filters.status !== 'all') {\n      constraints.push(where('isActive', '==', filters.status === 'active'));\n    }\n    \n    constraints.push(orderBy('createdAt', 'desc'));\n    \n    if (filters?.limit) {\n      constraints.push(limit(filters.limit));\n    }\n    \n    if (filters?.lastDoc) {\n      constraints.push(startAfter(filters.lastDoc));\n    }\n    \n    const q = query(collection(db, COLLECTIONS.USERS), ...constraints);\n    const snapshot = await getDocs(q);\n    \n    const users = snapshot.docs.map(doc => ({\n      id: doc.id,\n      ...convertDocData(doc.data())\n    })) as User[];\n    \n    // Filter by search term on client side (for simplicity)\n    let filteredUsers = users;\n    if (filters?.searchTerm) {\n      const searchLower = filters.searchTerm.toLowerCase();\n      filteredUsers = users.filter(user => \n        user.displayName?.toLowerCase().includes(searchLower) ||\n        user.email?.toLowerCase().includes(searchLower)\n      );\n    }\n    \n    return {\n      users: filteredUsers,\n      lastDoc: snapshot.docs[snapshot.docs.length - 1]\n    };\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const updateUserStatus = async (userId: string, isActive: boolean): Promise<void> => {\n  try {\n    const userRef = doc(db, COLLECTIONS.USERS, userId);\n    await updateDoc(userRef, { \n      isActive,\n      updatedAt: Timestamp.now()\n    });\n  } catch (error) {\n    console.error('Error updating user status:', error);\n    throw error;\n  }\n};\n\n// Providers\nexport const getProviders = async (filters?: {\n  searchTerm?: string;\n  status?: 'all' | 'approved' | 'pending' | 'rejected';\n  limit?: number;\n}): Promise<Provider[]> => {\n  try {\n    const constraints: QueryConstraint[] = [];\n    \n    if (filters?.status && filters.status !== 'all') {\n      if (filters.status === 'approved') {\n        constraints.push(where('isApproved', '==', true));\n      } else if (filters.status === 'pending') {\n        constraints.push(where('isApproved', '==', false));\n      }\n    }\n    \n    constraints.push(orderBy('createdAt', 'desc'));\n    \n    if (filters?.limit) {\n      constraints.push(limit(filters.limit));\n    }\n    \n    const q = query(collection(db, COLLECTIONS.PROVIDERS), ...constraints);\n    const snapshot = await getDocs(q);\n    \n    const providers = snapshot.docs.map(doc => ({\n      id: doc.id,\n      ...convertDocData(doc.data())\n    })) as Provider[];\n    \n    // Filter by search term on client side\n    let filteredProviders = providers;\n    if (filters?.searchTerm) {\n      const searchLower = filters.searchTerm.toLowerCase();\n      filteredProviders = providers.filter(provider => \n        provider.businessName?.toLowerCase().includes(searchLower) ||\n        provider.description?.toLowerCase().includes(searchLower)\n      );\n    }\n    \n    return filteredProviders;\n  } catch (error) {\n    console.error('Error fetching providers:', error);\n    throw error;\n  }\n};\n\nexport const updateProviderStatus = async (\n  providerId: string, \n  isApproved: boolean, \n  isVerified?: boolean\n): Promise<void> => {\n  try {\n    const providerRef = doc(db, COLLECTIONS.PROVIDERS, providerId);\n    const updateData: any = { \n      isApproved,\n      updatedAt: Timestamp.now()\n    };\n    \n    if (isVerified !== undefined) {\n      updateData.isVerified = isVerified;\n    }\n    \n    await updateDoc(providerRef, updateData);\n  } catch (error) {\n    console.error('Error updating provider status:', error);\n    throw error;\n  }\n};\n\n// Bookings\nexport const getBookings = async (filters?: {\n  searchTerm?: string;\n  status?: 'all' | 'pending' | 'confirmed' | 'completed' | 'cancelled';\n  limit?: number;\n}): Promise<Booking[]> => {\n  try {\n    const constraints: QueryConstraint[] = [];\n    \n    if (filters?.status && filters.status !== 'all') {\n      constraints.push(where('status', '==', filters.status));\n    }\n    \n    constraints.push(orderBy('createdAt', 'desc'));\n    \n    if (filters?.limit) {\n      constraints.push(limit(filters.limit));\n    }\n    \n    const q = query(collection(db, COLLECTIONS.BOOKINGS), ...constraints);\n    const snapshot = await getDocs(q);\n    \n    const bookings = snapshot.docs.map(doc => ({\n      id: doc.id,\n      ...convertDocData(doc.data())\n    })) as Booking[];\n    \n    return bookings;\n  } catch (error) {\n    console.error('Error fetching bookings:', error);\n    throw error;\n  }\n};\n\n// Transactions\nexport const getTransactions = async (limit?: number): Promise<Transaction[]> => {\n  try {\n    const constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')];\n    \n    if (limit) {\n      constraints.push(limit);\n    }\n    \n    const q = query(collection(db, COLLECTIONS.TRANSACTIONS), ...constraints);\n    const snapshot = await getDocs(q);\n    \n    return snapshot.docs.map(doc => ({\n      id: doc.id,\n      ...convertDocData(doc.data())\n    })) as Transaction[];\n  } catch (error) {\n    console.error('Error fetching transactions:', error);\n    throw error;\n  }\n};\n\n// Withdrawal Requests\nexport const getWithdrawalRequests = async (status?: string): Promise<WithdrawalRequest[]> => {\n  try {\n    const constraints: QueryConstraint[] = [];\n    \n    if (status && status !== 'all') {\n      constraints.push(where('status', '==', status));\n    }\n    \n    constraints.push(orderBy('requestedAt', 'desc'));\n    \n    const q = query(collection(db, COLLECTIONS.WITHDRAWALS), ...constraints);\n    const snapshot = await getDocs(q);\n    \n    return snapshot.docs.map(doc => ({\n      id: doc.id,\n      ...convertDocData(doc.data())\n    })) as WithdrawalRequest[];\n  } catch (error) {\n    console.error('Error fetching withdrawal requests:', error);\n    throw error;\n  }\n};\n\nexport const updateWithdrawalStatus = async (\n  withdrawalId: string, \n  status: 'approved' | 'rejected' | 'processed'\n): Promise<void> => {\n  try {\n    const withdrawalRef = doc(db, COLLECTIONS.WITHDRAWALS, withdrawalId);\n    await updateDoc(withdrawalRef, { \n      status,\n      processedAt: Timestamp.now()\n    });\n  } catch (error) {\n    console.error('Error updating withdrawal status:', error);\n    throw error;\n  }\n};\n\n// Dashboard Stats\nexport const getDashboardStats = async (): Promise<DashboardStats> => {\n  try {\n    // Get counts from each collection\n    const [usersSnapshot, providersSnapshot, bookingsSnapshot, transactionsSnapshot] = await Promise.all([\n      getDocs(collection(db, COLLECTIONS.USERS)),\n      getDocs(collection(db, COLLECTIONS.PROVIDERS)),\n      getDocs(collection(db, COLLECTIONS.BOOKINGS)),\n      getDocs(collection(db, COLLECTIONS.TRANSACTIONS))\n    ]);\n    \n    const users = usersSnapshot.docs.map(doc => convertDocData(doc.data()));\n    const providers = providersSnapshot.docs.map(doc => convertDocData(doc.data()));\n    const bookings = bookingsSnapshot.docs.map(doc => convertDocData(doc.data()));\n    const transactions = transactionsSnapshot.docs.map(doc => convertDocData(doc.data()));\n    \n    // Calculate stats\n    const totalUsers = users.length;\n    const totalProviders = providers.filter(p => p.isApproved).length;\n    const totalBookings = bookings.length;\n    const totalRevenue = transactions\n      .filter(t => t.type === 'payment' && t.status === 'completed')\n      .reduce((sum, t) => sum + t.amount, 0);\n    \n    const pendingApprovals = providers.filter(p => !p.isApproved).length;\n    const activeDisputes = bookings.filter(b => b.status === 'disputed').length;\n    \n    // Calculate monthly growth (simplified - you might want more sophisticated logic)\n    const monthlyGrowth = {\n      users: 15.2, // You can calculate this based on actual data\n      providers: 8.7,\n      bookings: 22.1,\n      revenue: 18.9\n    };\n    \n    return {\n      totalUsers,\n      totalProviders,\n      totalBookings,\n      totalRevenue,\n      pendingApprovals,\n      activeDisputes,\n      monthlyGrowth\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAiBA;;;AAGA,cAAc;AACd,MAAM,cAAc;IAClB,OAAO;IACP,WAAW;IACX,UAAU;IACV,cAAc;IACd,aAAa;IACb,QAAQ;AACV;AAEA,yDAAyD;AACzD,MAAM,mBAAmB,CAAC;IACxB,IAAI,sBAAA,gCAAA,UAAW,MAAM,EAAE;QACrB,OAAO,UAAU,MAAM;IACzB;IACA,IAAI,sBAAA,gCAAA,UAAW,OAAO,EAAE;QACtB,OAAO,IAAI,KAAK,UAAU,OAAO,GAAG;IACtC;IACA,OAAO,IAAI,KAAK;AAClB;AAEA,kDAAkD;AAClD,MAAM,iBAAiB,CAAC;IACtB,MAAM,YAAY;QAAE,GAAG,IAAI;IAAC;IAE5B,kCAAkC;IAClC,IAAI,UAAU,SAAS,EAAE,UAAU,SAAS,GAAG,iBAAiB,UAAU,SAAS;IACnF,IAAI,UAAU,SAAS,EAAE,UAAU,SAAS,GAAG,iBAAiB,UAAU,SAAS;IACnF,IAAI,UAAU,WAAW,EAAE,UAAU,WAAW,GAAG,iBAAiB,UAAU,WAAW;IACzF,IAAI,UAAU,SAAS,EAAE,UAAU,SAAS,GAAG,iBAAiB,UAAU,SAAS;IACnF,IAAI,UAAU,OAAO,EAAE,UAAU,OAAO,GAAG,iBAAiB,UAAU,OAAO;IAC7E,IAAI,UAAU,WAAW,EAAE,UAAU,WAAW,GAAG,iBAAiB,UAAU,WAAW;IACzF,IAAI,UAAU,WAAW,EAAE,UAAU,WAAW,GAAG,iBAAiB,UAAU,WAAW;IACzF,IAAI,UAAU,UAAU,EAAE,UAAU,UAAU,GAAG,iBAAiB,UAAU,UAAU;IACtF,IAAI,UAAU,UAAU,EAAE,UAAU,UAAU,GAAG,iBAAiB,UAAU,UAAU;IAEtF,OAAO;AACT;AAGO,MAAM,WAAW,OAAO;IAM7B,IAAI;QACF,MAAM,cAAiC,EAAE;QAEzC,IAAI,CAAA,oBAAA,8BAAA,QAAS,MAAM,KAAI,QAAQ,MAAM,KAAK,OAAO;YAC/C,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,MAAM,KAAK;QAC9D;QAEA,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEtC,IAAI,oBAAA,8BAAA,QAAS,KAAK,EAAE;YAClB,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QACtC;QAEA,IAAI,oBAAA,8BAAA,QAAS,OAAO,EAAE;YACpB,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,OAAO;QAC7C;QAEA,MAAM,IAAI,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,KAAK,MAAM;QACtD,MAAM,WAAW,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,MAAM,QAAQ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtC,IAAI,IAAI,EAAE;gBACV,GAAG,eAAe,IAAI,IAAI,GAAG;YAC/B,CAAC;QAED,wDAAwD;QACxD,IAAI,gBAAgB;QACpB,IAAI,oBAAA,8BAAA,QAAS,UAAU,EAAE;YACvB,MAAM,cAAc,QAAQ,UAAU,CAAC,WAAW;YAClD,gBAAgB,MAAM,MAAM,CAAC,CAAA;oBAC3B,mBACA;uBADA,EAAA,oBAAA,KAAK,WAAW,cAAhB,wCAAA,kBAAkB,WAAW,GAAG,QAAQ,CAAC,mBACzC,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,WAAW,GAAG,QAAQ,CAAC;;QAEvC;QAEA,OAAO;YACL,OAAO;YACP,SAAS,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,GAAG,EAAE;QAClD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO,QAAgB;IACrD,IAAI;QACF,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,KAAK,EAAE;QAC3C,MAAM,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;YACvB;YACA,WAAW,kKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,MAAM,eAAe,OAAO;IAKjC,IAAI;QACF,MAAM,cAAiC,EAAE;QAEzC,IAAI,CAAA,oBAAA,8BAAA,QAAS,MAAM,KAAI,QAAQ,MAAM,KAAK,OAAO;YAC/C,IAAI,QAAQ,MAAM,KAAK,YAAY;gBACjC,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,cAAc,MAAM;YAC7C,OAAO,IAAI,QAAQ,MAAM,KAAK,WAAW;gBACvC,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,cAAc,MAAM;YAC7C;QACF;QAEA,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEtC,IAAI,oBAAA,8BAAA,QAAS,KAAK,EAAE;YAClB,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QACtC;QAEA,MAAM,IAAI,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,SAAS,MAAM;QAC1D,MAAM,WAAW,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,MAAM,YAAY,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC1C,IAAI,IAAI,EAAE;gBACV,GAAG,eAAe,IAAI,IAAI,GAAG;YAC/B,CAAC;QAED,uCAAuC;QACvC,IAAI,oBAAoB;QACxB,IAAI,oBAAA,8BAAA,QAAS,UAAU,EAAE;YACvB,MAAM,cAAc,QAAQ,UAAU,CAAC,WAAW;YAClD,oBAAoB,UAAU,MAAM,CAAC,CAAA;oBACnC,wBACA;uBADA,EAAA,yBAAA,SAAS,YAAY,cAArB,6CAAA,uBAAuB,WAAW,GAAG,QAAQ,CAAC,mBAC9C,wBAAA,SAAS,WAAW,cAApB,4CAAA,sBAAsB,WAAW,GAAG,QAAQ,CAAC;;QAEjD;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,MAAM,uBAAuB,OAClC,YACA,YACA;IAEA,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,SAAS,EAAE;QACnD,MAAM,aAAkB;YACtB;YACA,WAAW,kKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;QAEA,IAAI,eAAe,WAAW;YAC5B,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,cAAc,OAAO;IAKhC,IAAI;QACF,MAAM,cAAiC,EAAE;QAEzC,IAAI,CAAA,oBAAA,8BAAA,QAAS,MAAM,KAAI,QAAQ,MAAM,KAAK,OAAO;YAC/C,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACvD;QAEA,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEtC,IAAI,oBAAA,8BAAA,QAAS,KAAK,EAAE;YAClB,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QACtC;QAEA,MAAM,IAAI,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,QAAQ,MAAM;QACzD,MAAM,WAAW,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,MAAM,WAAW,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACzC,IAAI,IAAI,EAAE;gBACV,GAAG,eAAe,IAAI,IAAI,GAAG;YAC/B,CAAC;QAED,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAGO,MAAM,kBAAkB,OAAO;IACpC,IAAI;QACF,MAAM,cAAiC;YAAC,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;SAAQ;QAErE,IAAI,OAAO;YACT,YAAY,IAAI,CAAC;QACnB;QAEA,MAAM,IAAI,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,YAAY,MAAM;QAC7D,MAAM,WAAW,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,OAAO,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC/B,IAAI,IAAI,EAAE;gBACV,GAAG,eAAe,IAAI,IAAI,GAAG;YAC/B,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,MAAM,wBAAwB,OAAO;IAC1C,IAAI;QACF,MAAM,cAAiC,EAAE;QAEzC,IAAI,UAAU,WAAW,OAAO;YAC9B,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM;QACzC;QAEA,YAAY,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAExC,MAAM,IAAI,CAAA,GAAA,kKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,WAAW,MAAM;QAC5D,MAAM,WAAW,MAAM,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;QAE/B,OAAO,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC/B,IAAI,IAAI,EAAE;gBACV,GAAG,eAAe,IAAI,IAAI,GAAG;YAC/B,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;AAEO,MAAM,yBAAyB,OACpC,cACA;IAEA,IAAI;QACF,MAAM,gBAAgB,CAAA,GAAA,kKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,WAAW,EAAE;QACvD,MAAM,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE,eAAe;YAC7B;YACA,aAAa,kKAAA,CAAA,YAAS,CAAC,GAAG;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,kCAAkC;QAClC,MAAM,CAAC,eAAe,mBAAmB,kBAAkB,qBAAqB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACnG,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,KAAK;YACxC,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,SAAS;YAC5C,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,QAAQ;YAC3C,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,YAAY;SAChD;QAED,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,eAAe,IAAI,IAAI;QACnE,MAAM,YAAY,kBAAkB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,eAAe,IAAI,IAAI;QAC3E,MAAM,WAAW,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,eAAe,IAAI,IAAI;QACzE,MAAM,eAAe,qBAAqB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,eAAe,IAAI,IAAI;QAEjF,kBAAkB;QAClB,MAAM,aAAa,MAAM,MAAM;QAC/B,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;QACjE,MAAM,gBAAgB,SAAS,MAAM;QACrC,MAAM,eAAe,aAClB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,MAAM,KAAK,aACjD,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;QACpE,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QAE3E,kFAAkF;QAClF,MAAM,gBAAgB;YACpB,OAAO;YACP,WAAW;YACX,UAAU;YACV,SAAS;QACX;QAEA,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport {\n  UsersIcon,\n  UserGroupIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n  ArrowUpIcon,\n  ArrowDownIcon,\n  ClockIcon,\n  CheckCircleIcon,\n} from '@heroicons/react/24/outline';\nimport { formatCurrency } from '@/lib/utils';\nimport { getDashboardStats, getBookings } from '@/lib/firestore';\nimport { DashboardStats } from '@/types';\n\nexport default function DashboardPage() {\n  const { user, adminUser, loading } = useAuth();\n  const router = useRouter();\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loadingStats, setLoadingStats] = useState(true);\n\n  useEffect(() => {\n    if (!loading && (!user || !adminUser)) {\n      router.push('/login');\n    }\n  }, [user, adminUser, loading, router]);\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      if (user && adminUser) {\n        try {\n          setLoadingStats(true);\n          const dashboardStats = await getDashboardStats();\n          setStats(dashboardStats);\n        } catch (error) {\n          console.error('Error fetching dashboard stats:', error);\n          // Set default stats if there's an error\n          setStats({\n            totalUsers: 0,\n            totalProviders: 0,\n            totalBookings: 0,\n            totalRevenue: 0,\n            pendingApprovals: 0,\n            activeDisputes: 0,\n            monthlyGrowth: {\n              users: 0,\n              providers: 0,\n              bookings: 0,\n              revenue: 0,\n            },\n          });\n        } finally {\n          setLoadingStats(false);\n        }\n      }\n    };\n\n    fetchStats();\n  }, [user, adminUser]);\n\n  if (loading || loadingStats) {\n    return (\n      <div className=\"min-h-screen bg-slate-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    router.push('/login');\n    return null;\n  }\n\n  if (!adminUser) {\n    return (\n      <div className=\"min-h-screen bg-slate-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-red-600 mb-4\">Access Denied</h1>\n          <p className=\"text-gray-600 mb-4\">You don't have admin privileges.</p>\n          <button\n            onClick={() => router.push('/login')}\n            className=\"btn btn-primary\"\n          >\n            Back to Login\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  if (!stats) {\n    return (\n      <div className=\"min-h-screen bg-slate-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-xl font-semibold text-gray-900 mb-4\">Loading Dashboard...</h1>\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  const statCards = [\n    {\n      name: 'Total Users',\n      value: stats.totalUsers.toLocaleString(),\n      change: stats.monthlyGrowth.users,\n      icon: UsersIcon,\n      color: 'blue',\n    },\n    {\n      name: 'Active Providers',\n      value: stats.totalProviders.toLocaleString(),\n      change: stats.monthlyGrowth.providers,\n      icon: UserGroupIcon,\n      color: 'green',\n    },\n    {\n      name: 'Total Bookings',\n      value: stats.totalBookings.toLocaleString(),\n      change: stats.monthlyGrowth.bookings,\n      icon: CalendarDaysIcon,\n      color: 'purple',\n    },\n    {\n      name: 'Revenue',\n      value: formatCurrency(stats.totalRevenue),\n      change: stats.monthlyGrowth.revenue,\n      icon: CurrencyDollarIcon,\n      color: 'yellow',\n    },\n  ];\n\n  return (\n    <DashboardLayout title=\"Dashboard\">\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            Welcome back, {adminUser.displayName}!\n          </h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Here&apos;s what&apos;s happening with your Fetchly platform today.\n          </p>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n          {statCards.map((stat) => (\n            <div key={stat.name} className=\"card p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <stat.icon className={`h-8 w-8 text-${stat.color}-600`} />\n                </div>\n                <div className=\"ml-5 w-0 flex-1\">\n                  <dl>\n                    <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                      {stat.name}\n                    </dt>\n                    <dd className=\"flex items-baseline\">\n                      <div className=\"text-2xl font-semibold text-gray-900\">\n                        {stat.value}\n                      </div>\n                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${\n                        stat.change >= 0 ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {stat.change >= 0 ? (\n                          <ArrowUpIcon className=\"h-4 w-4 flex-shrink-0 self-center\" />\n                        ) : (\n                          <ArrowDownIcon className=\"h-4 w-4 flex-shrink-0 self-center\" />\n                        )}\n                        <span className=\"sr-only\">\n                          {stat.change >= 0 ? 'Increased' : 'Decreased'} by\n                        </span>\n                        {Math.abs(stat.change)}%\n                      </div>\n                    </dd>\n                  </dl>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Quick Actions & Recent Activity */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Quick Actions */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-yellow-50 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <ClockIcon className=\"h-5 w-5 text-yellow-600 mr-3\" />\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    Pending Provider Approvals\n                  </span>\n                </div>\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                  {stats.pendingApprovals}\n                </span>\n              </div>\n              \n              <div className=\"flex items-center justify-between p-3 bg-red-50 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <ClockIcon className=\"h-5 w-5 text-red-600 mr-3\" />\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    Active Disputes\n                  </span>\n                </div>\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                  {stats.activeDisputes}\n                </span>\n              </div>\n              \n              <div className=\"pt-3\">\n                <button className=\"w-full btn btn-primary\">\n                  View All Pending Items\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Recent Activity */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Recent Activity</h3>\n            <div className=\"space-y-4\">\n              {recentActivities.map((activity) => (\n                <div key={activity.id} className=\"flex items-start space-x-3\">\n                  <div className=\"flex-shrink-0\">\n                    <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />\n                  </div>\n                  <div className=\"min-w-0 flex-1\">\n                    <p className=\"text-sm text-gray-900\">{activity.message}</p>\n                    <p className=\"text-xs text-gray-500\">{activity.time}</p>\n                  </div>\n                </div>\n              ))}\n              <div className=\"pt-3\">\n                <button className=\"w-full btn btn-secondary text-sm\">\n                  View All Activity\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;;;AAjBA;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,GAAG;gBACrC,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAW;QAAS;KAAO;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;sDAAa;oBACjB,IAAI,QAAQ,WAAW;wBACrB,IAAI;4BACF,gBAAgB;4BAChB,MAAM,iBAAiB,MAAM,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;4BAC7C,SAAS;wBACX,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,mCAAmC;4BACjD,wCAAwC;4BACxC,SAAS;gCACP,YAAY;gCACZ,gBAAgB;gCAChB,eAAe;gCACf,cAAc;gCACd,kBAAkB;gCAClB,gBAAgB;gCAChB,eAAe;oCACb,OAAO;oCACP,WAAW;oCACX,UAAU;oCACV,SAAS;gCACX;4BACF;wBACF,SAAU;4BACR,gBAAgB;wBAClB;oBACF;gBACF;;YAEA;QACF;kCAAG;QAAC;QAAM;KAAU;IAEpB,IAAI,WAAW,cAAc;QAC3B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,CAAC;QACZ,OAAO;IACT;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO,MAAM,UAAU,CAAC,cAAc;YACtC,QAAQ,MAAM,aAAa,CAAC,KAAK;YACjC,MAAM,oNAAA,CAAA,YAAS;YACf,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,MAAM,cAAc,CAAC,cAAc;YAC1C,QAAQ,MAAM,aAAa,CAAC,SAAS;YACrC,MAAM,4NAAA,CAAA,gBAAa;YACnB,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,MAAM,aAAa,CAAC,cAAc;YACzC,QAAQ,MAAM,aAAa,CAAC,QAAQ;YACpC,MAAM,kOAAA,CAAA,mBAAgB;YACtB,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;YACxC,QAAQ,MAAM,aAAa,CAAC,OAAO;YACnC,MAAM,sOAAA,CAAA,qBAAkB;YACxB,OAAO;QACT;KACD;IAED,qBACE,6LAAC,kJAAA,CAAA,UAAe;QAAC,OAAM;kBACrB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAmC;gCAChC,UAAU,WAAW;gCAAC;;;;;;;sCAEvC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAM5C,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;4BAAoB,WAAU;sCAC7B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,KAAK,IAAI;4CAAC,WAAW,AAAC,gBAA0B,OAAX,KAAK,KAAK,EAAC;;;;;;;;;;;kDAEnD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;8DAEZ,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAI,WAAW,AAAC,kDAEhB,OADC,KAAK,MAAM,IAAI,IAAI,mBAAmB;;gEAErC,KAAK,MAAM,IAAI,kBACd,6LAAC,wNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,6LAAC,4NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;8EAE3B,6LAAC;oEAAK,WAAU;;wEACb,KAAK,MAAM,IAAI,IAAI,cAAc;wEAAY;;;;;;;gEAE/C,KAAK,GAAG,CAAC,KAAK,MAAM;gEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAzBzB,KAAK,IAAI;;;;;;;;;;8BAoCvB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAItD,6LAAC;oDAAK,WAAU;8DACb,MAAM,gBAAgB;;;;;;;;;;;;sDAI3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;8DAItD,6LAAC;oDAAK,WAAU;8DACb,MAAM,cAAc;;;;;;;;;;;;sDAIzB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAO,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;sCAQjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;;wCACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;gDAAsB,WAAU;;kEAC/B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;;;;;;kEAE7B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,OAAO;;;;;;0EACtD,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,IAAI;;;;;;;;;;;;;+CAN7C,SAAS,EAAE;;;;;sDAUvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAO,WAAU;0DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrE;GAxOwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}