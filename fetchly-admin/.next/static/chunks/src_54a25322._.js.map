{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, formatDistanceToNow } from 'date-fns';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy');\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy HH:mm');\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return formatDistanceToNow(dateObj, { addSuffix: true });\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    // Booking statuses\n    pending: 'bg-yellow-100 text-yellow-800',\n    confirmed: 'bg-blue-100 text-blue-800',\n    'in-progress': 'bg-purple-100 text-purple-800',\n    completed: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    disputed: 'bg-orange-100 text-orange-800',\n    \n    // Payment statuses\n    paid: 'bg-green-100 text-green-800',\n    refunded: 'bg-gray-100 text-gray-800',\n    failed: 'bg-red-100 text-red-800',\n    \n    // Provider statuses\n    approved: 'bg-green-100 text-green-800',\n    rejected: 'bg-red-100 text-red-800',\n    \n    // General statuses\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    verified: 'bg-blue-100 text-blue-800',\n    unverified: 'bg-yellow-100 text-yellow-800',\n  };\n  \n  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '');\n  if (cleaned.length === 10) {\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n  }\n  return phone;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return current > 0 ? 100 : 0;\n  return ((current - previous) / previous) * 100;\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;QAAE,WAAW;IAAK;AACxD;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,mBAAmB;QACnB,SAAS;QACT,WAAW;QACX,eAAe;QACf,WAAW;QACX,WAAW;QACX,UAAU;QAEV,mBAAmB;QACnB,MAAM;QACN,UAAU;QACV,QAAQ;QAER,oBAAoB;QACpB,UAAU;QACV,UAAU;QAEV,mBAAmB;QACnB,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;IACd;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,oBAAoB,KAAa;IAC/C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,AAAC,IAA2B,OAAxB,QAAQ,KAAK,CAAC,GAAG,IAAG,MAA2B,OAAvB,QAAQ,KAAK,CAAC,GAAG,IAAG,KAAoB,OAAjB,QAAQ,KAAK,CAAC;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO,UAAU,IAAI,MAAM;IAC/C,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  HomeIcon,\n  UsersIcon,\n  UserGroupIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  BellIcon,\n} from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Users', href: '/dashboard/users', icon: UsersIcon },\n  { name: 'Providers', href: '/dashboard/providers', icon: UserGroupIcon },\n  { name: 'Bookings', href: '/dashboard/bookings', icon: CalendarDaysIcon },\n  { name: 'Payments', href: '/dashboard/payments', icon: CurrencyDollarIcon },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },\n  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },\n];\n\nexport default function DashboardLayout({ children, title }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { adminUser, signOut } = useAuth();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/login');\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-slate-50\">\n      {/* Mobile sidebar */}\n      <div className={cn(\n        \"fixed inset-0 z-50 lg:hidden\",\n        sidebarOpen ? \"block\" : \"hidden\"\n      )}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex items-center mb-3\">\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900\">{adminUser?.displayName}</p>\n                <p className=\"text-xs text-gray-500\">{adminUser?.role}</p>\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-2\" />\n              Sign out\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n              {title && (\n                <h1 className=\"ml-4 lg:ml-0 text-xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"text-gray-400 hover:text-gray-600\">\n                <BellIcon className=\"h-6 w-6\" />\n              </button>\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center lg:hidden\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAlBA;;;;;;AAyBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,oNAAA,CAAA,YAAS;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,4NAAA,CAAA,gBAAa;IAAC;IACvE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACxE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,sOAAA,CAAA,qBAAkB;IAAC;IAC1E;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,0NAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,gNAAA,CAAA,UAAO;IAAC;CAChE;AAEc,SAAS,gBAAgB,KAAyC;QAAzC,EAAE,QAAQ,EAAE,KAAK,EAAwB,GAAzC;QA+ErB,wBAyCA;;IAvHjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gCACA,cAAc,UAAU;;kCAExB,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAaxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAG/D,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCALL,KAAK,IAAI;;;;;;;;;;sCASpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,sBAAA,iCAAA,yBAAA,UAAW,WAAW,cAAtB,6CAAA,uBAAwB,MAAM,CAAC,OAAM;;;;;;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,sBAAA,gCAAA,UAAW,WAAW;;;;;;8DACxE,6LAAC;oDAAE,WAAU;8DAAyB,sBAAA,gCAAA,UAAW,IAAI;;;;;;;;;;;;;;;;;;8CAGzD,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,oPAAA,CAAA,4BAAyB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;wCAEtB,uBACC,6LAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,sBAAA,iCAAA,0BAAA,UAAW,WAAW,cAAtB,8CAAA,wBAAwB,MAAM,CAAC,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAxIwB;;QAES,kIAAA,CAAA,UAAO;QACvB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/app/dashboard/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport { useEffect } from 'react';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport {\n  ChartBarIcon,\n  UsersIcon,\n  CurrencyDollarIcon,\n  CalendarDaysIcon,\n  ArrowUpIcon,\n  ArrowDownIcon,\n} from '@heroicons/react/24/outline';\nimport { formatCurrency, calculatePercentageChange } from '@/lib/utils';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';\n\n// Mock analytics data\nconst mockRevenueData = [\n  { month: 'Jan', revenue: 12000, bookings: 45 },\n  { month: 'Feb', revenue: 15000, bookings: 52 },\n  { month: 'Mar', revenue: 18000, bookings: 68 },\n  { month: 'Apr', revenue: 22000, bookings: 78 },\n  { month: 'May', revenue: 25000, bookings: 89 },\n  { month: 'Jun', revenue: 28000, bookings: 95 },\n];\n\nconst mockUserGrowthData = [\n  { month: 'Jan', users: 120, providers: 15 },\n  { month: 'Feb', users: 145, providers: 18 },\n  { month: 'Mar', users: 178, providers: 22 },\n  { month: 'Apr', users: 210, providers: 28 },\n  { month: 'May', users: 245, providers: 32 },\n  { month: 'Jun', users: 280, providers: 38 },\n];\n\nconst mockServiceDistribution = [\n  { name: 'Dog Walking', value: 35, color: '#3B82F6' },\n  { name: 'Pet Sitting', value: 25, color: '#10B981' },\n  { name: 'Grooming', value: 20, color: '#F59E0B' },\n  { name: 'Training', value: 12, color: '#EF4444' },\n  { name: 'Veterinary', value: 8, color: '#8B5CF6' },\n];\n\nconst mockTopProviders = [\n  { name: 'PetCare Plus', bookings: 45, revenue: 1125 },\n  { name: 'Happy Tails', bookings: 38, revenue: 950 },\n  { name: 'Furry Friends', bookings: 32, revenue: 800 },\n  { name: 'Pet Paradise', bookings: 28, revenue: 700 },\n  { name: 'Loving Paws', bookings: 25, revenue: 625 },\n];\n\nexport default function AnalyticsPage() {\n  const { user, adminUser, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading && (!user || !adminUser)) {\n      router.push('/login');\n    }\n  }, [user, adminUser, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-slate-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!user || !adminUser) {\n    return null;\n  }\n\n  const currentMonthRevenue = mockRevenueData[mockRevenueData.length - 1].revenue;\n  const previousMonthRevenue = mockRevenueData[mockRevenueData.length - 2].revenue;\n  const revenueGrowth = calculatePercentageChange(currentMonthRevenue, previousMonthRevenue);\n\n  const currentMonthBookings = mockRevenueData[mockRevenueData.length - 1].bookings;\n  const previousMonthBookings = mockRevenueData[mockRevenueData.length - 2].bookings;\n  const bookingsGrowth = calculatePercentageChange(currentMonthBookings, previousMonthBookings);\n\n  return (\n    <DashboardLayout title=\"Analytics\">\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Analytics Dashboard</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Track performance metrics and business insights\n          </p>\n        </div>\n\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8\">\n          <div className=\"card p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CurrencyDollarIcon className=\"h-8 w-8 text-green-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Monthly Revenue\n                  </dt>\n                  <dd className=\"flex items-baseline\">\n                    <div className=\"text-2xl font-semibold text-gray-900\">\n                      {formatCurrency(currentMonthRevenue)}\n                    </div>\n                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${\n                      revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {revenueGrowth >= 0 ? (\n                        <ArrowUpIcon className=\"h-4 w-4 flex-shrink-0 self-center\" />\n                      ) : (\n                        <ArrowDownIcon className=\"h-4 w-4 flex-shrink-0 self-center\" />\n                      )}\n                      {Math.abs(revenueGrowth).toFixed(1)}%\n                    </div>\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CalendarDaysIcon className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Monthly Bookings\n                  </dt>\n                  <dd className=\"flex items-baseline\">\n                    <div className=\"text-2xl font-semibold text-gray-900\">\n                      {currentMonthBookings}\n                    </div>\n                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${\n                      bookingsGrowth >= 0 ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {bookingsGrowth >= 0 ? (\n                        <ArrowUpIcon className=\"h-4 w-4 flex-shrink-0 self-center\" />\n                      ) : (\n                        <ArrowDownIcon className=\"h-4 w-4 flex-shrink-0 self-center\" />\n                      )}\n                      {Math.abs(bookingsGrowth).toFixed(1)}%\n                    </div>\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <UsersIcon className=\"h-8 w-8 text-purple-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Active Users\n                  </dt>\n                  <dd className=\"text-2xl font-semibold text-gray-900\">\n                    {mockUserGrowthData[mockUserGrowthData.length - 1].users}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"card p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <ChartBarIcon className=\"h-8 w-8 text-yellow-600\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    Active Providers\n                  </dt>\n                  <dd className=\"text-2xl font-semibold text-gray-900\">\n                    {mockUserGrowthData[mockUserGrowthData.length - 1].providers}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Charts Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n          {/* Revenue Chart */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Revenue Trend</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={mockRevenueData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />\n                <Line type=\"monotone\" dataKey=\"revenue\" stroke=\"#3B82F6\" strokeWidth={2} />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* User Growth Chart */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">User Growth</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={mockUserGrowthData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip />\n                <Bar dataKey=\"users\" fill=\"#10B981\" />\n                <Bar dataKey=\"providers\" fill=\"#F59E0B\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Service Distribution */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Service Distribution</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={mockServiceDistribution}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                  label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}\n                >\n                  {mockServiceDistribution.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Top Providers */}\n          <div className=\"card p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Top Providers</h3>\n            <div className=\"space-y-4\">\n              {mockTopProviders.map((provider, index) => (\n                <div key={provider.name} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                      <span className=\"text-sm font-medium text-blue-700\">{index + 1}</span>\n                    </div>\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">{provider.name}</p>\n                      <p className=\"text-sm text-gray-500\">{provider.bookings} bookings</p>\n                    </div>\n                  </div>\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {formatCurrency(provider.revenue)}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;AAiBA,sBAAsB;AACtB,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAO,SAAS;QAAO,UAAU;IAAG;IAC7C;QAAE,OAAO;QAAO,SAAS;QAAO,UAAU;IAAG;IAC7C;QAAE,OAAO;QAAO,SAAS;QAAO,UAAU;IAAG;IAC7C;QAAE,OAAO;QAAO,SAAS;QAAO,UAAU;IAAG;IAC7C;QAAE,OAAO;QAAO,SAAS;QAAO,UAAU;IAAG;IAC7C;QAAE,OAAO;QAAO,SAAS;QAAO,UAAU;IAAG;CAC9C;AAED,MAAM,qBAAqB;IACzB;QAAE,OAAO;QAAO,OAAO;QAAK,WAAW;IAAG;IAC1C;QAAE,OAAO;QAAO,OAAO;QAAK,WAAW;IAAG;IAC1C;QAAE,OAAO;QAAO,OAAO;QAAK,WAAW;IAAG;IAC1C;QAAE,OAAO;QAAO,OAAO;QAAK,WAAW;IAAG;IAC1C;QAAE,OAAO;QAAO,OAAO;QAAK,WAAW;IAAG;IAC1C;QAAE,OAAO;QAAO,OAAO;QAAK,WAAW;IAAG;CAC3C;AAED,MAAM,0BAA0B;IAC9B;QAAE,MAAM;QAAe,OAAO;QAAI,OAAO;IAAU;IACnD;QAAE,MAAM;QAAe,OAAO;QAAI,OAAO;IAAU;IACnD;QAAE,MAAM;QAAY,OAAO;QAAI,OAAO;IAAU;IAChD;QAAE,MAAM;QAAY,OAAO;QAAI,OAAO;IAAU;IAChD;QAAE,MAAM;QAAc,OAAO;QAAG,OAAO;IAAU;CAClD;AAED,MAAM,mBAAmB;IACvB;QAAE,MAAM;QAAgB,UAAU;QAAI,SAAS;IAAK;IACpD;QAAE,MAAM;QAAe,UAAU;QAAI,SAAS;IAAI;IAClD;QAAE,MAAM;QAAiB,UAAU;QAAI,SAAS;IAAI;IACpD;QAAE,MAAM;QAAgB,UAAU;QAAI,SAAS;IAAI;IACnD;QAAE,MAAM;QAAe,UAAU;QAAI,SAAS;IAAI;CACnD;AAEc,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,GAAG;gBACrC,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAW;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,QAAQ,CAAC,WAAW;QACvB,OAAO;IACT;IAEA,MAAM,sBAAsB,eAAe,CAAC,gBAAgB,MAAM,GAAG,EAAE,CAAC,OAAO;IAC/E,MAAM,uBAAuB,eAAe,CAAC,gBAAgB,MAAM,GAAG,EAAE,CAAC,OAAO;IAChF,MAAM,gBAAgB,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EAAE,qBAAqB;IAErE,MAAM,uBAAuB,eAAe,CAAC,gBAAgB,MAAM,GAAG,EAAE,CAAC,QAAQ;IACjF,MAAM,wBAAwB,eAAe,CAAC,gBAAgB,MAAM,GAAG,EAAE,CAAC,QAAQ;IAClF,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EAAE,sBAAsB;IAEvE,qBACE,6LAAC,kJAAA,CAAA,UAAe;QAAC,OAAM;kBACrB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAM5C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,sOAAA,CAAA,qBAAkB;4CAAC,WAAU;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;sEAElB,6LAAC;4DAAI,WAAW,AAAC,kDAEhB,OADC,iBAAiB,IAAI,mBAAmB;;gEAEvC,iBAAiB,kBAChB,6LAAC,wNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,6LAAC,4NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAE1B,KAAK,GAAG,CAAC,eAAe,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,WAAU;sEACZ;;;;;;sEAEH,6LAAC;4DAAI,WAAW,AAAC,kDAEhB,OADC,kBAAkB,IAAI,mBAAmB;;gEAExC,kBAAkB,kBACjB,6LAAC,wNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;yFAEvB,6LAAC,4NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAE1B,KAAK,GAAG,CAAC,gBAAgB,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQjD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DACX,kBAAkB,CAAC,mBAAmB,MAAM,GAAG,EAAE,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAG3D,6LAAC;oDAAG,WAAU;8DACX,kBAAkB,CAAC,mBAAmB,MAAM,GAAG,EAAE,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASxE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;wCAAC,MAAM;;0DACf,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;gDAAC,WAAW,CAAC,QAAU;wDAAC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;wDAAS;qDAAU;;;;;;0DACzE,6LAAC,uJAAA,CAAA,OAAI;gDAAC,MAAK;gDAAW,SAAQ;gDAAU,QAAO;gDAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;sCAM5E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wCAAC,MAAM;;0DACd,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,6LAAC,wJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;0DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;0DACR,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAQ,MAAK;;;;;;0DAC1B,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAY,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMtC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;0DACP,6LAAC,kJAAA,CAAA,MAAG;gDACF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,MAAK;gDACL,SAAQ;gDACR,OAAO;wDAAC,EAAE,IAAI,EAAE,OAAO,EAAE;2DAAK,AAAC,GAAU,OAAR,MAAK,KAAqC,OAAlC,CAAC,CAAC,WAAW,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC,IAAG;;0DAE1E,wBAAwB,GAAG,CAAC,CAAC,OAAO,sBACnC,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,MAAM,KAAK;uDAAlC,AAAC,QAAa,OAAN;;;;;;;;;;0DAGvB,6LAAC,0JAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,6LAAC;4CAAwB,WAAU;;8DACjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAqC,QAAQ;;;;;;;;;;;sEAE/D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAqC,SAAS,IAAI;;;;;;8EAC/D,6LAAC;oEAAE,WAAU;;wEAAyB,SAAS,QAAQ;wEAAC;;;;;;;;;;;;;;;;;;;8DAG5D,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,OAAO;;;;;;;2CAX1B,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBvC;GA9NwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}