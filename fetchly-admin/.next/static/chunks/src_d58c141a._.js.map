{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp, getApps } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,\n};\n\n// Initialize Firebase\nconst app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\nexport default app;\n"], "names": [], "mappings": ";;;;;;AAMU;AANV;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAA,GAAA,mLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,KAAK,IAAI,CAAA,GAAA,mLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,mLAAA,CAAA,UAAO,AAAD,GAAG,CAAC,EAAE;AAG1E,MAAM,OAAO,CAAA,GAAA,yMAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,kKAAA,CAAA,eAAY,AAAD,EAAE;AACxB,MAAM,UAAU,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD,EAAE;uCAEnB", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { \n  User as FirebaseUser, \n  signInWithEmailAndPassword, \n  signOut as firebaseSignOut,\n  onAuthStateChanged \n} from 'firebase/auth';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { auth, db } from '@/lib/firebase';\nimport { AdminUser } from '@/types';\n\ninterface AuthContextType {\n  user: FirebaseUser | null;\n  adminUser: AdminUser | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<FirebaseUser | null>(null);\n  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\n      console.log('Auth state changed:', firebaseUser?.email);\n      setUser(firebaseUser);\n\n      if (firebaseUser) {\n        try {\n          console.log('Checking admin status for user:', firebaseUser.uid);\n\n          // First check if user is an admin in the admins collection\n          const adminDoc = await getDoc(doc(db, 'admins', firebaseUser.uid));\n          console.log('Admin doc exists:', adminDoc.exists());\n\n          if (adminDoc.exists()) {\n            console.log('Found admin in admins collection');\n            setAdminUser({\n              id: adminDoc.id,\n              ...adminDoc.data()\n            } as AdminUser);\n          } else {\n            // If not in admins collection, check users collection for admin role\n            console.log('Checking users collection for admin role');\n            const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));\n            console.log('User doc exists:', userDoc.exists());\n            console.log('User data:', userDoc.exists() ? userDoc.data() : 'No data');\n\n            if (userDoc.exists() && userDoc.data()?.role === 'admin') {\n              console.log('Found admin in users collection');\n              // Create admin user object from user data\n              const userData = userDoc.data();\n              setAdminUser({\n                id: userDoc.id,\n                email: userData.email || firebaseUser.email || '',\n                displayName: userData.displayName || firebaseUser.displayName || 'Admin',\n                role: userData.superAdmin ? 'super_admin' : 'admin',\n                permissions: [\n                  'manage_users',\n                  'manage_providers',\n                  'manage_bookings',\n                  'manage_payments',\n                  'manage_content',\n                  'view_analytics',\n                  ...(userData.superAdmin ? ['manage_admins'] : [])\n                ],\n                createdAt: userData.createdAt?.toDate() || new Date(),\n                lastLoginAt: userData.lastLoginAt?.toDate() || null\n              } as AdminUser);\n            } else {\n              // User is not an admin, don't sign them out immediately\n              console.log('User is not an admin');\n              setAdminUser(null);\n            }\n          }\n        } catch (error) {\n          console.error('Error fetching admin user:', error);\n          // Don't sign out on error, just set adminUser to null\n          setAdminUser(null);\n        }\n      } else {\n        setAdminUser(null);\n      }\n      \n      setLoading(false);\n    });\n\n    return unsubscribe;\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      await signInWithEmailAndPassword(auth, email, password);\n    } catch (error) {\n      console.error('Sign in error:', error);\n      throw error;\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      await firebaseSignOut(auth);\n      setAdminUser(null);\n    } catch (error) {\n      console.error('Sign out error:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    adminUser,\n    loading,\n    signIn,\n    signOut,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAMA;AAAA;AACA;;;AAVA;;;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAQT,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,cAAc,CAAA,GAAA,oNAAA,CAAA,qBAAkB,AAAD,EAAE,yHAAA,CAAA,OAAI;sDAAE,OAAO;oBAClD,QAAQ,GAAG,CAAC,uBAAuB,yBAAA,mCAAA,aAAc,KAAK;oBACtD,QAAQ;oBAER,IAAI,cAAc;wBAChB,IAAI;4BACF,QAAQ,GAAG,CAAC,mCAAmC,aAAa,GAAG;4BAE/D,2DAA2D;4BAC3D,MAAM,WAAW,MAAM,CAAA,GAAA,kKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,UAAU,aAAa,GAAG;4BAChE,QAAQ,GAAG,CAAC,qBAAqB,SAAS,MAAM;4BAEhD,IAAI,SAAS,MAAM,IAAI;gCACrB,QAAQ,GAAG,CAAC;gCACZ,aAAa;oCACX,IAAI,SAAS,EAAE;oCACf,GAAG,SAAS,IAAI,EAAE;gCACpB;4BACF,OAAO;oCAOmB;gCANxB,qEAAqE;gCACrE,QAAQ,GAAG,CAAC;gCACZ,MAAM,UAAU,MAAM,CAAA,GAAA,kKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,kKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,aAAa,GAAG;gCAC9D,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,MAAM;gCAC9C,QAAQ,GAAG,CAAC,cAAc,QAAQ,MAAM,KAAK,QAAQ,IAAI,KAAK;gCAE9D,IAAI,QAAQ,MAAM,MAAM,EAAA,gBAAA,QAAQ,IAAI,gBAAZ,oCAAA,cAAgB,IAAI,MAAK,SAAS;wCAkB3C,qBACE;oCAlBf,QAAQ,GAAG,CAAC;oCACZ,0CAA0C;oCAC1C,MAAM,WAAW,QAAQ,IAAI;oCAC7B,aAAa;wCACX,IAAI,QAAQ,EAAE;wCACd,OAAO,SAAS,KAAK,IAAI,aAAa,KAAK,IAAI;wCAC/C,aAAa,SAAS,WAAW,IAAI,aAAa,WAAW,IAAI;wCACjE,MAAM,SAAS,UAAU,GAAG,gBAAgB;wCAC5C,aAAa;4CACX;4CACA;4CACA;4CACA;4CACA;4CACA;+CACI,SAAS,UAAU,GAAG;gDAAC;6CAAgB,GAAG,EAAE;yCACjD;wCACD,WAAW,EAAA,sBAAA,SAAS,SAAS,cAAlB,0CAAA,oBAAoB,MAAM,OAAM,IAAI;wCAC/C,aAAa,EAAA,wBAAA,SAAS,WAAW,cAApB,4CAAA,sBAAsB,MAAM,OAAM;oCACjD;gCACF,OAAO;oCACL,wDAAwD;oCACxD,QAAQ,GAAG,CAAC;oCACZ,aAAa;gCACf;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,8BAA8B;4BAC5C,sDAAsD;4BACtD,aAAa;wBACf;oBACF,OAAO;wBACL,aAAa;oBACf;oBAEA,WAAW;gBACb;;YAEA,OAAO;QACT;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,MAAM,CAAA,GAAA,6NAAA,CAAA,6BAA0B,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE,OAAO;QAChD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,MAAM,CAAA,GAAA,yMAAA,CAAA,UAAe,AAAD,EAAE,yHAAA,CAAA,OAAI;YAC1B,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAzGgB;KAAA", "debugId": null}}]}