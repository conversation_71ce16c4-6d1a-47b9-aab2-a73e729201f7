{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, formatDistanceToNow } from 'date-fns';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy');\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy HH:mm');\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return formatDistanceToNow(dateObj, { addSuffix: true });\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    // Booking statuses\n    pending: 'bg-yellow-100 text-yellow-800',\n    confirmed: 'bg-blue-100 text-blue-800',\n    'in-progress': 'bg-purple-100 text-purple-800',\n    completed: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    disputed: 'bg-orange-100 text-orange-800',\n    \n    // Payment statuses\n    paid: 'bg-green-100 text-green-800',\n    refunded: 'bg-gray-100 text-gray-800',\n    failed: 'bg-red-100 text-red-800',\n    \n    // Provider statuses\n    approved: 'bg-green-100 text-green-800',\n    rejected: 'bg-red-100 text-red-800',\n    \n    // General statuses\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    verified: 'bg-blue-100 text-blue-800',\n    unverified: 'bg-yellow-100 text-yellow-800',\n  };\n  \n  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '');\n  if (cleaned.length === 10) {\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n  }\n  return phone;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return current > 0 ? 100 : 0;\n  return ((current - previous) / previous) * 100;\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;QAAE,WAAW;IAAK;AACxD;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,mBAAmB;QACnB,SAAS;QACT,WAAW;QACX,eAAe;QACf,WAAW;QACX,WAAW;QACX,UAAU;QAEV,mBAAmB;QACnB,MAAM;QACN,UAAU;QACV,QAAQ;QAER,oBAAoB;QACpB,UAAU;QACV,UAAU;QAEV,mBAAmB;QACnB,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;IACd;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,oBAAoB,KAAa;IAC/C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,AAAC,IAA2B,OAAxB,QAAQ,KAAK,CAAC,GAAG,IAAG,MAA2B,OAAvB,QAAQ,KAAK,CAAC,GAAG,IAAG,KAAoB,OAAjB,QAAQ,KAAK,CAAC;IAC1E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO,UAAU,IAAI,MAAM;IAC/C,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  HomeIcon,\n  UsersIcon,\n  UserGroupIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  BellIcon,\n} from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Users', href: '/dashboard/users', icon: UsersIcon },\n  { name: 'Providers', href: '/dashboard/providers', icon: UserGroupIcon },\n  { name: 'Bookings', href: '/dashboard/bookings', icon: CalendarDaysIcon },\n  { name: 'Payments', href: '/dashboard/payments', icon: CurrencyDollarIcon },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },\n  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },\n];\n\nexport default function DashboardLayout({ children, title }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { adminUser, signOut } = useAuth();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/login');\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-slate-50\">\n      {/* Mobile sidebar */}\n      <div className={cn(\n        \"fixed inset-0 z-50 lg:hidden\",\n        sidebarOpen ? \"block\" : \"hidden\"\n      )}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex items-center mb-3\">\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900\">{adminUser?.displayName}</p>\n                <p className=\"text-xs text-gray-500\">{adminUser?.role}</p>\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-2\" />\n              Sign out\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n              {title && (\n                <h1 className=\"ml-4 lg:ml-0 text-xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"text-gray-400 hover:text-gray-600\">\n                <BellIcon className=\"h-6 w-6\" />\n              </button>\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center lg:hidden\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAlBA;;;;;;AAyBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,oNAAA,CAAA,YAAS;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,4NAAA,CAAA,gBAAa;IAAC;IACvE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACxE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,sOAAA,CAAA,qBAAkB;IAAC;IAC1E;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,0NAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,gNAAA,CAAA,UAAO;IAAC;CAChE;AAEc,SAAS,gBAAgB,KAAyC;QAAzC,EAAE,QAAQ,EAAE,KAAK,EAAwB,GAAzC;QA+ErB,wBAyCA;;IAvHjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gCACA,cAAc,UAAU;;kCAExB,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAaxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAG/D,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCALL,KAAK,IAAI;;;;;;;;;;sCASpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,sBAAA,iCAAA,yBAAA,UAAW,WAAW,cAAtB,6CAAA,uBAAwB,MAAM,CAAC,OAAM;;;;;;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,sBAAA,gCAAA,UAAW,WAAW;;;;;;8DACxE,6LAAC;oDAAE,WAAU;8DAAyB,sBAAA,gCAAA,UAAW,IAAI;;;;;;;;;;;;;;;;;;8CAGzD,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,oPAAA,CAAA,4BAAyB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;wCAEtB,uBACC,6LAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;;8CAGtE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAChB,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,CAAA,sBAAA,iCAAA,0BAAA,UAAW,WAAW,cAAtB,8CAAA,wBAAwB,MAAM,CAAC,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAxIwB;;QAES,kIAAA,CAAA,UAAO;QACvB,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/app/dashboard/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport {\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  EyeIcon,\n  PencilIcon,\n  TrashIcon,\n} from '@heroicons/react/24/outline';\nimport { formatDate, getStatusColor, getInitials } from '@/lib/utils';\nimport { User } from '@/types';\n\n// Mock data - replace with real data from Firebase\nconst mockUsers: User[] = [\n  {\n    id: '1',\n    email: '<EMAIL>',\n    displayName: '<PERSON>',\n    phoneNumber: '+****************',\n    createdAt: new Date('2024-01-15'),\n    lastLoginAt: new Date('2024-01-30'),\n    isActive: true,\n    role: 'user',\n    profile: {\n      firstName: '<PERSON>',\n      lastName: '<PERSON>',\n      pets: [\n        { id: '1', name: '<PERSON>', type: 'dog', breed: 'Golden Retriever', age: 3, weight: 65, specialNeeds: [], photos: [] }\n      ]\n    }\n  },\n  {\n    id: '2',\n    email: '<EMAIL>',\n    displayName: '<PERSON>',\n    phoneNumber: '+****************',\n    createdAt: new Date('2024-01-20'),\n    lastLoginAt: new Date('2024-01-29'),\n    isActive: true,\n    role: 'user',\n    profile: {\n      firstName: '<PERSON>',\n      lastName: 'Chen',\n      pets: [\n        { id: '2', name: 'Luna', type: 'cat', breed: 'Persian', age: 2, weight: 8, specialNeeds: [], photos: [] }\n      ]\n    }\n  },\n  {\n    id: '3',\n    email: '<EMAIL>',\n    displayName: 'Emily Davis',\n    phoneNumber: '+****************',\n    createdAt: new Date('2024-01-10'),\n    lastLoginAt: new Date('2024-01-25'),\n    isActive: false,\n    role: 'user',\n    profile: {\n      firstName: 'Emily',\n      lastName: 'Davis',\n      pets: []\n    }\n  },\n];\n\nexport default function UsersPage() {\n  const { user, adminUser, loading } = useAuth();\n  const router = useRouter();\n  const [users, setUsers] = useState<User[]>(mockUsers);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');\n\n  useEffect(() => {\n    if (!loading && (!user || !adminUser)) {\n      router.push('/login');\n    }\n  }, [user, adminUser, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-slate-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!user || !adminUser) {\n    return null;\n  }\n\n  const filteredUsers = users.filter(user => {\n    const matchesSearch = user.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.email.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || \n                         (statusFilter === 'active' && user.isActive) ||\n                         (statusFilter === 'inactive' && !user.isActive);\n    return matchesSearch && matchesStatus;\n  });\n\n  return (\n    <DashboardLayout title=\"Users Management\">\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between mb-6\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Users</h1>\n            <p className=\"mt-1 text-sm text-gray-600\">\n              Manage all registered users on the platform\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button className=\"btn btn-primary\">\n              Export Users\n            </button>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"card p-4 mb-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search users...\"\n                  className=\"input pl-10\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <select\n                className=\"input\"\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"active\">Active</option>\n                <option value=\"inactive\">Inactive</option>\n              </select>\n              <button className=\"btn btn-secondary\">\n                <FunnelIcon className=\"h-4 w-4 mr-2\" />\n                More Filters\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Users Table */}\n        <div className=\"card overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    User\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Contact\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Pets\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Joined\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Last Login\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredUsers.map((user) => (\n                  <tr key={user.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <div className=\"h-10 w-10 flex-shrink-0\">\n                          {user.photoURL ? (\n                            <img className=\"h-10 w-10 rounded-full\" src={user.photoURL} alt=\"\" />\n                          ) : (\n                            <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                              <span className=\"text-sm font-medium text-gray-700\">\n                                {getInitials(user.displayName)}\n                              </span>\n                            </div>\n                          )}\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {user.displayName}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            ID: {user.id}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">{user.email}</div>\n                      <div className=\"text-sm text-gray-500\">{user.phoneNumber || 'No phone'}</div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900\">\n                        {user.profile?.pets?.length || 0} pets\n                      </div>\n                      {user.profile?.pets && user.profile.pets.length > 0 && (\n                        <div className=\"text-sm text-gray-500\">\n                          {user.profile.pets[0].name} ({user.profile.pets[0].type})\n                          {user.profile.pets.length > 1 && ` +${user.profile.pets.length - 1} more`}\n                        </div>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`badge ${getStatusColor(user.isActive ? 'active' : 'inactive')}`}>\n                        {user.isActive ? 'Active' : 'Inactive'}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {formatDate(user.createdAt)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                      <div className=\"flex justify-end space-x-2\">\n                        <button className=\"text-blue-600 hover:text-blue-900\">\n                          <EyeIcon className=\"h-4 w-4\" />\n                        </button>\n                        <button className=\"text-gray-600 hover:text-gray-900\">\n                          <PencilIcon className=\"h-4 w-4\" />\n                        </button>\n                        <button className=\"text-red-600 hover:text-red-900\">\n                          <TrashIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n          \n          {filteredUsers.length === 0 && (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-500\">No users found matching your criteria.</p>\n            </div>\n          )}\n        </div>\n\n        {/* Pagination */}\n        <div className=\"flex items-center justify-between mt-6\">\n          <div className=\"text-sm text-gray-700\">\n            Showing <span className=\"font-medium\">1</span> to <span className=\"font-medium\">{filteredUsers.length}</span> of{' '}\n            <span className=\"font-medium\">{users.length}</span> results\n          </div>\n          <div className=\"flex space-x-2\">\n            <button className=\"btn btn-secondary\" disabled>\n              Previous\n            </button>\n            <button className=\"btn btn-secondary\" disabled>\n              Next\n            </button>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;;;AAbA;;;;;;;AAgBA,mDAAmD;AACnD,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,aAAa,IAAI,KAAK;QACtB,UAAU;QACV,MAAM;QACN,SAAS;YACP,WAAW;YACX,UAAU;YACV,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAO,MAAM;oBAAO,OAAO;oBAAoB,KAAK;oBAAG,QAAQ;oBAAI,cAAc,EAAE;oBAAE,QAAQ,EAAE;gBAAC;aAClH;QACH;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,aAAa,IAAI,KAAK;QACtB,UAAU;QACV,MAAM;QACN,SAAS;YACP,WAAW;YACX,UAAU;YACV,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAQ,MAAM;oBAAO,OAAO;oBAAW,KAAK;oBAAG,QAAQ;oBAAG,cAAc,EAAE;oBAAE,QAAQ,EAAE;gBAAC;aACzG;QACH;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,aAAa;QACb,WAAW,IAAI,KAAK;QACpB,aAAa,IAAI,KAAK;QACtB,UAAU;QACV,MAAM;QACN,SAAS;YACP,WAAW;YACX,UAAU;YACV,MAAM,EAAE;QACV;IACF;CACD;AAEc,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAEhF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,GAAG;gBACrC,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAM;QAAW;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,QAAQ,CAAC,WAAW;QACvB,OAAO;IACT;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAC7E,MAAM,gBAAgB,iBAAiB,SACjB,iBAAiB,YAAY,KAAK,QAAQ,IAC1C,iBAAiB,cAAc,CAAC,KAAK,QAAQ;QACnE,OAAO,iBAAiB;IAC1B;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;QAAC,OAAM;kBACrB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,WAAU;0CAAkB;;;;;;;;;;;;;;;;;8BAOxC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;sDAC/B,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;0CAInD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;0DAE/C,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;kDAE3B,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC,sNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;;;;;;;kDAKpG,6LAAC;wCAAM,WAAU;kDACd,cAAc,GAAG,CAAC,CAAC;gDA+BX,oBAAA,eAEF;iEAhCL,6LAAC;gDAAiB,WAAU;;kEAC1B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,KAAK,QAAQ,iBACZ,6LAAC;wEAAI,WAAU;wEAAyB,KAAK,KAAK,QAAQ;wEAAE,KAAI;;;;;6FAEhE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,WAAW;;;;;;;;;;;;;;;;8EAKrC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,KAAK,WAAW;;;;;;sFAEnB,6LAAC;4EAAI,WAAU;;gFAAwB;gFAChC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;kEAKpB,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EAAyB,KAAK,KAAK;;;;;;0EAClD,6LAAC;gEAAI,WAAU;0EAAyB,KAAK,WAAW,IAAI;;;;;;;;;;;;kEAE9D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;oEACZ,EAAA,gBAAA,KAAK,OAAO,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,MAAM,KAAI;oEAAE;;;;;;;4DAElC,EAAA,iBAAA,KAAK,OAAO,cAAZ,qCAAA,eAAc,IAAI,KAAI,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,mBAChD,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;oEAAC;oEAAG,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;oEAAC;oEACvD,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,AAAC,KAAiC,OAA7B,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAE;;;;;;;;;;;;;kEAIzE,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,AAAC,SAA8D,OAAtD,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,QAAQ,GAAG,WAAW;sEACjE,KAAK,QAAQ,GAAG,WAAW;;;;;;;;;;;kEAGhC,6LAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;kEAE5B,6LAAC;wDAAG,WAAU;kEACX,KAAK,WAAW,GAAG,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW,IAAI;;;;;;kEAErD,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,gNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;8EAErB,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,sNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;;;;;;8EAExB,6LAAC;oEAAO,WAAU;8EAChB,cAAA,6LAAC,oNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CA3DpB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;wBAqEvB,cAAc,MAAM,KAAK,mBACxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;8BAMnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAAwB;8CAC7B,6LAAC;oCAAK,WAAU;8CAAc;;;;;;gCAAQ;8CAAI,6LAAC;oCAAK,WAAU;8CAAe,cAAc,MAAM;;;;;;gCAAQ;gCAAI;8CACjH,6LAAC;oCAAK,WAAU;8CAAe,MAAM,MAAM;;;;;;gCAAQ;;;;;;;sCAErD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;oCAAoB,QAAQ;8CAAC;;;;;;8CAG/C,6LAAC;oCAAO,WAAU;oCAAoB,QAAQ;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GAlNwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}