"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[556],{2513:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))})},2596:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}},3319:(e,t,r)=>{r.d(t,{GP:()=>Y});let n={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function o(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let a={date:o({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:o({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:o({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},i={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function l(e){return(t,r)=>{let n;if("formatting"===((null==r?void 0:r.context)?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,o=(null==r?void 0:r.width)?String(r.width):t;n=e.formattingValues[o]||e.formattingValues[t]}else{let t=e.defaultWidth,o=(null==r?void 0:r.width)?String(r.width):e.defaultWidth;n=e.values[o]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}function s(e){return function(t){let r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=n.width,a=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],i=t.match(a);if(!i)return null;let l=i[0],s=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(s)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(s,e=>e.test(l)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(s,e=>e.test(l));return r=e.valueCallback?e.valueCallback(d):d,{value:r=n.valueCallback?n.valueCallback(r):r,rest:t.slice(l.length)}}}let d={code:"en-US",formatDistance:(e,t,r)=>{let o,a=n[e];if(o="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",t.toString()),null==r?void 0:r.addSuffix)if(r.comparison&&r.comparison>0)return"in "+o;else return o+" ago";return o},formatLong:a,formatRelative:(e,t,r,n)=>i[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:l({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:l({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:l({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:l({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:l({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.match(e.matchPattern);if(!n)return null;let o=n[0],a=t.match(e.parsePattern);if(!a)return null;let i=e.valueCallback?e.valueCallback(a[0]):a[0];return{value:i=r.valueCallback?r.valueCallback(i):i,rest:t.slice(o.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:s({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:s({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:s({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:s({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},c={},u=Symbol.for("constructDateFrom");function m(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&u in e?e[u](t):e instanceof Date?new e.constructor(t):new Date(t)}function f(e,t){return m(t||e,e)}function h(e){let t=f(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),e-r}function g(e,t){let r=f(e,null==t?void 0:t.in);return r.setHours(0,0,0,0),r}function p(e,t){var r,n,o,a,i,l,s,d;let u=null!=(d=null!=(s=null!=(l=null!=(i=null==t?void 0:t.weekStartsOn)?i:null==t||null==(n=t.locale)||null==(r=n.options)?void 0:r.weekStartsOn)?l:c.weekStartsOn)?s:null==(a=c.locale)||null==(o=a.options)?void 0:o.weekStartsOn)?d:0,m=f(e,null==t?void 0:t.in),h=m.getDay();return m.setDate(m.getDate()-(7*(h<u)+h-u)),m.setHours(0,0,0,0),m}function b(e,t){return p(e,{...t,weekStartsOn:1})}function w(e,t){let r=f(e,null==t?void 0:t.in),n=r.getFullYear(),o=m(r,0);o.setFullYear(n+1,0,4),o.setHours(0,0,0,0);let a=b(o),i=m(r,0);i.setFullYear(n,0,4),i.setHours(0,0,0,0);let l=b(i);return r.getTime()>=a.getTime()?n+1:r.getTime()>=l.getTime()?n:n-1}function v(e,t){var r,n,o,a,i,l,s,d;let u=f(e,null==t?void 0:t.in),h=u.getFullYear(),g=null!=(d=null!=(s=null!=(l=null!=(i=null==t?void 0:t.firstWeekContainsDate)?i:null==t||null==(n=t.locale)||null==(r=n.options)?void 0:r.firstWeekContainsDate)?l:c.firstWeekContainsDate)?s:null==(a=c.locale)||null==(o=a.options)?void 0:o.firstWeekContainsDate)?d:1,b=m((null==t?void 0:t.in)||e,0);b.setFullYear(h+1,0,g),b.setHours(0,0,0,0);let w=p(b,t),v=m((null==t?void 0:t.in)||e,0);v.setFullYear(h,0,g),v.setHours(0,0,0,0);let k=p(v,t);return+u>=+w?h+1:+u>=+k?h:h-1}function k(e,t){let r=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+r}let y={y(e,t){let r=e.getFullYear(),n=r>0?r:1-r;return k("yy"===t?n%100:n,t.length)},M(e,t){let r=e.getMonth();return"M"===t?String(r+1):k(r+1,2)},d:(e,t)=>k(e.getDate(),t.length),a(e,t){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>k(e.getHours()%12||12,t.length),H:(e,t)=>k(e.getHours(),t.length),m:(e,t)=>k(e.getMinutes(),t.length),s:(e,t)=>k(e.getSeconds(),t.length),S(e,t){let r=t.length;return k(Math.trunc(e.getMilliseconds()*Math.pow(10,r-3)),t.length)}},x={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},M={G:function(e,t,r){let n=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});default:return r.era(n,{width:"wide"})}},y:function(e,t,r){if("yo"===t){let t=e.getFullYear();return r.ordinalNumber(t>0?t:1-t,{unit:"year"})}return y.y(e,t)},Y:function(e,t,r,n){let o=v(e,n),a=o>0?o:1-o;return"YY"===t?k(a%100,2):"Yo"===t?r.ordinalNumber(a,{unit:"year"}):k(a,t.length)},R:function(e,t){return k(w(e),t.length)},u:function(e,t){return k(e.getFullYear(),t.length)},Q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return k(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return k(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,r){let n=e.getMonth();switch(t){case"M":case"MM":return y.M(e,t);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,r){let n=e.getMonth();switch(t){case"L":return String(n+1);case"LL":return k(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,r,n){let o=function(e,t){let r=f(e,null==t?void 0:t.in);return Math.round((p(r,t)-function(e,t){var r,n,o,a,i,l,s,d;let u=null!=(d=null!=(s=null!=(l=null!=(i=null==t?void 0:t.firstWeekContainsDate)?i:null==t||null==(n=t.locale)||null==(r=n.options)?void 0:r.firstWeekContainsDate)?l:c.firstWeekContainsDate)?s:null==(a=c.locale)||null==(o=a.options)?void 0:o.firstWeekContainsDate)?d:1,f=v(e,t),h=m((null==t?void 0:t.in)||e,0);return h.setFullYear(f,0,u),h.setHours(0,0,0,0),p(h,t)}(r,t))/6048e5)+1}(e,n);return"wo"===t?r.ordinalNumber(o,{unit:"week"}):k(o,t.length)},I:function(e,t,r){let n=function(e,t){let r=f(e,void 0);return Math.round((b(r)-function(e,t){let r=w(e,void 0),n=m(e,0);return n.setFullYear(r,0,4),n.setHours(0,0,0,0),b(n)}(r))/6048e5)+1}(e);return"Io"===t?r.ordinalNumber(n,{unit:"week"}):k(n,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):y.d(e,t)},D:function(e,t,r){let n=function(e,t){let r=f(e,void 0);return function(e,t,r){let[n,o]=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];let o=m.bind(null,e||r.find(e=>"object"==typeof e));return r.map(o)}(void 0,e,t),a=g(n),i=g(o);return Math.round((a-h(a)-(i-h(i)))/864e5)}(r,function(e,t){let r=f(e,void 0);return r.setFullYear(r.getFullYear(),0,1),r.setHours(0,0,0,0),r}(r))+1}(e);return"Do"===t?r.ordinalNumber(n,{unit:"dayOfYear"}):k(n,t.length)},E:function(e,t,r){let n=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,r,n){let o=e.getDay(),a=(o-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(a);case"ee":return k(a,2);case"eo":return r.ordinalNumber(a,{unit:"day"});case"eee":return r.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(o,{width:"short",context:"formatting"});default:return r.day(o,{width:"wide",context:"formatting"})}},c:function(e,t,r,n){let o=e.getDay(),a=(o-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(a);case"cc":return k(a,t.length);case"co":return r.ordinalNumber(a,{unit:"day"});case"ccc":return r.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(o,{width:"narrow",context:"standalone"});case"cccccc":return r.day(o,{width:"short",context:"standalone"});default:return r.day(o,{width:"wide",context:"standalone"})}},i:function(e,t,r){let n=e.getDay(),o=0===n?7:n;switch(t){case"i":return String(o);case"ii":return k(o,t.length);case"io":return r.ordinalNumber(o,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,r){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},b:function(e,t,r){let n,o=e.getHours();switch(n=12===o?x.noon:0===o?x.midnight:o/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},B:function(e,t,r){let n,o=e.getHours();switch(n=o>=17?x.evening:o>=12?x.afternoon:o>=4?x.morning:x.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return y.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):y.H(e,t)},K:function(e,t,r){let n=e.getHours()%12;return"Ko"===t?r.ordinalNumber(n,{unit:"hour"}):k(n,t.length)},k:function(e,t,r){let n=e.getHours();return(0===n&&(n=24),"ko"===t)?r.ordinalNumber(n,{unit:"hour"}):k(n,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):y.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):y.s(e,t)},S:function(e,t){return y.S(e,t)},X:function(e,t,r){let n=e.getTimezoneOffset();if(0===n)return"Z";switch(t){case"X":return P(n);case"XXXX":case"XX":return E(n);default:return E(n,":")}},x:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"x":return P(n);case"xxxx":case"xx":return E(n);default:return E(n,":")}},O:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+z(n,":");default:return"GMT"+E(n,":")}},z:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+z(n,":");default:return"GMT"+E(n,":")}},t:function(e,t,r){return k(Math.trunc(e/1e3),t.length)},T:function(e,t,r){return k(+e,t.length)}};function z(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=e>0?"-":"+",n=Math.abs(e),o=Math.trunc(n/60),a=n%60;return 0===a?r+String(o):r+String(o)+t+k(a,2)}function P(e,t){return e%60==0?(e>0?"-":"+")+k(Math.abs(e)/60,2):E(e,t)}function E(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=Math.abs(e);return(e>0?"-":"+")+k(Math.trunc(r/60),2)+t+k(r%60,2)}let W=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},j=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},C={p:j,P:(e,t)=>{let r,n=e.match(/(P+)(p+)?/)||[],o=n[1],a=n[2];if(!a)return W(e,t);switch(o){case"P":r=t.dateTime({width:"short"});break;case"PP":r=t.dateTime({width:"medium"});break;case"PPP":r=t.dateTime({width:"long"});break;default:r=t.dateTime({width:"full"})}return r.replace("{{date}}",W(o,t)).replace("{{time}}",j(a,t))}},S=/^D+$/,A=/^Y+$/,D=["D","DD","YY","YYYY"],O=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,L=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,H=/^'([^]*?)'?$/,T=/''/g,N=/[a-zA-Z]/;function Y(e,t,r){var n,o,a,i,l,s,u,m,h,g,p,b,w,v,k,y,x,z;let P=null!=(g=null!=(h=null==r?void 0:r.locale)?h:c.locale)?g:d,E=null!=(v=null!=(w=null!=(b=null!=(p=null==r?void 0:r.firstWeekContainsDate)?p:null==r||null==(o=r.locale)||null==(n=o.options)?void 0:n.firstWeekContainsDate)?b:c.firstWeekContainsDate)?w:null==(i=c.locale)||null==(a=i.options)?void 0:a.firstWeekContainsDate)?v:1,W=null!=(z=null!=(x=null!=(y=null!=(k=null==r?void 0:r.weekStartsOn)?k:null==r||null==(s=r.locale)||null==(l=s.options)?void 0:l.weekStartsOn)?y:c.weekStartsOn)?x:null==(m=c.locale)||null==(u=m.options)?void 0:u.weekStartsOn)?z:0,j=f(e,null==r?void 0:r.in);if(!(j instanceof Date||"object"==typeof j&&"[object Date]"===Object.prototype.toString.call(j))&&"number"!=typeof j||isNaN(+f(j)))throw RangeError("Invalid time value");let Y=t.match(L).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,C[t])(e,P.formatLong):e}).join("").match(O).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(H);return t?t[1].replace(T,"'"):e}(e)};if(M[t])return{isToken:!0,value:e};if(t.match(N))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});P.localize.preprocessor&&(Y=P.localize.preprocessor(j,Y));let q={firstWeekContainsDate:E,weekStartsOn:W,locale:P};return Y.map(n=>{if(!n.isToken)return n.value;let o=n.value;return(!(null==r?void 0:r.useAdditionalWeekYearTokens)&&A.test(o)||!(null==r?void 0:r.useAdditionalDayOfYearTokens)&&S.test(o))&&function(e,t,r){let n=function(e,t,r){let n="Y"===e[0]?"years":"days of the month";return"Use `".concat(e.toLowerCase(),"` instead of `").concat(e,"` (in `").concat(t,"`) for formatting ").concat(n," to the input `").concat(r,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md")}(e,t,r);if(console.warn(n),D.includes(e))throw RangeError(n)}(o,t,String(e)),(0,M[o[0]])(j,o,P.localize,q)}).join("")}},4219:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})},4500:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},5246:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))})},5500:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},5596:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))})},5695:(e,t,r)=>{var n=r(8999);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},6884:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))})},8828:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))})},8960:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},9598:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))})},9688:(e,t,r)=>{r.d(t,{QP:()=>ee});let n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},o=/^\[(.+)\]$/,a=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:i(t,e)).classGroupId=r;return}if("function"==typeof e)return l(e)?void a(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{a(o,i(t,e),r,n)})})},i=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},l=e=>e.isThemeGetter,s=/\s+/;function d(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=c(e))&&(n&&(n+=" "),n+=t);return n}let c=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=c(e[n]))&&(r&&(r+=" "),r+=t);return r},u=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},m=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,f=/^\((?:(\w[\w-]*):)?(.+)\)$/i,h=/^\d+\/\d+$/,g=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,p=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,b=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,w=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,v=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,k=e=>h.test(e),y=e=>!!e&&!Number.isNaN(Number(e)),x=e=>!!e&&Number.isInteger(Number(e)),M=e=>e.endsWith("%")&&y(e.slice(0,-1)),z=e=>g.test(e),P=()=>!0,E=e=>p.test(e)&&!b.test(e),W=()=>!1,j=e=>w.test(e),C=e=>v.test(e),S=e=>!D(e)&&!Y(e),A=e=>R(e,I,W),D=e=>m.test(e),O=e=>R(e,J,E),L=e=>R(e,_,y),H=e=>R(e,X,W),T=e=>R(e,$,C),N=e=>R(e,K,j),Y=e=>f.test(e),q=e=>V(e,J),F=e=>V(e,U),G=e=>V(e,X),B=e=>V(e,I),Z=e=>V(e,$),Q=e=>V(e,K,!0),R=(e,t,r)=>{let n=m.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},V=(e,t,r=!1)=>{let n=f.exec(e);return!!n&&(n[1]?t(n[1]):r)},X=e=>"position"===e||"percentage"===e,$=e=>"image"===e||"url"===e,I=e=>"length"===e||"size"===e||"bg-size"===e,J=e=>"length"===e,_=e=>"number"===e,U=e=>"family-name"===e,K=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let r,i,l,c=function(s){let d;return i=(r={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}})((d=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r,n=[],o=0,a=0,i=0;for(let r=0;r<e.length;r++){let l=e[r];if(0===o&&0===a){if(":"===l){n.push(e.slice(i,r)),i=r+1;continue}if("/"===l){t=r;continue}}"["===l?o++:"]"===l?o--:"("===l?a++:")"===l&&a--}let l=0===n.length?e:e.substring(i),s=(r=l).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:n,hasImportantModifier:s!==l,baseClassName:s,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n})(d),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}})(d),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)a(r[e],n,e,t);return n})(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||(e=>{if(o.test(e)){let t=o.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&i[e]?[...n,...i[e]]:n}}})(d)}).cache.get,l=r.cache.set,c=u,u(s)};function u(e){let t=i(e);if(t)return t;let n=((e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:a}=t,i=[],l=e.trim().split(s),d="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:s,modifiers:c,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:f}=r(t);if(s){d=t+(d.length>0?" "+d:d);continue}let h=!!f,g=n(h?m.substring(0,f):m);if(!g){if(!h||!(g=n(m))){d=t+(d.length>0?" "+d:d);continue}h=!1}let p=a(c).join(":"),b=u?p+"!":p,w=b+g;if(i.includes(w))continue;i.push(w);let v=o(g,h);for(let e=0;e<v.length;++e){let t=v[e];i.push(b+t)}d=t+(d.length>0?" "+d:d)}return d})(e,r);return l(e,n),n}return function(){return c(d.apply(null,arguments))}}(()=>{let e=u("color"),t=u("font"),r=u("text"),n=u("font-weight"),o=u("tracking"),a=u("leading"),i=u("breakpoint"),l=u("container"),s=u("spacing"),d=u("radius"),c=u("shadow"),m=u("inset-shadow"),f=u("text-shadow"),h=u("drop-shadow"),g=u("blur"),p=u("perspective"),b=u("aspect"),w=u("ease"),v=u("animate"),E=()=>["auto","avoid","all","avoid-page","page","left","right","column"],W=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],j=()=>[...W(),Y,D],C=()=>["auto","hidden","clip","visible","scroll"],R=()=>["auto","contain","none"],V=()=>[Y,D,s],X=()=>[k,"full","auto",...V()],$=()=>[x,"none","subgrid",Y,D],I=()=>["auto",{span:["full",x,Y,D]},x,Y,D],J=()=>[x,"auto",Y,D],_=()=>["auto","min","max","fr",Y,D],U=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],K=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...V()],et=()=>[k,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...V()],er=()=>[e,Y,D],en=()=>[...W(),G,H,{position:[Y,D]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",B,A,{size:[Y,D]}],ei=()=>[M,q,O],el=()=>["","none","full",d,Y,D],es=()=>["",y,q,O],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[y,M,G,H],em=()=>["","none",g,Y,D],ef=()=>["none",y,Y,D],eh=()=>["none",y,Y,D],eg=()=>[y,Y,D],ep=()=>[k,"full",...V()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[z],breakpoint:[z],color:[P],container:[z],"drop-shadow":[z],ease:["in","out","in-out"],font:[S],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[z],shadow:[z],spacing:["px",y],text:[z],"text-shadow":[z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",k,D,Y,b]}],container:["container"],columns:[{columns:[y,D,Y,l]}],"break-after":[{"break-after":E()}],"break-before":[{"break-before":E()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:j()}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:X()}],"inset-x":[{"inset-x":X()}],"inset-y":[{"inset-y":X()}],start:[{start:X()}],end:[{end:X()}],top:[{top:X()}],right:[{right:X()}],bottom:[{bottom:X()}],left:[{left:X()}],visibility:["visible","invisible","collapse"],z:[{z:[x,"auto",Y,D]}],basis:[{basis:[k,"full","auto",l,...V()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[y,k,"auto","initial","none",D]}],grow:[{grow:["",y,Y,D]}],shrink:[{shrink:["",y,Y,D]}],order:[{order:[x,"first","last","none",Y,D]}],"grid-cols":[{"grid-cols":$()}],"col-start-end":[{col:I()}],"col-start":[{"col-start":J()}],"col-end":[{"col-end":J()}],"grid-rows":[{"grid-rows":$()}],"row-start-end":[{row:I()}],"row-start":[{"row-start":J()}],"row-end":[{"row-end":J()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":_()}],"auto-rows":[{"auto-rows":_()}],gap:[{gap:V()}],"gap-x":[{"gap-x":V()}],"gap-y":[{"gap-y":V()}],"justify-content":[{justify:[...U(),"normal"]}],"justify-items":[{"justify-items":[...K(),"normal"]}],"justify-self":[{"justify-self":["auto",...K()]}],"align-content":[{content:["normal",...U()]}],"align-items":[{items:[...K(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...K(),{baseline:["","last"]}]}],"place-content":[{"place-content":U()}],"place-items":[{"place-items":[...K(),"baseline"]}],"place-self":[{"place-self":["auto",...K()]}],p:[{p:V()}],px:[{px:V()}],py:[{py:V()}],ps:[{ps:V()}],pe:[{pe:V()}],pt:[{pt:V()}],pr:[{pr:V()}],pb:[{pb:V()}],pl:[{pl:V()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":V()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":V()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[i]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,q,O]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,Y,L]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",M,D]}],"font-family":[{font:[F,D,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,Y,D]}],"line-clamp":[{"line-clamp":[y,"none",Y,L]}],leading:[{leading:[a,...V()]}],"list-image":[{"list-image":["none",Y,D]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Y,D]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[y,"from-font","auto",Y,O]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[y,"auto",Y,D]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y,D]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y,D]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},x,Y,D],radial:["",Y,D],conic:[x,Y,D]},Z,T]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[y,Y,D]}],"outline-w":[{outline:["",y,q,O]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,Q,N]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",m,Q,N]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[y,O]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,Q,N]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[y,Y,D]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[y]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[Y,D]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":W()}],"mask-image-conic-pos":[{"mask-conic":[y]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Y,D]}],filter:[{filter:["","none",Y,D]}],blur:[{blur:em()}],brightness:[{brightness:[y,Y,D]}],contrast:[{contrast:[y,Y,D]}],"drop-shadow":[{"drop-shadow":["","none",h,Q,N]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",y,Y,D]}],"hue-rotate":[{"hue-rotate":[y,Y,D]}],invert:[{invert:["",y,Y,D]}],saturate:[{saturate:[y,Y,D]}],sepia:[{sepia:["",y,Y,D]}],"backdrop-filter":[{"backdrop-filter":["","none",Y,D]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[y,Y,D]}],"backdrop-contrast":[{"backdrop-contrast":[y,Y,D]}],"backdrop-grayscale":[{"backdrop-grayscale":["",y,Y,D]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[y,Y,D]}],"backdrop-invert":[{"backdrop-invert":["",y,Y,D]}],"backdrop-opacity":[{"backdrop-opacity":[y,Y,D]}],"backdrop-saturate":[{"backdrop-saturate":[y,Y,D]}],"backdrop-sepia":[{"backdrop-sepia":["",y,Y,D]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":V()}],"border-spacing-x":[{"border-spacing-x":V()}],"border-spacing-y":[{"border-spacing-y":V()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Y,D]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[y,"initial",Y,D]}],ease:[{ease:["linear","initial",w,Y,D]}],delay:[{delay:[y,Y,D]}],animate:[{animate:["none",v,Y,D]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,Y,D]}],"perspective-origin":[{"perspective-origin":j()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:eh()}],"scale-x":[{"scale-x":eh()}],"scale-y":[{"scale-y":eh()}],"scale-z":[{"scale-z":eh()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[Y,D,"","none","gpu","cpu"]}],"transform-origin":[{origin:j()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ep()}],"translate-x":[{"translate-x":ep()}],"translate-y":[{"translate-y":ep()}],"translate-z":[{"translate-z":ep()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y,D]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y,D]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[y,q,O,L]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9994:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(2115);let o=n.forwardRef(function(e,t){let{title:r,titleId:o,...a}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},a),r?n.createElement("title",{id:o},r):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))})}}]);