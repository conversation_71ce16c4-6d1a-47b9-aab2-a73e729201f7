exports.id=852,exports.ids=[852],exports.modules={473:(a,b,c)=>{Promise.resolve().then(c.bind(c,29131))},4780:(a,b,c)=>{"use strict";c.d(b,{IM:()=>l,Yq:()=>i,cn:()=>g,qY:()=>j,tP:()=>k,vv:()=>h});var d=c(49384),e=c(82348),f=c(44867);function g(...a){return(0,e.QP)((0,d.$)(a))}function h(a){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a)}function i(a){let b="string"==typeof a?new Date(a):a;return(0,f.GP)(b,"MMM dd, yyyy")}function j(a){return({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800","in-progress":"bg-purple-100 text-purple-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800",disputed:"bg-orange-100 text-orange-800",paid:"bg-green-100 text-green-800",refunded:"bg-gray-100 text-gray-800",failed:"bg-red-100 text-red-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",verified:"bg-blue-100 text-blue-800",unverified:"bg-yellow-100 text-yellow-800"})[a.toLowerCase()]||"bg-gray-100 text-gray-800"}function k(a,b){return 0===b?100*(a>0):(a-b)/b*100}function l(a){return a.split(" ").map(a=>a.charAt(0).toUpperCase()).join("").slice(0,2)}},7613:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>n,A:()=>m});var d=c(60687),e=c(43210),f=c(61448),g=c(75535),h=c(67989),i=c(70146);let j=0===(0,h.Dk)().length?(0,h.Wp)({apiKey:"your_firebase_api_key",authDomain:"your_project.firebaseapp.com",projectId:"your_project_id",storageBucket:"your_project.appspot.com",messagingSenderId:"your_sender_id",appId:"your_app_id"}):(0,h.Dk)()[0],k=(0,f.xI)(j);(0,g.aU)(j),(0,i.c7)(j);let l=(0,e.createContext)(void 0);function m(){let a=(0,e.useContext)(l);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a}function n({children:a}){let[b,c]=(0,e.useState)(null),[g,h]=(0,e.useState)(null),[i,j]=(0,e.useState)(!0),m=async(a,b)=>{try{await (0,f.x9)(k,a,b)}catch(a){throw console.error("Sign in error:",a),a}},n=async()=>{try{await (0,f.CI)(k),h(null)}catch(a){throw console.error("Sign out error:",a),a}};return(0,d.jsx)(l.Provider,{value:{user:b,adminUser:g,loading:i,signIn:m,signOut:n},children:a})}},29131:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(61369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/contexts/AuthContext.tsx","useAuth");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/contexts/AuthContext.tsx","AuthProvider")},30028:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},36921:(a,b,c)=>{Promise.resolve().then(c.bind(c,7613))},60149:(a,b,c)=>{"use strict";c.d(b,{A:()=>u});var d=c(60687),e=c(43210),f=c(7613),g=c(16189),h=c(20816),i=c(87061),j=c(93635),k=c(61022),l=c(31082),m=c(45994),n=c(14566),o=c(81836),p=c(34944),q=c(6510),r=c(52238),s=c(4780);let t=[{name:"Dashboard",href:"/dashboard",icon:h.A},{name:"Users",href:"/dashboard/users",icon:i.A},{name:"Providers",href:"/dashboard/providers",icon:j.A},{name:"Bookings",href:"/dashboard/bookings",icon:k.A},{name:"Payments",href:"/dashboard/payments",icon:l.A},{name:"Analytics",href:"/dashboard/analytics",icon:m.A},{name:"Settings",href:"/dashboard/settings",icon:n.A}];function u({children:a,title:b}){let[c,h]=(0,e.useState)(!1),{adminUser:i,signOut:j}=(0,f.A)(),k=(0,g.useRouter)(),l=async()=>{try{await j(),k.push("/login")}catch(a){console.error("Sign out error:",a)}};return(0,d.jsxs)("div",{className:"min-h-screen bg-slate-50",children:[(0,d.jsxs)("div",{className:(0,s.cn)("fixed inset-0 z-50 lg:hidden",c?"block":"hidden"),children:[(0,d.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>h(!1)}),(0,d.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl",children:[(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 border-b border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-sm",children:"F"})}),(0,d.jsx)("span",{className:"ml-2 text-lg font-semibold text-gray-900",children:"Fetchly Admin"})]}),(0,d.jsx)("button",{onClick:()=>h(!1),className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(o.A,{className:"h-6 w-6"})})]}),(0,d.jsx)("nav",{className:"flex-1 px-4 py-4 space-y-1",children:t.map(a=>(0,d.jsxs)("a",{href:a.href,className:"sidebar-link sidebar-link-inactive",children:[(0,d.jsx)(a.icon,{className:"h-5 w-5 mr-3"}),a.name]},a.name))})]})]}),(0,d.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,d.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm",children:[(0,d.jsx)("div",{className:"flex h-16 items-center px-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-sm",children:"F"})}),(0,d.jsx)("span",{className:"ml-2 text-lg font-semibold text-gray-900",children:"Fetchly Admin"})]})}),(0,d.jsx)("nav",{className:"flex-1 px-4 py-4 space-y-1",children:t.map(a=>(0,d.jsxs)("a",{href:a.href,className:"sidebar-link sidebar-link-inactive",children:[(0,d.jsx)(a.icon,{className:"h-5 w-5 mr-3"}),a.name]},a.name))}),(0,d.jsxs)("div",{className:"p-4 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center mb-3",children:[(0,d.jsx)("div",{className:"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-gray-600 font-medium text-sm",children:i?.displayName?.charAt(0)||"A"})}),(0,d.jsxs)("div",{className:"ml-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:i?.displayName}),(0,d.jsx)("p",{className:"text-xs text-gray-500",children:i?.role})]})]}),(0,d.jsxs)("button",{onClick:l,className:"flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})}),(0,d.jsxs)("div",{className:"lg:pl-64",children:[(0,d.jsx)("div",{className:"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm",children:(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("button",{onClick:()=>h(!0),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,d.jsx)(q.A,{className:"h-6 w-6"})}),b&&(0,d.jsx)("h1",{className:"ml-4 lg:ml-0 text-xl font-semibold text-gray-900",children:b})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,d.jsx)(r.A,{className:"h-6 w-6"})}),(0,d.jsx)("div",{className:"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center lg:hidden",children:(0,d.jsx)("span",{className:"text-gray-600 font-medium text-sm",children:i?.displayName?.charAt(0)||"A"})})]})]})}),(0,d.jsx)("main",{className:"flex-1",children:(0,d.jsx)("div",{className:"py-6",children:a})})]})]})}},61135:()=>{},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},93580:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,metadata:()=>h});var d=c(37413),e=c(7339),f=c.n(e);c(61135);var g=c(29131);let h={title:"Fetchly Admin Dashboard",description:"Administrative portal for Fetchly pet services platform"};function i({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:f().className,children:(0,d.jsx)(g.AuthProvider,{children:a})})})}}};