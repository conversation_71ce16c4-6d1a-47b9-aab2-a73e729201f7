{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, formatDistanceToNow } from 'date-fns';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy');\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy HH:mm');\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return formatDistanceToNow(dateObj, { addSuffix: true });\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    // Booking statuses\n    pending: 'bg-yellow-100 text-yellow-800',\n    confirmed: 'bg-blue-100 text-blue-800',\n    'in-progress': 'bg-purple-100 text-purple-800',\n    completed: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    disputed: 'bg-orange-100 text-orange-800',\n    \n    // Payment statuses\n    paid: 'bg-green-100 text-green-800',\n    refunded: 'bg-gray-100 text-gray-800',\n    failed: 'bg-red-100 text-red-800',\n    \n    // Provider statuses\n    approved: 'bg-green-100 text-green-800',\n    rejected: 'bg-red-100 text-red-800',\n    \n    // General statuses\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    verified: 'bg-blue-100 text-blue-800',\n    unverified: 'bg-yellow-100 text-yellow-800',\n  };\n  \n  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '');\n  if (cleaned.length === 10) {\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n  }\n  return phone;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return current > 0 ? 100 : 0;\n  return ((current - previous) / previous) * 100;\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;QAAE,WAAW;IAAK;AACxD;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,mBAAmB;QACnB,SAAS;QACT,WAAW;QACX,eAAe;QACf,WAAW;QACX,WAAW;QACX,UAAU;QAEV,mBAAmB;QACnB,MAAM;QACN,UAAU;QACV,QAAQ;QAER,oBAAoB;QACpB,UAAU;QACV,UAAU;QAEV,mBAAmB;QACnB,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;IACd;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,oBAAoB,KAAa;IAC/C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC9E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO,UAAU,IAAI,MAAM;IAC/C,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  HomeIcon,\n  UsersIcon,\n  UserGroupIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  BellIcon,\n} from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Users', href: '/dashboard/users', icon: UsersIcon },\n  { name: 'Providers', href: '/dashboard/providers', icon: UserGroupIcon },\n  { name: 'Bookings', href: '/dashboard/bookings', icon: CalendarDaysIcon },\n  { name: 'Payments', href: '/dashboard/payments', icon: CurrencyDollarIcon },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },\n  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },\n];\n\nexport default function DashboardLayout({ children, title }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { adminUser, signOut } = useAuth();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/login');\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-slate-50\">\n      {/* Mobile sidebar */}\n      <div className={cn(\n        \"fixed inset-0 z-50 lg:hidden\",\n        sidebarOpen ? \"block\" : \"hidden\"\n      )}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex items-center mb-3\">\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900\">{adminUser?.displayName}</p>\n                <p className=\"text-xs text-gray-500\">{adminUser?.role}</p>\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-2\" />\n              Sign out\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n              {title && (\n                <h1 className=\"ml-4 lg:ml-0 text-xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"text-gray-400 hover:text-gray-600\">\n                <BellIcon className=\"h-6 w-6\" />\n              </button>\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center lg:hidden\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAlBA;;;;;;;AAyBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,iNAAA,CAAA,YAAS;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,yNAAA,CAAA,gBAAa;IAAC;IACvE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IACxE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,mOAAA,CAAA,qBAAkB;IAAC;IAC1E;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,uNAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,6MAAA,CAAA,UAAO;IAAC;CAChE;AAEc,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAwB;IAC/E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gCACA,cAAc,UAAU;;kCAExB,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAaxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCALL,KAAK,IAAI;;;;;;;;;;sCASpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,WAAW,aAAa,OAAO,MAAM;;;;;;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC,WAAW;;;;;;8DAC7D,8OAAC;oDAAE,WAAU;8DAAyB,WAAW;;;;;;;;;;;;;;;;;;8CAGrD,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,iPAAA,CAAA,4BAAyB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;wCAEtB,uBACC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;;8CAGtE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAChB,cAAA,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,WAAW,aAAa,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/app/dashboard/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport {\n  CogIcon,\n  BellIcon,\n  ShieldCheckIcon,\n  CurrencyDollarIcon,\n} from '@heroicons/react/24/outline';\n\nexport default function SettingsPage() {\n  const { user, adminUser, loading } = useAuth();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState('general');\n\n  // Settings state\n  const [settings, setSettings] = useState({\n    general: {\n      platformName: 'Fetchly',\n      supportEmail: '<EMAIL>',\n      maintenanceMode: false,\n      allowNewRegistrations: true,\n    },\n    notifications: {\n      emailNotifications: true,\n      pushNotifications: true,\n      smsNotifications: false,\n      weeklyReports: true,\n      monthlyReports: true,\n    },\n    payments: {\n      stripePublishableKey: 'pk_test_...',\n      stripeSecretKey: 'sk_test_...',\n      platformFeePercentage: 10,\n      minimumWithdrawal: 50,\n      payoutSchedule: 'weekly',\n    },\n    security: {\n      requireTwoFactor: false,\n      sessionTimeout: 30,\n      passwordMinLength: 8,\n      requirePasswordChange: 90,\n    },\n  });\n\n  useEffect(() => {\n    if (!loading && (!user || !adminUser)) {\n      router.push('/login');\n    }\n  }, [user, adminUser, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-slate-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!user || !adminUser) {\n    return null;\n  }\n\n  const tabs = [\n    { id: 'general', name: 'General', icon: CogIcon },\n    { id: 'notifications', name: 'Notifications', icon: BellIcon },\n    { id: 'payments', name: 'Payments', icon: CurrencyDollarIcon },\n    { id: 'security', name: 'Security', icon: ShieldCheckIcon },\n  ];\n\n  const handleSave = () => {\n    // Here you would save settings to Firebase or your backend\n    console.log('Saving settings:', settings);\n    // Show success message\n  };\n\n  const updateSetting = (category: string, key: string, value: unknown) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category as keyof typeof prev],\n        [key]: value,\n      },\n    }));\n  };\n\n  return (\n    <DashboardLayout title=\"Settings\">\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Settings</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            Manage platform configuration and preferences\n          </p>\n        </div>\n\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar */}\n          <div className=\"lg:w-64\">\n            <nav className=\"space-y-1\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                    activeTab === tab.id\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                  }`}\n                >\n                  <tab.icon className=\"h-5 w-5 mr-3\" />\n                  {tab.name}\n                </button>\n              ))}\n            </nav>\n          </div>\n\n          {/* Content */}\n          <div className=\"flex-1\">\n            <div className=\"card p-6\">\n              {activeTab === 'general' && (\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">General Settings</h3>\n                  \n                  <div className=\"grid grid-cols-1 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Platform Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        className=\"input\"\n                        value={settings.general.platformName}\n                        onChange={(e) => updateSetting('general', 'platformName', e.target.value)}\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Support Email\n                      </label>\n                      <input\n                        type=\"email\"\n                        className=\"input\"\n                        value={settings.general.supportEmail}\n                        onChange={(e) => updateSetting('general', 'supportEmail', e.target.value)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-700\">\n                          Maintenance Mode\n                        </label>\n                        <p className=\"text-sm text-gray-500\">\n                          Temporarily disable the platform for maintenance\n                        </p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        checked={settings.general.maintenanceMode}\n                        onChange={(e) => updateSetting('general', 'maintenanceMode', e.target.checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-700\">\n                          Allow New Registrations\n                        </label>\n                        <p className=\"text-sm text-gray-500\">\n                          Allow new users to register on the platform\n                        </p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        checked={settings.general.allowNewRegistrations}\n                        onChange={(e) => updateSetting('general', 'allowNewRegistrations', e.target.checked)}\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {activeTab === 'notifications' && (\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Notification Settings</h3>\n                  \n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-700\">\n                          Email Notifications\n                        </label>\n                        <p className=\"text-sm text-gray-500\">\n                          Receive notifications via email\n                        </p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        checked={settings.notifications.emailNotifications}\n                        onChange={(e) => updateSetting('notifications', 'emailNotifications', e.target.checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-700\">\n                          Push Notifications\n                        </label>\n                        <p className=\"text-sm text-gray-500\">\n                          Receive push notifications in browser\n                        </p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        checked={settings.notifications.pushNotifications}\n                        onChange={(e) => updateSetting('notifications', 'pushNotifications', e.target.checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-700\">\n                          SMS Notifications\n                        </label>\n                        <p className=\"text-sm text-gray-500\">\n                          Receive notifications via SMS\n                        </p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        checked={settings.notifications.smsNotifications}\n                        onChange={(e) => updateSetting('notifications', 'smsNotifications', e.target.checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-700\">\n                          Weekly Reports\n                        </label>\n                        <p className=\"text-sm text-gray-500\">\n                          Receive weekly analytics reports\n                        </p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        checked={settings.notifications.weeklyReports}\n                        onChange={(e) => updateSetting('notifications', 'weeklyReports', e.target.checked)}\n                      />\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-700\">\n                          Monthly Reports\n                        </label>\n                        <p className=\"text-sm text-gray-500\">\n                          Receive monthly analytics reports\n                        </p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        checked={settings.notifications.monthlyReports}\n                        onChange={(e) => updateSetting('notifications', 'monthlyReports', e.target.checked)}\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {activeTab === 'payments' && (\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Payment Settings</h3>\n                  \n                  <div className=\"grid grid-cols-1 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Platform Fee Percentage\n                      </label>\n                      <div className=\"relative\">\n                        <input\n                          type=\"number\"\n                          className=\"input pr-8\"\n                          value={settings.payments.platformFeePercentage}\n                          onChange={(e) => updateSetting('payments', 'platformFeePercentage', Number(e.target.value))}\n                        />\n                        <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                          <span className=\"text-gray-500 sm:text-sm\">%</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Minimum Withdrawal Amount\n                      </label>\n                      <div className=\"relative\">\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                          <span className=\"text-gray-500 sm:text-sm\">$</span>\n                        </div>\n                        <input\n                          type=\"number\"\n                          className=\"input pl-8\"\n                          value={settings.payments.minimumWithdrawal}\n                          onChange={(e) => updateSetting('payments', 'minimumWithdrawal', Number(e.target.value))}\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Payout Schedule\n                      </label>\n                      <select\n                        className=\"input\"\n                        value={settings.payments.payoutSchedule}\n                        onChange={(e) => updateSetting('payments', 'payoutSchedule', e.target.value)}\n                      >\n                        <option value=\"daily\">Daily</option>\n                        <option value=\"weekly\">Weekly</option>\n                        <option value=\"monthly\">Monthly</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {activeTab === 'security' && (\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Security Settings</h3>\n                  \n                  <div className=\"space-y-6\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-700\">\n                          Require Two-Factor Authentication\n                        </label>\n                        <p className=\"text-sm text-gray-500\">\n                          Require 2FA for all admin accounts\n                        </p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                        checked={settings.security.requireTwoFactor}\n                        onChange={(e) => updateSetting('security', 'requireTwoFactor', e.target.checked)}\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Session Timeout (minutes)\n                      </label>\n                      <input\n                        type=\"number\"\n                        className=\"input\"\n                        value={settings.security.sessionTimeout}\n                        onChange={(e) => updateSetting('security', 'sessionTimeout', Number(e.target.value))}\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Minimum Password Length\n                      </label>\n                      <input\n                        type=\"number\"\n                        className=\"input\"\n                        value={settings.security.passwordMinLength}\n                        onChange={(e) => updateSetting('security', 'passwordMinLength', Number(e.target.value))}\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Password Change Required (days)\n                      </label>\n                      <input\n                        type=\"number\"\n                        className=\"input\"\n                        value={settings.security.requirePasswordChange}\n                        onChange={(e) => updateSetting('security', 'requirePasswordChange', Number(e.target.value))}\n                      />\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Save Button */}\n              <div className=\"mt-8 pt-6 border-t border-gray-200\">\n                <div className=\"flex justify-end\">\n                  <button onClick={handleSave} className=\"btn btn-primary\">\n                    Save Changes\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,iBAAiB;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,SAAS;YACP,cAAc;YACd,cAAc;YACd,iBAAiB;YACjB,uBAAuB;QACzB;QACA,eAAe;YACb,oBAAoB;YACpB,mBAAmB;YACnB,kBAAkB;YAClB,eAAe;YACf,gBAAgB;QAClB;QACA,UAAU;YACR,sBAAsB;YACtB,iBAAiB;YACjB,uBAAuB;YACvB,mBAAmB;YACnB,gBAAgB;QAClB;QACA,UAAU;YACR,kBAAkB;YAClB,gBAAgB;YAChB,mBAAmB;YACnB,uBAAuB;QACzB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,GAAG;YACrC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAW;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,QAAQ,CAAC,WAAW;QACvB,OAAO;IACT;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM,6MAAA,CAAA,UAAO;QAAC;QAChD;YAAE,IAAI;YAAiB,MAAM;YAAiB,MAAM,+MAAA,CAAA,WAAQ;QAAC;QAC7D;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM,mOAAA,CAAA,qBAAkB;QAAC;QAC7D;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM,6NAAA,CAAA,kBAAe;QAAC;KAC3D;IAED,MAAM,aAAa;QACjB,2DAA2D;QAC3D,QAAQ,GAAG,CAAC,oBAAoB;IAChC,uBAAuB;IACzB;IAEA,MAAM,gBAAgB,CAAC,UAAkB,KAAa;QACpD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE;oBACV,GAAG,IAAI,CAAC,SAA8B;oBACtC,CAAC,IAAI,EAAE;gBACT;YACF,CAAC;IACH;IAEA,qBACE,8OAAC,+IAAA,CAAA,UAAe;QAAC,OAAM;kBACrB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,wDACA,sDACJ;;0DAEF,8OAAC,IAAI,IAAI;gDAAC,WAAU;;;;;;4CACnB,IAAI,IAAI;;uCATJ,IAAI,EAAE;;;;;;;;;;;;;;;sCAgBnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,2BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAElD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,OAAO,CAAC,YAAY;gEACpC,UAAU,CAAC,IAAM,cAAc,WAAW,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAI5E,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,OAAO,CAAC,YAAY;gEACpC,UAAU,CAAC,IAAM,cAAc,WAAW,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAI5E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAoC;;;;;;kFAGrD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,OAAO,CAAC,eAAe;gEACzC,UAAU,CAAC,IAAM,cAAc,WAAW,mBAAmB,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;kEAIjF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAoC;;;;;;kFAGrD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,OAAO,CAAC,qBAAqB;gEAC/C,UAAU,CAAC,IAAM,cAAc,WAAW,yBAAyB,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;oCAO5F,cAAc,iCACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAElD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAoC;;;;;;kFAGrD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,aAAa,CAAC,kBAAkB;gEAClD,UAAU,CAAC,IAAM,cAAc,iBAAiB,sBAAsB,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;kEAI1F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAoC;;;;;;kFAGrD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,aAAa,CAAC,iBAAiB;gEACjD,UAAU,CAAC,IAAM,cAAc,iBAAiB,qBAAqB,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;kEAIzF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAoC;;;;;;kFAGrD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,aAAa,CAAC,gBAAgB;gEAChD,UAAU,CAAC,IAAM,cAAc,iBAAiB,oBAAoB,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;kEAIxF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAoC;;;;;;kFAGrD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,aAAa,CAAC,aAAa;gEAC7C,UAAU,CAAC,IAAM,cAAc,iBAAiB,iBAAiB,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;kEAIrF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAoC;;;;;;kFAGrD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,aAAa,CAAC,cAAc;gEAC9C,UAAU,CAAC,IAAM,cAAc,iBAAiB,kBAAkB,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;oCAO3F,cAAc,4BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAElD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,WAAU;wEACV,OAAO,SAAS,QAAQ,CAAC,qBAAqB;wEAC9C,UAAU,CAAC,IAAM,cAAc,YAAY,yBAAyB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;kFAE3F,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFAA2B;;;;;;;;;;;;;;;;;;;;;;;kEAKjD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFAA2B;;;;;;;;;;;kFAE7C,8OAAC;wEACC,MAAK;wEACL,WAAU;wEACV,OAAO,SAAS,QAAQ,CAAC,iBAAiB;wEAC1C,UAAU,CAAC,IAAM,cAAc,YAAY,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;kEAK3F,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,WAAU;gEACV,OAAO,SAAS,QAAQ,CAAC,cAAc;gEACvC,UAAU,CAAC,IAAM,cAAc,YAAY,kBAAkB,EAAE,MAAM,CAAC,KAAK;;kFAE3E,8OAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAOjC,cAAc,4BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAElD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAoC;;;;;;kFAGrD,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIvC,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,SAAS,QAAQ,CAAC,gBAAgB;gEAC3C,UAAU,CAAC,IAAM,cAAc,YAAY,oBAAoB,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;kEAInF,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,QAAQ,CAAC,cAAc;gEACvC,UAAU,CAAC,IAAM,cAAc,YAAY,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAItF,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,QAAQ,CAAC,iBAAiB;gEAC1C,UAAU,CAAC,IAAM,cAAc,YAAY,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAIzF,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,QAAQ,CAAC,qBAAqB;gEAC9C,UAAU,CAAC,IAAM,cAAc,YAAY,yBAAyB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAQnG,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,SAAS;gDAAY,WAAU;0DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3E", "debugId": null}}]}