{"version": 3, "sources": [], "sections": [{"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp, getApps } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,\n};\n\n// Debug: Log config (remove in production)\nconsole.log('Firebase Config:', {\n  apiKey: firebaseConfig.apiKey ? `${firebaseConfig.apiKey.substring(0, 10)}...` : 'MISSING',\n  authDomain: firebaseConfig.authDomain || 'MISSING',\n  projectId: firebaseConfig.projectId || 'MISSING',\n  storageBucket: firebaseConfig.storageBucket || 'MISSING',\n  messagingSenderId: firebaseConfig.messagingSenderId || 'MISSING',\n  appId: firebaseConfig.appId ? `${firebaseConfig.appId.substring(0, 10)}...` : 'MISSING',\n});\n\n// Initialize Firebase\nconst app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\n\n// Initialize Firebase services\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport const storage = getStorage(app);\n\nexport default app;\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,2CAA2C;AAC3C,QAAQ,GAAG,CAAC,oBAAoB;IAC9B,QAAQ,eAAe,MAAM,GAAG,GAAG,eAAe,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;IACjF,YAAY,eAAe,UAAU,IAAI;IACzC,WAAW,eAAe,SAAS,IAAI;IACvC,eAAe,eAAe,aAAa,IAAI;IAC/C,mBAAmB,eAAe,iBAAiB,IAAI;IACvD,OAAO,eAAe,KAAK,GAAG,GAAG,eAAe,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;AAChF;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,KAAK,IAAI,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,GAAG,CAAC,EAAE;AAG1E,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;AACxB,MAAM,UAAU,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE;uCAEnB", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { \n  User as FirebaseUser, \n  signInWithEmailAndPassword, \n  signOut as firebaseSignOut,\n  onAuthStateChanged \n} from 'firebase/auth';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { auth, db } from '@/lib/firebase';\nimport { AdminUser } from '@/types';\n\ninterface AuthContextType {\n  user: FirebaseUser | null;\n  adminUser: AdminUser | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<FirebaseUser | null>(null);\n  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const checkAdminStatus = async (firebaseUser: FirebaseUser) => {\n      setLoading(true);\n      try {\n        console.log('Checking admin status for user:', firebaseUser.uid);\n\n        // First check if user is an admin in the admins collection\n        try {\n          const adminDoc = await getDoc(doc(db, 'admins', firebaseUser.uid));\n          console.log('Admin doc exists:', adminDoc.exists());\n\n          if (adminDoc.exists()) {\n            console.log('Found admin in admins collection');\n            const adminData = adminDoc.data();\n            const adminUser: AdminUser = {\n              id: adminDoc.id,\n              email: adminData.email || firebaseUser.email || '',\n              displayName: adminData.displayName || firebaseUser.displayName || 'Admin',\n              role: adminData.superAdmin ? 'super_admin' : 'admin',\n              permissions: adminData.permissions || [\n                'manage_users',\n                'manage_providers',\n                'manage_bookings',\n                'manage_payments',\n                'manage_content',\n                'view_analytics',\n                ...(adminData.superAdmin ? ['manage_admins'] : [])\n              ],\n              createdAt: adminData.createdAt?.toDate() || new Date(),\n              lastLoginAt: adminData.lastLoginAt?.toDate()\n            };\n            setAdminUser(adminUser);\n            return;\n          }\n        } catch (adminError) {\n          console.error('Error checking admins collection:', adminError);\n        }\n\n        // If not in admins collection, check users collection for admin role\n        try {\n          const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));\n          console.log('User doc exists:', userDoc.exists());\n\n          if (userDoc.exists()) {\n            const userData = userDoc.data();\n            console.log('User role:', userData.role);\n            \n            if (userData.role === 'admin' || userData.role === 'super_admin') {\n              console.log('Found admin in users collection');\n              const adminUser: AdminUser = {\n                id: userDoc.id,\n                email: userData.email || firebaseUser.email || '',\n                displayName: userData.displayName || firebaseUser.displayName || 'Admin',\n                role: userData.role,\n                permissions: userData.permissions || [\n                  'manage_users',\n                  'manage_providers',\n                  'manage_bookings',\n                  ...(userData.role === 'super_admin' ? ['manage_admins'] : [])\n                ],\n                createdAt: userData.createdAt?.toDate() || new Date(),\n                lastLoginAt: userData.lastLoginAt?.toDate()\n              };\n              setAdminUser(adminUser);\n              return;\n            }\n          }\n        } catch (userError) {\n          console.error('Error checking users collection:', userError);\n        }\n\n        // If we get here, user is not an admin\n        console.log('User is not an admin');\n        setAdminUser(null);\n      } catch (error) {\n        console.error('Error checking admin status:', error);\n        setAdminUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {\n      console.log('Auth state changed:', firebaseUser?.email);\n      setUser(firebaseUser);\n\n      if (firebaseUser) {\n        checkAdminStatus(firebaseUser);\n      } else {\n        setAdminUser(null);\n        setLoading(false);\n      }\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      await signInWithEmailAndPassword(auth, email, password);\n    } catch (error) {\n      console.error('Sign in error:', error);\n      throw error;\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      await firebaseSignOut(auth);\n      setAdminUser(null);\n    } catch (error) {\n      console.error('Sign out error:', error);\n      throw error;\n    }\n  };\n\n  const value = {\n    user,\n    adminUser,\n    loading,\n    signIn,\n    signOut,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAMA;AAAA;AACA;AAVA;;;;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,OAAO;YAC9B,WAAW;YACX,IAAI;gBACF,QAAQ,GAAG,CAAC,mCAAmC,aAAa,GAAG;gBAE/D,2DAA2D;gBAC3D,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,aAAa,GAAG;oBAChE,QAAQ,GAAG,CAAC,qBAAqB,SAAS,MAAM;oBAEhD,IAAI,SAAS,MAAM,IAAI;wBACrB,QAAQ,GAAG,CAAC;wBACZ,MAAM,YAAY,SAAS,IAAI;wBAC/B,MAAM,YAAuB;4BAC3B,IAAI,SAAS,EAAE;4BACf,OAAO,UAAU,KAAK,IAAI,aAAa,KAAK,IAAI;4BAChD,aAAa,UAAU,WAAW,IAAI,aAAa,WAAW,IAAI;4BAClE,MAAM,UAAU,UAAU,GAAG,gBAAgB;4BAC7C,aAAa,UAAU,WAAW,IAAI;gCACpC;gCACA;gCACA;gCACA;gCACA;gCACA;mCACI,UAAU,UAAU,GAAG;oCAAC;iCAAgB,GAAG,EAAE;6BAClD;4BACD,WAAW,UAAU,SAAS,EAAE,YAAY,IAAI;4BAChD,aAAa,UAAU,WAAW,EAAE;wBACtC;wBACA,aAAa;wBACb;oBACF;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,qCAAqC;gBACrD;gBAEA,qEAAqE;gBACrE,IAAI;oBACF,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS,aAAa,GAAG;oBAC9D,QAAQ,GAAG,CAAC,oBAAoB,QAAQ,MAAM;oBAE9C,IAAI,QAAQ,MAAM,IAAI;wBACpB,MAAM,WAAW,QAAQ,IAAI;wBAC7B,QAAQ,GAAG,CAAC,cAAc,SAAS,IAAI;wBAEvC,IAAI,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,KAAK,eAAe;4BAChE,QAAQ,GAAG,CAAC;4BACZ,MAAM,YAAuB;gCAC3B,IAAI,QAAQ,EAAE;gCACd,OAAO,SAAS,KAAK,IAAI,aAAa,KAAK,IAAI;gCAC/C,aAAa,SAAS,WAAW,IAAI,aAAa,WAAW,IAAI;gCACjE,MAAM,SAAS,IAAI;gCACnB,aAAa,SAAS,WAAW,IAAI;oCACnC;oCACA;oCACA;uCACI,SAAS,IAAI,KAAK,gBAAgB;wCAAC;qCAAgB,GAAG,EAAE;iCAC7D;gCACD,WAAW,SAAS,SAAS,EAAE,YAAY,IAAI;gCAC/C,aAAa,SAAS,WAAW,EAAE;4BACrC;4BACA,aAAa;4BACb;wBACF;oBACF;gBACF,EAAE,OAAO,WAAW;oBAClB,QAAQ,KAAK,CAAC,oCAAoC;gBACpD;gBAEA,uCAAuC;gBACvC,QAAQ,GAAG,CAAC;gBACZ,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,aAAa;YACf,SAAU;gBACR,WAAW;YACb;QACF;QAEA,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,CAAC;YAC5C,QAAQ,GAAG,CAAC,uBAAuB,cAAc;YACjD,QAAQ;YAER,IAAI,cAAc;gBAChB,iBAAiB;YACnB,OAAO;gBACL,aAAa;gBACb,WAAW;YACb;QACF;QAEA,OAAO,IAAM;IACf,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;QAChD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAe,AAAD,EAAE,sHAAA,CAAA,OAAI;YAC1B,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}]}