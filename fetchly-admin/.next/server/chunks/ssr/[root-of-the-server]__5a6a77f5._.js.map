{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, formatDistanceToNow } from 'date-fns';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount);\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy');\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return format(dateObj, 'MMM dd, yyyy HH:mm');\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return formatDistanceToNow(dateObj, { addSuffix: true });\n}\n\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    // Booking statuses\n    pending: 'bg-yellow-100 text-yellow-800',\n    confirmed: 'bg-blue-100 text-blue-800',\n    'in-progress': 'bg-purple-100 text-purple-800',\n    completed: 'bg-green-100 text-green-800',\n    cancelled: 'bg-red-100 text-red-800',\n    disputed: 'bg-orange-100 text-orange-800',\n    \n    // Payment statuses\n    paid: 'bg-green-100 text-green-800',\n    refunded: 'bg-gray-100 text-gray-800',\n    failed: 'bg-red-100 text-red-800',\n    \n    // Provider statuses\n    approved: 'bg-green-100 text-green-800',\n    rejected: 'bg-red-100 text-red-800',\n    \n    // General statuses\n    active: 'bg-green-100 text-green-800',\n    inactive: 'bg-gray-100 text-gray-800',\n    verified: 'bg-blue-100 text-blue-800',\n    unverified: 'bg-yellow-100 text-yellow-800',\n  };\n  \n  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '');\n  if (cleaned.length === 10) {\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n  }\n  return phone;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function calculatePercentageChange(current: number, previous: number): number {\n  if (previous === 0) return current > 0 ? 100 : 0;\n  return ((current - previous) / previous) * 100;\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS;QAAE,WAAW;IAAK;AACxD;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,mBAAmB;QACnB,SAAS;QACT,WAAW;QACX,eAAe;QACf,WAAW;QACX,WAAW;QACX,UAAU;QAEV,mBAAmB;QACnB,MAAM;QACN,UAAU;QACV,QAAQ;QAER,oBAAoB;QACpB,UAAU;QACV,UAAU;QAEV,mBAAmB;QACnB,QAAQ;QACR,UAAU;QACV,UAAU;QACV,YAAY;IACd;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,oBAAoB,KAAa;IAC/C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC9E;IACA,OAAO;AACT;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,0BAA0B,OAAe,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO,UAAU,IAAI,MAAM;IAC/C,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;AAC7C;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  HomeIcon,\n  UsersIcon,\n  UserGroupIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n  ChartBarIcon,\n  CogIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon,\n  BellIcon,\n} from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Users', href: '/dashboard/users', icon: UsersIcon },\n  { name: 'Providers', href: '/dashboard/providers', icon: UserGroupIcon },\n  { name: 'Bookings', href: '/dashboard/bookings', icon: CalendarDaysIcon },\n  { name: 'Payments', href: '/dashboard/payments', icon: CurrencyDollarIcon },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },\n  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },\n];\n\nexport default function DashboardLayout({ children, title }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { adminUser, signOut } = useAuth();\n  const router = useRouter();\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/login');\n    } catch (error) {\n      console.error('Sign out error:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-slate-50\">\n      {/* Mobile sidebar */}\n      <div className={cn(\n        \"fixed inset-0 z-50 lg:hidden\",\n        sidebarOpen ? \"block\" : \"hidden\"\n      )}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center px-4 border-b border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">F</span>\n              </div>\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Fetchly Admin</span>\n            </div>\n          </div>\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"sidebar-link sidebar-link-inactive\"\n              >\n                <item.icon className=\"h-5 w-5 mr-3\" />\n                {item.name}\n              </a>\n            ))}\n          </nav>\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex items-center mb-3\">\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n              <div className=\"ml-3\">\n                <p className=\"text-sm font-medium text-gray-900\">{adminUser?.displayName}</p>\n                <p className=\"text-xs text-gray-500\">{adminUser?.role}</p>\n              </div>\n            </div>\n            <button\n              onClick={handleSignOut}\n              className=\"flex items-center w-full px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-2\" />\n              Sign out\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n              >\n                <Bars3Icon className=\"h-6 w-6\" />\n              </button>\n              {title && (\n                <h1 className=\"ml-4 lg:ml-0 text-xl font-semibold text-gray-900\">{title}</h1>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"text-gray-400 hover:text-gray-600\">\n                <BellIcon className=\"h-6 w-6\" />\n              </button>\n              <div className=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center lg:hidden\">\n                <span className=\"text-gray-600 font-medium text-sm\">\n                  {adminUser?.displayName?.charAt(0) || 'A'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAlBA;;;;;;;AAyBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAoB,MAAM,iNAAA,CAAA,YAAS;IAAC;IAC3D;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,yNAAA,CAAA,gBAAa;IAAC;IACvE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IACxE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,mOAAA,CAAA,qBAAkB;IAAC;IAC1E;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,uNAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,6MAAA,CAAA,UAAO;IAAC;CAChE;AAEc,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAwB;IAC/E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gCACA,cAAc,UAAU;;kCAExB,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAaxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCALL,KAAK,IAAI;;;;;;;;;;sCASpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,WAAW,aAAa,OAAO,MAAM;;;;;;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAqC,WAAW;;;;;;8DAC7D,8OAAC;oDAAE,WAAU;8DAAyB,WAAW;;;;;;;;;;;;;;;;;;8CAGrD,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,iPAAA,CAAA,4BAAyB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;wCAEtB,uBACC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;;8CAGtE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAChB,cAAA,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,WAAW,aAAa,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/fetchly%20admin/fetchly-admin/src/app/dashboard/bookings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport {\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  EyeIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n} from '@heroicons/react/24/outline';\nimport { formatDate, formatCurrency, getStatusColor } from '@/lib/utils';\nimport { Booking } from '@/types';\n\n// Mock data - replace with real data from Firebase\nconst mockBookings: Booking[] = [\n  {\n    id: '1',\n    userId: 'user1',\n    providerId: 'provider1',\n    serviceId: 'service1',\n    petIds: ['pet1'],\n    startDate: new Date('2024-02-15T10:00:00'),\n    endDate: new Date('2024-02-15T11:00:00'),\n    status: 'confirmed',\n    totalAmount: 25,\n    paymentStatus: 'paid',\n    specialInstructions: '<PERSON> loves treats and needs to be walked slowly',\n    createdAt: new Date('2024-02-10'),\n    updatedAt: new Date('2024-02-12')\n  },\n  {\n    id: '2',\n    userId: 'user2',\n    providerId: 'provider1',\n    serviceId: 'service2',\n    petIds: ['pet2'],\n    startDate: new Date('2024-02-16T14:00:00'),\n    endDate: new Date('2024-02-16T15:00:00'),\n    status: 'pending',\n    totalAmount: 40,\n    paymentStatus: 'pending',\n    createdAt: new Date('2024-02-14'),\n    updatedAt: new Date('2024-02-14')\n  },\n  {\n    id: '3',\n    userId: 'user3',\n    providerId: 'provider2',\n    serviceId: 'service3',\n    petIds: ['pet3'],\n    startDate: new Date('2024-02-12T09:00:00'),\n    endDate: new Date('2024-02-12T11:00:00'),\n    status: 'completed',\n    totalAmount: 60,\n    paymentStatus: 'paid',\n    createdAt: new Date('2024-02-08'),\n    updatedAt: new Date('2024-02-12')\n  },\n  {\n    id: '4',\n    userId: 'user1',\n    providerId: 'provider1',\n    serviceId: 'service1',\n    petIds: ['pet1'],\n    startDate: new Date('2024-02-20T16:00:00'),\n    endDate: new Date('2024-02-20T17:00:00'),\n    status: 'cancelled',\n    totalAmount: 25,\n    paymentStatus: 'refunded',\n    createdAt: new Date('2024-02-18'),\n    updatedAt: new Date('2024-02-19')\n  },\n];\n\n// Mock user and provider data for display\nconst mockUserData: Record<string, { name: string; email: string }> = {\n  user1: { name: 'Sarah Johnson', email: '<EMAIL>' },\n  user2: { name: 'Mike Chen', email: '<EMAIL>' },\n  user3: { name: 'Emily Davis', email: '<EMAIL>' },\n};\n\nconst mockProviderData: Record<string, { name: string }> = {\n  provider1: { name: 'PetCare Plus' },\n  provider2: { name: 'Happy Tails Pet Services' },\n};\n\nconst mockServiceData: Record<string, { name: string }> = {\n  service1: { name: 'Dog Walking' },\n  service2: { name: 'Pet Sitting' },\n  service3: { name: 'Pet Grooming' },\n};\n\nexport default function BookingsPage() {\n  const { user, adminUser, loading } = useAuth();\n  const router = useRouter();\n  const [bookings, setBookings] = useState<Booking[]>(mockBookings);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'confirmed' | 'completed' | 'cancelled'>('all');\n\n  useEffect(() => {\n    if (!loading && (!user || !adminUser)) {\n      router.push('/login');\n    }\n  }, [user, adminUser, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-slate-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!user || !adminUser) {\n    return null;\n  }\n\n  const filteredBookings = bookings.filter(booking => {\n    const userData = mockUserData[booking.userId];\n    const providerData = mockProviderData[booking.providerId];\n    const serviceData = mockServiceData[booking.serviceId];\n    \n    const matchesSearch = userData?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         userData?.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         providerData?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         serviceData?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         booking.id.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;\n    \n    return matchesSearch && matchesStatus;\n  });\n\n  const totalBookings = bookings.length;\n  const pendingBookings = bookings.filter(b => b.status === 'pending').length;\n  const completedBookings = bookings.filter(b => b.status === 'completed').length;\n  const totalRevenue = bookings\n    .filter(b => b.paymentStatus === 'paid')\n    .reduce((sum, b) => sum + b.totalAmount, 0);\n\n  return (\n    <DashboardLayout title=\"Bookings Management\">\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"sm:flex sm:items-center sm:justify-between mb-6\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Bookings</h1>\n            <p className=\"mt-1 text-sm text-gray-600\">\n              Manage all bookings and appointments\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0\">\n            <button className=\"btn btn-primary\">\n              Export Bookings\n            </button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-4 mb-6\">\n          <div className=\"card p-4\">\n            <div className=\"flex items-center\">\n              <CalendarDaysIcon className=\"h-8 w-8 text-blue-600\" />\n              <div className=\"ml-3\">\n                <div className=\"text-2xl font-bold text-gray-900\">{totalBookings}</div>\n                <div className=\"text-sm text-gray-600\">Total Bookings</div>\n              </div>\n            </div>\n          </div>\n          <div className=\"card p-4\">\n            <div className=\"flex items-center\">\n              <CalendarDaysIcon className=\"h-8 w-8 text-yellow-600\" />\n              <div className=\"ml-3\">\n                <div className=\"text-2xl font-bold text-yellow-600\">{pendingBookings}</div>\n                <div className=\"text-sm text-gray-600\">Pending</div>\n              </div>\n            </div>\n          </div>\n          <div className=\"card p-4\">\n            <div className=\"flex items-center\">\n              <CalendarDaysIcon className=\"h-8 w-8 text-green-600\" />\n              <div className=\"ml-3\">\n                <div className=\"text-2xl font-bold text-green-600\">{completedBookings}</div>\n                <div className=\"text-sm text-gray-600\">Completed</div>\n              </div>\n            </div>\n          </div>\n          <div className=\"card p-4\">\n            <div className=\"flex items-center\">\n              <CurrencyDollarIcon className=\"h-8 w-8 text-purple-600\" />\n              <div className=\"ml-3\">\n                <div className=\"text-2xl font-bold text-purple-600\">{formatCurrency(totalRevenue)}</div>\n                <div className=\"text-sm text-gray-600\">Revenue</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"card p-4 mb-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search bookings...\"\n                  className=\"input pl-10\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <select\n                className=\"input\"\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'pending' | 'confirmed' | 'completed' | 'cancelled')}\n              >\n                <option value=\"all\">All Status</option>\n                <option value=\"pending\">Pending</option>\n                <option value=\"confirmed\">Confirmed</option>\n                <option value=\"completed\">Completed</option>\n                <option value=\"cancelled\">Cancelled</option>\n              </select>\n              <button className=\"btn btn-secondary\">\n                <FunnelIcon className=\"h-4 w-4 mr-2\" />\n                More Filters\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bookings Table */}\n        <div className=\"card overflow-hidden\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Booking ID\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Customer\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Provider\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Service\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Date & Time\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Amount\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredBookings.map((booking) => {\n                  const userData = mockUserData[booking.userId];\n                  const providerData = mockProviderData[booking.providerId];\n                  const serviceData = mockServiceData[booking.serviceId];\n                  \n                  return (\n                    <tr key={booking.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">#{booking.id}</div>\n                        <div className=\"text-sm text-gray-500\">\n                          Created {formatDate(booking.createdAt)}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">{userData?.name}</div>\n                        <div className=\"text-sm text-gray-500\">{userData?.email}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-900\">{providerData?.name}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-900\">{serviceData?.name}</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm text-gray-900\">{formatDate(booking.startDate)}</div>\n                        <div className=\"text-sm text-gray-500\">\n                          {booking.startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - \n                          {booking.endDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {formatCurrency(booking.totalAmount)}\n                        </div>\n                        <div className={`text-xs ${getStatusColor(booking.paymentStatus)}`}>\n                          {booking.paymentStatus}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`badge ${getStatusColor(booking.status)}`}>\n                          {booking.status}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                        <button className=\"text-blue-600 hover:text-blue-900\">\n                          <EyeIcon className=\"h-4 w-4\" />\n                        </button>\n                      </td>\n                    </tr>\n                  );\n                })}\n              </tbody>\n            </table>\n          </div>\n          \n          {filteredBookings.length === 0 && (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-500\">No bookings found matching your criteria.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAbA;;;;;;;;AAgBA,mDAAmD;AACnD,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,QAAQ;YAAC;SAAO;QAChB,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;QACb,eAAe;QACf,qBAAqB;QACrB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,QAAQ;YAAC;SAAO;QAChB,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;QACb,eAAe;QACf,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,QAAQ;YAAC;SAAO;QAChB,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;QACb,eAAe;QACf,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,QAAQ;YAAC;SAAO;QAChB,WAAW,IAAI,KAAK;QACpB,SAAS,IAAI,KAAK;QAClB,QAAQ;QACR,aAAa;QACb,eAAe;QACf,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAED,0CAA0C;AAC1C,MAAM,eAAgE;IACpE,OAAO;QAAE,MAAM;QAAiB,OAAO;IAA0B;IACjE,OAAO;QAAE,MAAM;QAAa,OAAO;IAAsB;IACzD,OAAO;QAAE,MAAM;QAAe,OAAO;IAAwB;AAC/D;AAEA,MAAM,mBAAqD;IACzD,WAAW;QAAE,MAAM;IAAe;IAClC,WAAW;QAAE,MAAM;IAA2B;AAChD;AAEA,MAAM,kBAAoD;IACxD,UAAU;QAAE,MAAM;IAAc;IAChC,UAAU;QAAE,MAAM;IAAc;IAChC,UAAU;QAAE,MAAM;IAAe;AACnC;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+D;IAE9G,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,GAAG;YACrC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAW;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,QAAQ,CAAC,WAAW;QACvB,OAAO;IACT;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,WAAW,YAAY,CAAC,QAAQ,MAAM,CAAC;QAC7C,MAAM,eAAe,gBAAgB,CAAC,QAAQ,UAAU,CAAC;QACzD,MAAM,cAAc,eAAe,CAAC,QAAQ,SAAS,CAAC;QAEtD,MAAM,gBAAgB,UAAU,KAAK,cAAc,SAAS,WAAW,WAAW,OAC7D,UAAU,MAAM,cAAc,SAAS,WAAW,WAAW,OAC7D,cAAc,KAAK,cAAc,SAAS,WAAW,WAAW,OAChE,aAAa,KAAK,cAAc,SAAS,WAAW,WAAW,OAC/D,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE7E,MAAM,gBAAgB,iBAAiB,SAAS,QAAQ,MAAM,KAAK;QAEnE,OAAO,iBAAiB;IAC1B;IAEA,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;IAC3E,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IAC/E,MAAM,eAAe,SAClB,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK,QAChC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,WAAW,EAAE;IAE3C,qBACE,8OAAC,+IAAA,CAAA,UAAe;QAAC,OAAM;kBACrB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,WAAU;0CAAkB;;;;;;;;;;;;;;;;;8BAOxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+NAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAI7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+NAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAI7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+NAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAI7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mOAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;kDAC9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;0DACpE,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;sDAC/B,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;0DAE/C,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;kDAE5B,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,mNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAkF;;;;;;;;;;;;;;;;;kDAKpG,8OAAC;wCAAM,WAAU;kDACd,iBAAiB,GAAG,CAAC,CAAC;4CACrB,MAAM,WAAW,YAAY,CAAC,QAAQ,MAAM,CAAC;4CAC7C,MAAM,eAAe,gBAAgB,CAAC,QAAQ,UAAU,CAAC;4CACzD,MAAM,cAAc,eAAe,CAAC,QAAQ,SAAS,CAAC;4CAEtD,qBACE,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;oEAAoC;oEAAE,QAAQ,EAAE;;;;;;;0EAC/D,8OAAC;gEAAI,WAAU;;oEAAwB;oEAC5B,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EAAqC,UAAU;;;;;;0EAC9D,8OAAC;gEAAI,WAAU;0EAAyB,UAAU;;;;;;;;;;;;kEAEpD,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;sEAAyB,cAAc;;;;;;;;;;;kEAExD,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;sEAAyB,aAAa;;;;;;;;;;;kEAEvD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EAAyB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,SAAS;;;;;;0EACpE,8OAAC;gEAAI,WAAU;;oEACZ,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;wEAAE,MAAM;wEAAW,QAAQ;oEAAU;oEAAG;oEACjF,QAAQ,OAAO,CAAC,kBAAkB,CAAC,EAAE,EAAE;wEAAE,MAAM;wEAAW,QAAQ;oEAAU;;;;;;;;;;;;;kEAGjF,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,WAAW;;;;;;0EAErC,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa,GAAG;0EAC/D,QAAQ,aAAa;;;;;;;;;;;;kEAG1B,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAW,CAAC,MAAM,EAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM,GAAG;sEACvD,QAAQ,MAAM;;;;;;;;;;;kEAGnB,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAO,WAAU;sEAChB,cAAA,8OAAC,6MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;;+CAvChB,QAAQ,EAAE;;;;;wCA4CvB;;;;;;;;;;;;;;;;;wBAKL,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}