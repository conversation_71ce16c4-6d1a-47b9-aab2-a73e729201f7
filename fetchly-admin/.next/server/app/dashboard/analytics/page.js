(()=>{var a={};a.id=754,a.ids=[754],a.modules={175:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if(!a||"object"!=typeof a)return!1;let b=Object.getPrototypeOf(a);return(null===b||b===Object.prototype||null===Object.getPrototypeOf(b))&&"[object Object]"===Object.prototype.toString.call(a)}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1640:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObject=function(a){return null!==a&&("object"==typeof a||"function"==typeof a)}},1706:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.eq=function(a,b){return a===b||Number.isNaN(a)&&Number.isNaN(b)}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3567:(a,b,c)=>{"use strict";var d=c(43210);"function"==typeof Object.is&&Object.is,d.useSyncExternalStore,d.useRef,d.useEffect,d.useMemo,d.useDebugValue},4080:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/analytics/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/analytics/page.tsx","default")},5664:(a,b,c)=>{a.exports=c(87509).get},6895:(a,b,c)=>{"use strict";c(3567)},9474:(a,b,c)=>{a.exports=c(33731).last},10687:(a,b,c)=>{a.exports=c(75446).sortBy},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10907:(a,b,c)=>{"use strict";var d=c(43210),e=c(57379),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},11117:a=>{"use strict";var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),new d().__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},12728:(a,b,c)=>{a.exports=c(92292).isEqual},14454:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(1640),f=c(23457),g=c(1706);function h(a,b,c,d){if(b===a)return!0;switch(typeof b){case"object":return function(a,b,c,d){if(null==b)return!0;if(Array.isArray(b))return i(a,b,c,d);if(b instanceof Map){var e=a,g=b,h=c,k=d;if(0===g.size)return!0;if(!(e instanceof Map))return!1;for(let[a,b]of g.entries())if(!1===h(e.get(a),b,a,e,g,k))return!1;return!0}if(b instanceof Set)return j(a,b,c,d);let l=Object.keys(b);if(null==a)return 0===l.length;if(0===l.length)return!0;if(d&&d.has(b))return d.get(b)===a;d&&d.set(b,a);try{for(let e=0;e<l.length;e++){let g=l[e];if(!f.isPrimitive(a)&&!(g in a)||void 0===b[g]&&void 0!==a[g]||null===b[g]&&null!==a[g]||!c(a[g],b[g],g,a,b,d))return!1}return!0}finally{d&&d.delete(b)}}(a,b,c,d);case"function":if(Object.keys(b).length>0)return h(a,{...b},c,d);return g.eq(a,b);default:if(!e.isObject(a))return g.eq(a,b);if("string"==typeof b)return""===b;return!0}}function i(a,b,c,d){if(0===b.length)return!0;if(!Array.isArray(a))return!1;let e=new Set;for(let f=0;f<b.length;f++){let g=b[f],h=!1;for(let i=0;i<a.length;i++){if(e.has(i))continue;let j=a[i],k=!1;if(c(j,g,f,a,b,d)&&(k=!0),k){e.add(i),h=!0;break}}if(!h)return!1}return!0}function j(a,b,c,d){return 0===b.size||a instanceof Set&&i([...a],[...b],c,d)}b.isMatchWith=function(a,b,c){return"function"!=typeof c?d.isMatch(a,b):h(a,b,function a(b,d,e,f,g,i){let j=c(b,d,e,f,g,i);return void 0!==j?!!j:h(b,d,a,i)},new Map)},b.isSetMatch=j},15708:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(95819);b.toNumber=function(a){return d.isSymbol(a)?NaN:Number(a)}},17617:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92681),e=c(40144),f=c(74838),g=c(30415);b.iteratee=function(a){if(null==a)return d.identity;switch(typeof a){case"function":return a;case"object":if(Array.isArray(a)&&2===a.length)return g.matchesProperty(a[0],a[1]);return f.matches(a);case"string":case"symbol":case"number":return e.property(a)}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:a=>{"use strict";a.exports=require("process")},20911:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(55100);b.debounce=function(a,b=0,c={}){let e;"object"!=typeof c&&(c={});let{leading:f=!1,trailing:g=!0,maxWait:h}=c,i=[,,];f&&(i[0]="leading"),g&&(i[1]="trailing");let j=null,k=d.debounce(function(...b){e=a.apply(this,b),j=null},b,{edges:i}),l=function(...b){return null!=h&&(null===j&&(j=Date.now()),Date.now()-j>=h)?(e=a.apply(this,b),j=Date.now(),k.cancel(),k.schedule(),e):(k.apply(this,b),e)};return l.cancel=k.cancel,l.flush=()=>(k.flush(),e),l}},21251:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isTypedArray=function(a){return ArrayBuffer.isView(a)&&!(a instanceof DataView)}},21424:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toArray=function(a){return Array.isArray(a)?a:Array.from(a)}},21820:a=>{"use strict";a.exports=require("os")},23457:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPrimitive=function(a){return null==a||"object"!=typeof a&&"function"!=typeof a}},23854:(a,b,c)=>{a.exports=c(45263).uniqBy},26349:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(48130);b.isArrayLike=function(a){return null!=a&&"function"!=typeof a&&d.isLength(a.length)}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27469:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.argumentsTag="[object Arguments]",b.arrayBufferTag="[object ArrayBuffer]",b.arrayTag="[object Array]",b.bigInt64ArrayTag="[object BigInt64Array]",b.bigUint64ArrayTag="[object BigUint64Array]",b.booleanTag="[object Boolean]",b.dataViewTag="[object DataView]",b.dateTag="[object Date]",b.errorTag="[object Error]",b.float32ArrayTag="[object Float32Array]",b.float64ArrayTag="[object Float64Array]",b.functionTag="[object Function]",b.int16ArrayTag="[object Int16Array]",b.int32ArrayTag="[object Int32Array]",b.int8ArrayTag="[object Int8Array]",b.mapTag="[object Map]",b.numberTag="[object Number]",b.objectTag="[object Object]",b.regexpTag="[object RegExp]",b.setTag="[object Set]",b.stringTag="[object String]",b.symbolTag="[object Symbol]",b.uint16ArrayTag="[object Uint16Array]",b.uint32ArrayTag="[object Uint32Array]",b.uint8ArrayTag="[object Uint8Array]",b.uint8ClampedArrayTag="[object Uint8ClampedArray]"},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28382:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.flatten=function(a,b=1){let c=[],d=Math.floor(b),e=(a,b)=>{for(let f=0;f<a.length;f++){let g=a[f];Array.isArray(g)&&b<d?e(g,b+1):c.push(g)}};return e(a,0),c}},29021:a=>{"use strict";a.exports=require("fs")},29243:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(91428);b.isArguments=function(a){return null!==a&&"object"==typeof a&&"[object Arguments]"===d.getTag(a)}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29632:(a,b,c)=>{"use strict";a.exports=c(97668)},29808:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>sz});var d={};c.r(d),c.d(d,{scaleBand:()=>dk,scaleDiverging:()=>function a(){var b=ez(gO()(eh));return b.copy=function(){return gL(b,a())},de.apply(b,arguments)},scaleDivergingLog:()=>function a(){var b=eH(gO()).domain([.1,1,10]);return b.copy=function(){return gL(b,a()).base(b.base())},de.apply(b,arguments)},scaleDivergingPow:()=>gP,scaleDivergingSqrt:()=>gQ,scaleDivergingSymlog:()=>function a(){var b=eK(gO());return b.copy=function(){return gL(b,a()).constant(b.constant())},de.apply(b,arguments)},scaleIdentity:()=>function a(b){var c;function d(a){return null==a||isNaN(a*=1)?c:a}return d.invert=d,d.domain=d.range=function(a){return arguments.length?(b=Array.from(a,ef),d):b.slice()},d.unknown=function(a){return arguments.length?(c=a,d):c},d.copy=function(){return a(b).unknown(c)},b=arguments.length?Array.from(b,ef):[0,1],ez(d)},scaleImplicit:()=>di,scaleLinear:()=>function a(){var b=en();return b.copy=function(){return el(b,a())},dd.apply(b,arguments),ez(b)},scaleLog:()=>function a(){let b=eH(em()).domain([1,10]);return b.copy=()=>el(b,a()).base(b.base()),dd.apply(b,arguments),b},scaleOrdinal:()=>dj,scalePoint:()=>dl,scalePow:()=>eP,scaleQuantile:()=>function a(){var b,c=[],d=[],e=[];function f(){var a=0,b=Math.max(1,d.length);for(e=Array(b-1);++a<b;)e[a-1]=function(a,b,c=dy){if(!(!(d=a.length)||isNaN(b*=1))){if(b<=0||d<2)return+c(a[0],0,a);if(b>=1)return+c(a[d-1],d-1,a);var d,e=(d-1)*b,f=Math.floor(e),g=+c(a[f],f,a);return g+(c(a[f+1],f+1,a)-g)*(e-f)}}(c,a/b);return g}function g(a){return null==a||isNaN(a*=1)?b:d[dA(e,a)]}return g.invertExtent=function(a){var b=d.indexOf(a);return b<0?[NaN,NaN]:[b>0?e[b-1]:c[0],b<e.length?e[b]:c[c.length-1]]},g.domain=function(a){if(!arguments.length)return c.slice();for(let b of(c=[],a))null==b||isNaN(b*=1)||c.push(b);return c.sort(du),f()},g.range=function(a){return arguments.length?(d=Array.from(a),f()):d.slice()},g.unknown=function(a){return arguments.length?(b=a,g):b},g.quantiles=function(){return e.slice()},g.copy=function(){return a().domain(c).range(d).unknown(b)},dd.apply(g,arguments)},scaleQuantize:()=>function a(){var b,c=0,d=1,e=1,f=[.5],g=[0,1];function h(a){return null!=a&&a<=a?g[dA(f,a,0,e)]:b}function i(){var a=-1;for(f=Array(e);++a<e;)f[a]=((a+1)*d-(a-e)*c)/(e+1);return h}return h.domain=function(a){return arguments.length?([c,d]=a,c*=1,d*=1,i()):[c,d]},h.range=function(a){return arguments.length?(e=(g=Array.from(a)).length-1,i()):g.slice()},h.invertExtent=function(a){var b=g.indexOf(a);return b<0?[NaN,NaN]:b<1?[c,f[0]]:b>=e?[f[e-1],d]:[f[b-1],f[b]]},h.unknown=function(a){return arguments.length&&(b=a),h},h.thresholds=function(){return f.slice()},h.copy=function(){return a().domain([c,d]).range(g).unknown(b)},dd.apply(ez(h),arguments)},scaleRadial:()=>function a(){var b,c=en(),d=[0,1],e=!1;function f(a){var d,f=Math.sign(d=c(a))*Math.sqrt(Math.abs(d));return isNaN(f)?b:e?Math.round(f):f}return f.invert=function(a){return c.invert(eR(a))},f.domain=function(a){return arguments.length?(c.domain(a),f):c.domain()},f.range=function(a){return arguments.length?(c.range((d=Array.from(a,ef)).map(eR)),f):d.slice()},f.rangeRound=function(a){return f.range(a).round(!0)},f.round=function(a){return arguments.length?(e=!!a,f):e},f.clamp=function(a){return arguments.length?(c.clamp(a),f):c.clamp()},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a(c.domain(),d).round(e).clamp(c.clamp()).unknown(b)},dd.apply(f,arguments),ez(f)},scaleSequential:()=>function a(){var b=ez(gK()(eh));return b.copy=function(){return gL(b,a())},de.apply(b,arguments)},scaleSequentialLog:()=>function a(){var b=eH(gK()).domain([1,10]);return b.copy=function(){return gL(b,a()).base(b.base())},de.apply(b,arguments)},scaleSequentialPow:()=>gM,scaleSequentialQuantile:()=>function a(){var b=[],c=eh;function d(a){if(null!=a&&!isNaN(a*=1))return c((dA(b,a,1)-1)/(b.length-1))}return d.domain=function(a){if(!arguments.length)return b.slice();for(let c of(b=[],a))null==c||isNaN(c*=1)||b.push(c);return b.sort(du),d},d.interpolator=function(a){return arguments.length?(c=a,d):c},d.range=function(){return b.map((a,d)=>c(d/(b.length-1)))},d.quantiles=function(a){return Array.from({length:a+1},(c,d)=>(function(a,b,c){if(!(!(d=(a=Float64Array.from(function*(a,b){if(void 0===b)for(let b of a)null!=b&&(b*=1)>=b&&(yield b);else{let c=-1;for(let d of a)null!=(d=b(d,++c,a))&&(d*=1)>=d&&(yield d)}}(a,void 0))).length)||isNaN(b*=1))){if(b<=0||d<2)return eT(a);if(b>=1)return eS(a);var d,e=(d-1)*b,f=Math.floor(e),g=eS((function a(b,c,d=0,e=1/0,f){if(c=Math.floor(c),d=Math.floor(Math.max(0,d)),e=Math.floor(Math.min(b.length-1,e)),!(d<=c&&c<=e))return b;for(f=void 0===f?eU:function(a=du){if(a===du)return eU;if("function"!=typeof a)throw TypeError("compare is not a function");return(b,c)=>{let d=a(b,c);return d||0===d?d:(0===a(c,c))-(0===a(b,b))}}(f);e>d;){if(e-d>600){let g=e-d+1,h=c-d+1,i=Math.log(g),j=.5*Math.exp(2*i/3),k=.5*Math.sqrt(i*j*(g-j)/g)*(h-g/2<0?-1:1),l=Math.max(d,Math.floor(c-h*j/g+k)),m=Math.min(e,Math.floor(c+(g-h)*j/g+k));a(b,c,l,m,f)}let g=b[c],h=d,i=e;for(eV(b,d,c),f(b[e],g)>0&&eV(b,d,e);h<i;){for(eV(b,h,i),++h,--i;0>f(b[h],g);)++h;for(;f(b[i],g)>0;)--i}0===f(b[d],g)?eV(b,d,i):eV(b,++i,e),i<=c&&(d=i+1),c<=i&&(e=i-1)}return b})(a,f).subarray(0,f+1));return g+(eT(a.subarray(f+1))-g)*(e-f)}})(b,d/a))},d.copy=function(){return a(c).domain(b)},de.apply(d,arguments)},scaleSequentialSqrt:()=>gN,scaleSequentialSymlog:()=>function a(){var b=eK(gK());return b.copy=function(){return gL(b,a()).constant(b.constant())},de.apply(b,arguments)},scaleSqrt:()=>eQ,scaleSymlog:()=>function a(){var b=eK(em());return b.copy=function(){return el(b,a()).constant(b.constant())},dd.apply(b,arguments)},scaleThreshold:()=>function a(){var b,c=[.5],d=[0,1],e=1;function f(a){return null!=a&&a<=a?d[dA(c,a,0,e)]:b}return f.domain=function(a){return arguments.length?(e=Math.min((c=Array.from(a)).length,d.length-1),f):c.slice()},f.range=function(a){return arguments.length?(d=Array.from(a),e=Math.min(c.length,d.length-1),f):d.slice()},f.invertExtent=function(a){var b=d.indexOf(a);return[c[b-1],c[b]]},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a().domain(c).range(d).unknown(b)},dd.apply(f,arguments)},scaleTime:()=>gI,scaleUtc:()=>gJ,tickFormat:()=>ey});var e=c(60687),f=c(7613),g=c(16189),h=c(43210),i=c(60149),j=c(31082),k=c(70149),l=c(55296),m=c(61022),n=c(87061),o=c(45994),p=c(4780),q=c(49384),r=c(67766),s=c.n(r),t=c(5664),u=c.n(t),v=a=>0===a?0:a>0?1:-1,w=a=>"number"==typeof a&&a!=+a,x=a=>"string"==typeof a&&a.indexOf("%")===a.length-1,y=a=>("number"==typeof a||a instanceof Number)&&!w(a),z=a=>y(a)||"string"==typeof a,A=0,B=a=>{var b=++A;return"".concat(a||"").concat(b)},C=function(a,b){var c,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!y(a)&&"string"!=typeof a)return d;if(x(a)){if(null==b)return d;var f=a.indexOf("%");c=b*parseFloat(a.slice(0,f))/100}else c=+a;return w(c)&&(c=d),e&&null!=b&&c>b&&(c=b),c},D=a=>{if(!Array.isArray(a))return!1;for(var b=a.length,c={},d=0;d<b;d++)if(c[a[d]])return!0;else c[a[d]]=!0;return!1},E=(a,b)=>y(a)&&y(b)?c=>a+c*(b-a):()=>b;function F(a,b,c){if(a&&a.length)return a.find(a=>a&&("function"==typeof b?b(a):u()(a,b))===c)}var G=a=>null==a?a:"".concat(a.charAt(0).toUpperCase()).concat(a.slice(1)),H=function(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e]};function I(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function J(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?I(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):I(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var K=(0,h.forwardRef)((a,b)=>{var{aspect:c,initialDimension:d={width:-1,height:-1},width:e="100%",height:f="100%",minWidth:g=0,minHeight:i,maxHeight:j,children:k,debounce:l=0,id:m,className:n,onResize:o,style:p={}}=a,r=(0,h.useRef)(null),t=(0,h.useRef)();t.current=o,(0,h.useImperativeHandle)(b,()=>r.current);var[u,v]=(0,h.useState)({containerWidth:d.width,containerHeight:d.height}),w=(0,h.useCallback)((a,b)=>{v(c=>{var d=Math.round(a),e=Math.round(b);return c.containerWidth===d&&c.containerHeight===e?c:{containerWidth:d,containerHeight:e}})},[]);(0,h.useEffect)(()=>{var a=a=>{var b,{width:c,height:d}=a[0].contentRect;w(c,d),null==(b=t.current)||b.call(t,c,d)};l>0&&(a=s()(a,l,{trailing:!0,leading:!1}));var b=new ResizeObserver(a),{width:c,height:d}=r.current.getBoundingClientRect();return w(c,d),b.observe(r.current),()=>{b.disconnect()}},[w,l]);var y=(0,h.useMemo)(()=>{var{containerWidth:a,containerHeight:b}=u;if(a<0||b<0)return null;H(x(e)||x(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",e,f),H(!c||c>0,"The aspect(%s) must be greater than zero.",c);var d=x(e)?a:e,l=x(f)?b:f;return c&&c>0&&(d?l=d/c:l&&(d=l*c),j&&l>j&&(l=j)),H(d>0||l>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",d,l,e,f,g,i,c),h.Children.map(k,a=>(0,h.cloneElement)(a,{width:d,height:l,style:J({width:d,height:l},a.props.style)}))},[c,k,f,j,i,g,u,e]);return h.createElement("div",{id:m?"".concat(m):void 0,className:(0,q.$)("recharts-responsive-container",n),style:J(J({},p),{},{width:e,height:f,minWidth:g,minHeight:i,maxHeight:j}),ref:r},h.createElement("div",{style:{width:0,height:0,overflow:"visible"}},y))});function L(a){return`Minified Redux error #${a}; visit https://redux.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}var M="function"==typeof Symbol&&Symbol.observable||"@@observable",N=()=>Math.random().toString(36).substring(7).split("").join("."),O={INIT:`@@redux/INIT${N()}`,REPLACE:`@@redux/REPLACE${N()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${N()}`};function P(a){if("object"!=typeof a||null===a)return!1;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b||null===Object.getPrototypeOf(a)}function Q(a){let b,c=Object.keys(a),d={};for(let b=0;b<c.length;b++){let e=c[b];"function"==typeof a[e]&&(d[e]=a[e])}let e=Object.keys(d);try{Object.keys(d).forEach(a=>{let b=d[a];if(void 0===b(void 0,{type:O.INIT}))throw Error(L(12));if(void 0===b(void 0,{type:O.PROBE_UNKNOWN_ACTION()}))throw Error(L(13))})}catch(a){b=a}return function(a={},c){if(b)throw b;let f=!1,g={};for(let b=0;b<e.length;b++){let h=e[b],i=d[h],j=a[h],k=i(j,c);if(void 0===k)throw c&&c.type,Error(L(14));g[h]=k,f=f||k!==j}return(f=f||e.length!==Object.keys(a).length)?g:a}}function R(...a){return 0===a.length?a=>a:1===a.length?a[0]:a.reduce((a,b)=>(...c)=>a(b(...c)))}function S(a){return P(a)&&"type"in a&&"string"==typeof a.type}function T(a){return({dispatch:b,getState:c})=>d=>e=>"function"==typeof e?e(b,c,a):d(e)}var U=T(),V=Symbol.for("immer-nothing"),W=Symbol.for("immer-draftable"),X=Symbol.for("immer-state");function Y(a){throw Error(`[Immer] minified error nr: ${a}. Full error at: https://bit.ly/3cXEKWf`)}var Z=Object.getPrototypeOf;function $(a){return!!a&&!!a[X]}function _(a){return!!a&&(ab(a)||Array.isArray(a)||!!a[W]||!!a.constructor?.[W]||ag(a)||ah(a))}var aa=Object.prototype.constructor.toString();function ab(a){if(!a||"object"!=typeof a)return!1;let b=Z(a);if(null===b)return!0;let c=Object.hasOwnProperty.call(b,"constructor")&&b.constructor;return c===Object||"function"==typeof c&&Function.toString.call(c)===aa}function ac(a,b){0===ad(a)?Reflect.ownKeys(a).forEach(c=>{b(c,a[c],a)}):a.forEach((c,d)=>b(d,c,a))}function ad(a){let b=a[X];return b?b.type_:Array.isArray(a)?1:ag(a)?2:3*!!ah(a)}function ae(a,b){return 2===ad(a)?a.has(b):Object.prototype.hasOwnProperty.call(a,b)}function af(a,b,c){let d=ad(a);2===d?a.set(b,c):3===d?a.add(c):a[b]=c}function ag(a){return a instanceof Map}function ah(a){return a instanceof Set}function ai(a){return a.copy_||a.base_}function aj(a,b){if(ag(a))return new Map(a);if(ah(a))return new Set(a);if(Array.isArray(a))return Array.prototype.slice.call(a);let c=ab(a);if(!0!==b&&("class_only"!==b||c)){let b=Z(a);return null!==b&&c?{...a}:Object.assign(Object.create(b),a)}{let b=Object.getOwnPropertyDescriptors(a);delete b[X];let c=Reflect.ownKeys(b);for(let d=0;d<c.length;d++){let e=c[d],f=b[e];!1===f.writable&&(f.writable=!0,f.configurable=!0),(f.get||f.set)&&(b[e]={configurable:!0,writable:!0,enumerable:f.enumerable,value:a[e]})}return Object.create(Z(a),b)}}function ak(a,b=!1){return am(a)||$(a)||!_(a)||(ad(a)>1&&(a.set=a.add=a.clear=a.delete=al),Object.freeze(a),b&&Object.entries(a).forEach(([a,b])=>ak(b,!0))),a}function al(){Y(2)}function am(a){return Object.isFrozen(a)}var an={};function ao(a){let b=an[a];return b||Y(0,a),b}function ap(a,b){b&&(ao("Patches"),a.patches_=[],a.inversePatches_=[],a.patchListener_=b)}function aq(a){ar(a),a.drafts_.forEach(at),a.drafts_=null}function ar(a){a===gY&&(gY=a.parent_)}function as(a){return gY={drafts_:[],parent_:gY,immer_:a,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function at(a){let b=a[X];0===b.type_||1===b.type_?b.revoke_():b.revoked_=!0}function au(a,b){b.unfinalizedDrafts_=b.drafts_.length;let c=b.drafts_[0];return void 0!==a&&a!==c?(c[X].modified_&&(aq(b),Y(4)),_(a)&&(a=av(b,a),b.parent_||ax(b,a)),b.patches_&&ao("Patches").generateReplacementPatches_(c[X].base_,a,b.patches_,b.inversePatches_)):a=av(b,c,[]),aq(b),b.patches_&&b.patchListener_(b.patches_,b.inversePatches_),a!==V?a:void 0}function av(a,b,c){if(am(b))return b;let d=b[X];if(!d)return ac(b,(e,f)=>aw(a,d,b,e,f,c)),b;if(d.scope_!==a)return b;if(!d.modified_)return ax(a,d.base_,!0),d.base_;if(!d.finalized_){d.finalized_=!0,d.scope_.unfinalizedDrafts_--;let b=d.copy_,e=b,f=!1;3===d.type_&&(e=new Set(b),b.clear(),f=!0),ac(e,(e,g)=>aw(a,d,b,e,g,c,f)),ax(a,b,!1),c&&a.patches_&&ao("Patches").generatePatches_(d,c,a.patches_,a.inversePatches_)}return d.copy_}function aw(a,b,c,d,e,f,g){if($(e)){let g=av(a,e,f&&b&&3!==b.type_&&!ae(b.assigned_,d)?f.concat(d):void 0);if(af(c,d,g),!$(g))return;a.canAutoFreeze_=!1}else g&&c.add(e);if(_(e)&&!am(e)){if(!a.immer_.autoFreeze_&&a.unfinalizedDrafts_<1)return;av(a,e),(!b||!b.scope_.parent_)&&"symbol"!=typeof d&&Object.prototype.propertyIsEnumerable.call(c,d)&&ax(a,e)}}function ax(a,b,c=!1){!a.parent_&&a.immer_.autoFreeze_&&a.canAutoFreeze_&&ak(b,c)}var ay={get(a,b){if(b===X)return a;let c=ai(a);if(!ae(c,b)){var d=a,e=c,f=b;let g=aB(e,f);return g?"value"in g?g.value:g.get?.call(d.draft_):void 0}let g=c[b];return a.finalized_||!_(g)?g:g===aA(a.base_,b)?(aD(a),a.copy_[b]=aE(g,a)):g},has:(a,b)=>b in ai(a),ownKeys:a=>Reflect.ownKeys(ai(a)),set(a,b,c){let d=aB(ai(a),b);if(d?.set)return d.set.call(a.draft_,c),!0;if(!a.modified_){let d=aA(ai(a),b),e=d?.[X];if(e&&e.base_===c)return a.copy_[b]=c,a.assigned_[b]=!1,!0;if((c===d?0!==c||1/c==1/d:c!=c&&d!=d)&&(void 0!==c||ae(a.base_,b)))return!0;aD(a),aC(a)}return!!(a.copy_[b]===c&&(void 0!==c||b in a.copy_)||Number.isNaN(c)&&Number.isNaN(a.copy_[b]))||(a.copy_[b]=c,a.assigned_[b]=!0,!0)},deleteProperty:(a,b)=>(void 0!==aA(a.base_,b)||b in a.base_?(a.assigned_[b]=!1,aD(a),aC(a)):delete a.assigned_[b],a.copy_&&delete a.copy_[b],!0),getOwnPropertyDescriptor(a,b){let c=ai(a),d=Reflect.getOwnPropertyDescriptor(c,b);return d?{writable:!0,configurable:1!==a.type_||"length"!==b,enumerable:d.enumerable,value:c[b]}:d},defineProperty(){Y(11)},getPrototypeOf:a=>Z(a.base_),setPrototypeOf(){Y(12)}},az={};function aA(a,b){let c=a[X];return(c?ai(c):a)[b]}function aB(a,b){if(!(b in a))return;let c=Z(a);for(;c;){let a=Object.getOwnPropertyDescriptor(c,b);if(a)return a;c=Z(c)}}function aC(a){!a.modified_&&(a.modified_=!0,a.parent_&&aC(a.parent_))}function aD(a){a.copy_||(a.copy_=aj(a.base_,a.scope_.immer_.useStrictShallowCopy_))}function aE(a,b){let c=ag(a)?ao("MapSet").proxyMap_(a,b):ah(a)?ao("MapSet").proxySet_(a,b):function(a,b){let c=Array.isArray(a),d={type_:+!!c,scope_:b?b.scope_:gY,modified_:!1,finalized_:!1,assigned_:{},parent_:b,base_:a,draft_:null,copy_:null,revoke_:null,isManual_:!1},e=d,f=ay;c&&(e=[d],f=az);let{revoke:g,proxy:h}=Proxy.revocable(e,f);return d.draft_=h,d.revoke_=g,h}(a,b);return(b?b.scope_:gY).drafts_.push(c),c}function aF(a){return $(a)||Y(10,a),function a(b){let c;if(!_(b)||am(b))return b;let d=b[X];if(d){if(!d.modified_)return d.base_;d.finalized_=!0,c=aj(b,d.scope_.immer_.useStrictShallowCopy_)}else c=aj(b,!0);return ac(c,(b,d)=>{af(c,b,a(d))}),d&&(d.finalized_=!1),c}(a)}ac(ay,(a,b)=>{az[a]=function(){return arguments[0]=arguments[0][0],b.apply(this,arguments)}}),az.deleteProperty=function(a,b){return az.set.call(this,a,b,void 0)},az.set=function(a,b,c){return ay.set.call(this,a[0],b,c,a[0])};var aG=new class{constructor(a){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(a,b,c)=>{let d;if("function"==typeof a&&"function"!=typeof b){let c=b;b=a;let d=this;return function(a=c,...e){return d.produce(a,a=>b.call(this,a,...e))}}if("function"!=typeof b&&Y(6),void 0!==c&&"function"!=typeof c&&Y(7),_(a)){let e=as(this),f=aE(a,void 0),g=!0;try{d=b(f),g=!1}finally{g?aq(e):ar(e)}return ap(e,c),au(d,e)}if(a&&"object"==typeof a)Y(1,a);else{if(void 0===(d=b(a))&&(d=a),d===V&&(d=void 0),this.autoFreeze_&&ak(d,!0),c){let b=[],e=[];ao("Patches").generateReplacementPatches_(a,d,b,e),c(b,e)}return d}},this.produceWithPatches=(a,b)=>{let c,d;return"function"==typeof a?(b,...c)=>this.produceWithPatches(b,b=>a(b,...c)):[this.produce(a,b,(a,b)=>{c=a,d=b}),c,d]},"boolean"==typeof a?.autoFreeze&&this.setAutoFreeze(a.autoFreeze),"boolean"==typeof a?.useStrictShallowCopy&&this.setUseStrictShallowCopy(a.useStrictShallowCopy)}createDraft(a){_(a)||Y(8),$(a)&&(a=aF(a));let b=as(this),c=aE(a,void 0);return c[X].isManual_=!0,ar(b),c}finishDraft(a,b){let c=a&&a[X];c&&c.isManual_||Y(9);let{scope_:d}=c;return ap(d,b),au(void 0,d)}setAutoFreeze(a){this.autoFreeze_=a}setUseStrictShallowCopy(a){this.useStrictShallowCopy_=a}applyPatches(a,b){let c;for(c=b.length-1;c>=0;c--){let d=b[c];if(0===d.path.length&&"replace"===d.op){a=d.value;break}}c>-1&&(b=b.slice(c+1));let d=ao("Patches").applyPatches_;return $(a)?d(a,b):this.produce(a,a=>d(a,b))}},aH=aG.produce;aG.produceWithPatches.bind(aG),aG.setAutoFreeze.bind(aG),aG.setUseStrictShallowCopy.bind(aG),aG.applyPatches.bind(aG),aG.createDraft.bind(aG),aG.finishDraft.bind(aG);var aI="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?R:R.apply(null,arguments)};function aJ(a,b){function c(...d){if(b){let c=b(...d);if(!c)throw Error(bm(0));return{type:a,payload:c.payload,..."meta"in c&&{meta:c.meta},..."error"in c&&{error:c.error}}}return{type:a,payload:d[0]}}return c.toString=()=>`${a}`,c.type=a,c.match=b=>S(b)&&b.type===a,c}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var aK=class a extends Array{constructor(...b){super(...b),Object.setPrototypeOf(this,a.prototype)}static get[Symbol.species](){return a}concat(...a){return super.concat.apply(this,a)}prepend(...b){return 1===b.length&&Array.isArray(b[0])?new a(...b[0].concat(this)):new a(...b.concat(this))}};function aL(a){return _(a)?aH(a,()=>{}):a}function aM(a,b,c){return a.has(b)?a.get(b):a.set(b,c(b)).get(b)}var aN=a=>b=>{setTimeout(b,a)};function aO(a){let b,c={},d=[],e={addCase(a,b){let d="string"==typeof a?a:a.type;if(!d)throw Error(bm(28));if(d in c)throw Error(bm(29));return c[d]=b,e},addMatcher:(a,b)=>(d.push({matcher:a,reducer:b}),e),addDefaultCase:a=>(b=a,e)};return a(e),[c,d,b]}var aP=Symbol.for("rtk-slice-createasyncthunk"),aQ=(a=>(a.reducer="reducer",a.reducerWithPrepare="reducerWithPrepare",a.asyncThunk="asyncThunk",a))(aQ||{}),aR=function({creators:a}={}){let b=a?.asyncThunk?.[aP];return function(a){let c,{name:d,reducerPath:e=d}=a;if(!d)throw Error(bm(11));let f=("function"==typeof a.reducers?a.reducers(function(){function a(a,b){return{_reducerDefinitionType:"asyncThunk",payloadCreator:a,...b}}return a.withTypes=()=>a,{reducer:a=>Object.assign({[a.name]:(...b)=>a(...b)}[a.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(a,b)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:a,reducer:b}),asyncThunk:a}}()):a.reducers)||{},g=Object.keys(f),h={},i={},j={},k=[],l={addCase(a,b){let c="string"==typeof a?a:a.type;if(!c)throw Error(bm(12));if(c in i)throw Error(bm(13));return i[c]=b,l},addMatcher:(a,b)=>(k.push({matcher:a,reducer:b}),l),exposeAction:(a,b)=>(j[a]=b,l),exposeCaseReducer:(a,b)=>(h[a]=b,l)};function m(){let[b={},c=[],d]="function"==typeof a.extraReducers?aO(a.extraReducers):[a.extraReducers],e={...b,...i};return function(a,b){let c,[d,e,f]=aO(b);if("function"==typeof a)c=()=>aL(a());else{let b=aL(a);c=()=>b}function g(a=c(),b){let h=[d[b.type],...e.filter(({matcher:a})=>a(b)).map(({reducer:a})=>a)];return 0===h.filter(a=>!!a).length&&(h=[f]),h.reduce((a,c)=>{if(c)if($(a)){let d=c(a,b);return void 0===d?a:d}else{if(_(a))return aH(a,a=>c(a,b));let d=c(a,b);if(void 0===d){if(null===a)return a;throw Error("A case reducer on a non-draftable value must not return undefined")}return d}return a},a)}return g.getInitialState=c,g}(a.initialState,a=>{for(let b in e)a.addCase(b,e[b]);for(let b of k)a.addMatcher(b.matcher,b.reducer);for(let b of c)a.addMatcher(b.matcher,b.reducer);d&&a.addDefaultCase(d)})}g.forEach(c=>{let e=f[c],g={reducerName:c,type:`${d}/${c}`,createNotation:"function"==typeof a.reducers};"asyncThunk"===e._reducerDefinitionType?function({type:a,reducerName:b},c,d,e){if(!e)throw Error(bm(18));let{payloadCreator:f,fulfilled:g,pending:h,rejected:i,settled:j,options:k}=c,l=e(a,f,k);d.exposeAction(b,l),g&&d.addCase(l.fulfilled,g),h&&d.addCase(l.pending,h),i&&d.addCase(l.rejected,i),j&&d.addMatcher(l.settled,j),d.exposeCaseReducer(b,{fulfilled:g||aS,pending:h||aS,rejected:i||aS,settled:j||aS})}(g,e,l,b):function({type:a,reducerName:b,createNotation:c},d,e){let f,g;if("reducer"in d){if(c&&"reducerWithPrepare"!==d._reducerDefinitionType)throw Error(bm(17));f=d.reducer,g=d.prepare}else f=d;e.addCase(a,f).exposeCaseReducer(b,f).exposeAction(b,g?aJ(a,g):aJ(a))}(g,e,l)});let n=a=>a,o=new Map,p=new WeakMap;function q(a,b){return c||(c=m()),c(a,b)}function r(){return c||(c=m()),c.getInitialState()}function s(b,c=!1){function d(a){let e=a[b];return void 0===e&&c&&(e=aM(p,d,r)),e}function e(b=n){let d=aM(o,c,()=>new WeakMap);return aM(d,b,()=>{let d={};for(let[e,f]of Object.entries(a.selectors??{}))d[e]=function(a,b,c,d){function e(f,...g){let h=b(f);return void 0===h&&d&&(h=c()),a(h,...g)}return e.unwrapped=a,e}(f,b,()=>aM(p,b,r),c);return d})}return{reducerPath:b,getSelectors:e,get selectors(){return e(d)},selectSlice:d}}let t={name:d,reducer:q,actions:j,caseReducers:h,getInitialState:r,...s(e),injectInto(a,{reducerPath:b,...c}={}){let d=b??e;return a.inject({reducerPath:d,reducer:q},c),{...t,...s(d,!0)}}};return t}}();function aS(){}var aT="listener",aU="completed",aV="cancelled",aW=`task-${aV}`,aX=`task-${aU}`,aY=`${aT}-${aV}`,aZ=`${aT}-${aU}`,a$=class{constructor(a){this.code=a,this.message=`task ${aV} (reason: ${a})`}name="TaskAbortError";message},a_=(a,b)=>{if("function"!=typeof a)throw TypeError(bm(32))},a0=()=>{},a1=(a,b=a0)=>(a.catch(b),a),a2=(a,b)=>(a.addEventListener("abort",b,{once:!0}),()=>a.removeEventListener("abort",b)),a3=(a,b)=>{let c=a.signal;c.aborted||("reason"in c||Object.defineProperty(c,"reason",{enumerable:!0,value:b,configurable:!0,writable:!0}),a.abort(b))},a4=a=>{if(a.aborted){let{reason:b}=a;throw new a$(b)}};function a5(a,b){let c=a0;return new Promise((d,e)=>{let f=()=>e(new a$(a.reason));if(a.aborted)return void f();c=a2(a,f),b.finally(()=>c()).then(d,e)}).finally(()=>{c=a0})}var a6=async(a,b)=>{try{await Promise.resolve();let b=await a();return{status:"ok",value:b}}catch(a){return{status:a instanceof a$?"cancelled":"rejected",error:a}}finally{b?.()}},a7=a=>b=>a1(a5(a,b).then(b=>(a4(a),b))),a8=a=>{let b=a7(a);return a=>b(new Promise(b=>setTimeout(b,a)))},{assign:a9}=Object,ba={},bb="listenerMiddleware",bc=a=>{let{type:b,actionCreator:c,matcher:d,predicate:e,effect:f}=a;if(b)e=aJ(b).match;else if(c)b=c.type,e=c.match;else if(d)e=d;else if(e);else throw Error(bm(21));return a_(f,"options.listener"),{predicate:e,type:b,effect:f}},bd=a9(a=>{let{type:b,predicate:c,effect:d}=bc(a);return{id:((a=21)=>{let b="",c=a;for(;c--;)b+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return b})(),effect:d,type:b,predicate:c,pending:new Set,unsubscribe:()=>{throw Error(bm(22))}}},{withTypes:()=>bd}),be=(a,b)=>{let{type:c,effect:d,predicate:e}=bc(b);return Array.from(a.values()).find(a=>("string"==typeof c?a.type===c:a.predicate===e)&&a.effect===d)},bf=a=>{a.pending.forEach(a=>{a3(a,aY)})},bg=(a,b,c)=>{try{a(b,c)}catch(a){setTimeout(()=>{throw a},0)}},bh=a9(aJ(`${bb}/add`),{withTypes:()=>bh}),bi=aJ(`${bb}/removeAll`),bj=a9(aJ(`${bb}/remove`),{withTypes:()=>bj}),bk=(...a)=>{console.error(`${bb}/error`,...a)},bl=(a={})=>{let b=new Map,{extra:c,onError:d=bk}=a;a_(d,"onError");let e=a=>(a=>(a.unsubscribe=()=>b.delete(a.id),b.set(a.id,a),b=>{a.unsubscribe(),b?.cancelActive&&bf(a)}))(be(b,a)??bd(a));a9(e,{withTypes:()=>e});let f=a=>{let c=be(b,a);return c&&(c.unsubscribe(),a.cancelActive&&bf(c)),!!c};a9(f,{withTypes:()=>f});let g=async(a,f,g,h)=>{let i=new AbortController,j=((a,b)=>{let c=async(c,d)=>{a4(b);let e=()=>{},f=[new Promise((b,d)=>{let f=a({predicate:c,effect:(a,c)=>{c.unsubscribe(),b([a,c.getState(),c.getOriginalState()])}});e=()=>{f(),d()}})];null!=d&&f.push(new Promise(a=>setTimeout(a,d,null)));try{let a=await a5(b,Promise.race(f));return a4(b),a}finally{e()}};return(a,b)=>a1(c(a,b))})(e,i.signal),k=[];try{a.pending.add(i),await Promise.resolve(a.effect(f,a9({},g,{getOriginalState:h,condition:(a,b)=>j(a,b).then(Boolean),take:j,delay:a8(i.signal),pause:a7(i.signal),extra:c,signal:i.signal,fork:((a,b)=>(c,d)=>{a_(c,"taskExecutor");let e=new AbortController;a2(a,()=>a3(e,a.reason));let f=a6(async()=>{a4(a),a4(e.signal);let b=await c({pause:a7(e.signal),delay:a8(e.signal),signal:e.signal});return a4(e.signal),b},()=>a3(e,aX));return d?.autoJoin&&b.push(f.catch(a0)),{result:a7(a)(f),cancel(){a3(e,aW)}}})(i.signal,k),unsubscribe:a.unsubscribe,subscribe:()=>{b.set(a.id,a)},cancelActiveListeners:()=>{a.pending.forEach((a,b,c)=>{a!==i&&(a3(a,aY),c.delete(a))})},cancel:()=>{a3(i,aY),a.pending.delete(i)},throwIfCancelled:()=>{a4(i.signal)}})))}catch(a){a instanceof a$||bg(d,a,{raisedBy:"effect"})}finally{await Promise.all(k),a3(i,aZ),a.pending.delete(i)}},h=(a=>()=>{a.forEach(bf),a.clear()})(b);return{middleware:a=>c=>i=>{let j;if(!S(i))return c(i);if(bh.match(i))return e(i.payload);if(bi.match(i))return void h();if(bj.match(i))return f(i.payload);let k=a.getState(),l=()=>{if(k===ba)throw Error(bm(23));return k};try{if(j=c(i),b.size>0){let c=a.getState();for(let e of Array.from(b.values())){let b=!1;try{b=e.predicate(i,c,k)}catch(a){b=!1,bg(d,a,{raisedBy:"predicate"})}b&&g(e,i,a,l)}}}finally{k=ba}return j},startListening:e,stopListening:f,clearListeners:h}};function bm(a){return`Minified Redux Toolkit error #${a}; visit https://redux-toolkit.js.org/Errors?code=${a} for the full message or use the non-minified dev environment for full errors. `}function bn(a,b){if(b){var c=Number.parseInt(b,10);if(!w(c))return null==a?void 0:a[c]}}Symbol.for("rtk-state-proxy-original");var bo=aR({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:a=>{null==a.eventEmitter&&(a.eventEmitter=Symbol("rechartsEventEmitter"))}}}),bp=bo.reducer,{createEventEmitter:bq}=bo.actions;c(6895);var br={notify(){},get:()=>[]},bs="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,bt="undefined"!=typeof navigator&&"ReactNative"===navigator.product,bu=bs||bt?h.useLayoutEffect:h.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var bv=Symbol.for("react-redux-context"),bw="undefined"!=typeof globalThis?globalThis:{},bx=function(){if(!h.createContext)return{};let a=bw[bv]??=new Map,b=a.get(h.createContext);return b||(b=h.createContext(null),a.set(h.createContext,b)),b}(),by=function(a){let{children:b,context:c,serverState:d,store:e}=a,f=h.useMemo(()=>{let a=function(a,b){let c,d=br,e=0,f=!1;function g(){j.onStateChange&&j.onStateChange()}function h(){if(e++,!c){let b,e;c=a.subscribe(g),b=null,e=null,d={clear(){b=null,e=null},notify(){let a=b;for(;a;)a.callback(),a=a.next},get(){let a=[],c=b;for(;c;)a.push(c),c=c.next;return a},subscribe(a){let c=!0,d=e={callback:a,next:null,prev:e};return d.prev?d.prev.next=d:b=d,function(){c&&null!==b&&(c=!1,d.next?d.next.prev=d.prev:e=d.prev,d.prev?d.prev.next=d.next:b=d.next)}}}}}function i(){e--,c&&0===e&&(c(),c=void 0,d.clear(),d=br)}let j={addNestedSub:function(a){h();let b=d.subscribe(a),c=!1;return()=>{c||(c=!0,b(),i())}},notifyNestedSubs:function(){d.notify()},handleChangeWrapper:g,isSubscribed:function(){return f},trySubscribe:function(){f||(f=!0,h())},tryUnsubscribe:function(){f&&(f=!1,i())},getListeners:()=>d};return j}(e);return{store:e,subscription:a,getServerState:d?()=>d:void 0}},[e,d]),g=h.useMemo(()=>e.getState(),[e]);return bu(()=>{let{subscription:a}=f;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),g!==e.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[f,g]),h.createElement((c||bx).Provider,{value:f},b)},bz={active:!1,index:null,dataKey:void 0,coordinate:void 0},bA=aR({name:"tooltip",initialState:{itemInteraction:{click:bz,hover:bz},axisInteraction:{click:bz,hover:bz},keyboardInteraction:bz,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(a,b){a.tooltipItemPayloads.push(b.payload)},removeTooltipEntrySettings(a,b){var c=aF(a).tooltipItemPayloads.indexOf(b.payload);c>-1&&a.tooltipItemPayloads.splice(c,1)},setTooltipSettingsState(a,b){a.settings=b.payload},setActiveMouseOverItemIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.itemInteraction.hover.active=!0,a.itemInteraction.hover.index=b.payload.activeIndex,a.itemInteraction.hover.dataKey=b.payload.activeDataKey,a.itemInteraction.hover.coordinate=b.payload.activeCoordinate},mouseLeaveChart(a){a.itemInteraction.hover.active=!1,a.axisInteraction.hover.active=!1},mouseLeaveItem(a){a.itemInteraction.hover.active=!1},setActiveClickItemIndex(a,b){a.syncInteraction.active=!1,a.itemInteraction.click.active=!0,a.keyboardInteraction.active=!1,a.itemInteraction.click.index=b.payload.activeIndex,a.itemInteraction.click.dataKey=b.payload.activeDataKey,a.itemInteraction.click.coordinate=b.payload.activeCoordinate},setMouseOverAxisIndex(a,b){a.syncInteraction.active=!1,a.axisInteraction.hover.active=!0,a.keyboardInteraction.active=!1,a.axisInteraction.hover.index=b.payload.activeIndex,a.axisInteraction.hover.dataKey=b.payload.activeDataKey,a.axisInteraction.hover.coordinate=b.payload.activeCoordinate},setMouseClickAxisIndex(a,b){a.syncInteraction.active=!1,a.keyboardInteraction.active=!1,a.axisInteraction.click.active=!0,a.axisInteraction.click.index=b.payload.activeIndex,a.axisInteraction.click.dataKey=b.payload.activeDataKey,a.axisInteraction.click.coordinate=b.payload.activeCoordinate},setSyncInteraction(a,b){a.syncInteraction=b.payload},setKeyboardInteraction(a,b){a.keyboardInteraction.active=b.payload.active,a.keyboardInteraction.index=b.payload.activeIndex,a.keyboardInteraction.coordinate=b.payload.activeCoordinate,a.keyboardInteraction.dataKey=b.payload.activeDataKey}}}),{addTooltipEntrySettings:bB,removeTooltipEntrySettings:bC,setTooltipSettingsState:bD,setActiveMouseOverItemIndex:bE,mouseLeaveItem:bF,mouseLeaveChart:bG,setActiveClickItemIndex:bH,setMouseOverAxisIndex:bI,setMouseClickAxisIndex:bJ,setSyncInteraction:bK,setKeyboardInteraction:bL}=bA.actions,bM=bA.reducer,bN=aR({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(a,b){if(a.chartData=b.payload,null==b.payload){a.dataStartIndex=0,a.dataEndIndex=0;return}b.payload.length>0&&a.dataEndIndex!==b.payload.length-1&&(a.dataEndIndex=b.payload.length-1)},setComputedData(a,b){a.computedData=b.payload},setDataStartEndIndexes(a,b){var{startIndex:c,endIndex:d}=b.payload;null!=c&&(a.dataStartIndex=c),null!=d&&(a.dataEndIndex=d)}}}),{setChartData:bO,setDataStartEndIndexes:bP,setComputedData:bQ}=bN.actions,bR=bN.reducer,bS=aR({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(a,b){a.layoutType=b.payload},setChartSize(a,b){a.width=b.payload.width,a.height=b.payload.height},setMargin(a,b){a.margin.top=b.payload.top,a.margin.right=b.payload.right,a.margin.bottom=b.payload.bottom,a.margin.left=b.payload.left},setScale(a,b){a.scale=b.payload}}}),{setMargin:bT,setLayout:bU,setChartSize:bV,setScale:bW}=bS.actions,bX=bS.reducer,bY=a=>Array.isArray(a)?a:[a],bZ=0,b$=class{revision=bZ;_value;_lastValue;_isEqual=b_;constructor(a,b=b_){this._value=this._lastValue=a,this._isEqual=b}get value(){return this._value}set value(a){this.value!==a&&(this._value=a,this.revision=++bZ)}};function b_(a,b){return a===b}function b0(a){return a instanceof b$||console.warn("Not a valid cell! ",a),a.value}var b1=(a,b)=>!1;function b2(){return function(a,b=b_){return new b$(null,b)}(0,b1)}var b3=a=>{let b=a.collectionTag;null===b&&(b=a.collectionTag=b2()),b0(b)};Symbol();var b4=0,b5=Object.getPrototypeOf({}),b6=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy(this,b7);tag=b2();tags={};children={};collectionTag=null;id=b4++},b7={get:(a,b)=>(function(){let{value:c}=a,d=Reflect.get(c,b);if("symbol"==typeof b||b in b5)return d;if("object"==typeof d&&null!==d){var e;let c=a.children[b];return void 0===c&&(c=a.children[b]=Array.isArray(e=d)?new b8(e):new b6(e)),c.tag&&b0(c.tag),c.proxy}{let c=a.tags[b];return void 0===c&&((c=a.tags[b]=b2()).value=d),b0(c),d}})(),ownKeys:a=>(b3(a),Reflect.ownKeys(a.value)),getOwnPropertyDescriptor:(a,b)=>Reflect.getOwnPropertyDescriptor(a.value,b),has:(a,b)=>Reflect.has(a.value,b)},b8=class{constructor(a){this.value=a,this.value=a,this.tag.value=a}proxy=new Proxy([this],b9);tag=b2();tags={};children={};collectionTag=null;id=b4++},b9={get:([a],b)=>("length"===b&&b3(a),b7.get(a,b)),ownKeys:([a])=>b7.ownKeys(a),getOwnPropertyDescriptor:([a],b)=>b7.getOwnPropertyDescriptor(a,b),has:([a],b)=>b7.has(a,b)},ca="undefined"!=typeof WeakRef?WeakRef:class{constructor(a){this.value=a}deref(){return this.value}};function cb(){return{s:0,v:void 0,o:null,p:null}}function cc(a,b={}){let c,d=cb(),{resultEqualityCheck:e}=b,f=0;function g(){let b,g=d,{length:h}=arguments;for(let a=0;a<h;a++){let b=arguments[a];if("function"==typeof b||"object"==typeof b&&null!==b){let a=g.o;null===a&&(g.o=a=new WeakMap);let c=a.get(b);void 0===c?(g=cb(),a.set(b,g)):g=c}else{let a=g.p;null===a&&(g.p=a=new Map);let c=a.get(b);void 0===c?(g=cb(),a.set(b,g)):g=c}}let i=g;if(1===g.s)b=g.v;else if(b=a.apply(null,arguments),f++,e){let a=c?.deref?.()??c;null!=a&&e(a,b)&&(b=a,0!==f&&f--),c="object"==typeof b&&null!==b||"function"==typeof b?new ca(b):b}return i.s=1,i.v=b,b}return g.clearCache=()=>{d=cb(),g.resetResultsCount()},g.resultsCount=()=>f,g.resetResultsCount=()=>{f=0},g}var cd=function(a,...b){let c="function"==typeof a?{memoize:a,memoizeOptions:b}:a,d=(...a)=>{let b,d=0,e=0,f={},g=a.pop();"object"==typeof g&&(f=g,g=a.pop()),function(a,b=`expected a function, instead received ${typeof a}`){if("function"!=typeof a)throw TypeError(b)}(g,`createSelector expects an output function after the inputs, but received: [${typeof g}]`);let{memoize:h,memoizeOptions:i=[],argsMemoize:j=cc,argsMemoizeOptions:k=[],devModeChecks:l={}}={...c,...f},m=bY(i),n=bY(k),o=function(a){let b=Array.isArray(a[0])?a[0]:a;return!function(a,b="expected all items to be functions, instead received the following types: "){if(!a.every(a=>"function"==typeof a)){let c=a.map(a=>"function"==typeof a?`function ${a.name||"unnamed"}()`:typeof a).join(", ");throw TypeError(`${b}[${c}]`)}}(b,"createSelector expects all input-selectors to be functions, but received the following types: "),b}(a),p=h(function(){return d++,g.apply(null,arguments)},...m);return Object.assign(j(function(){e++;let a=function(a,b){let c=[],{length:d}=a;for(let e=0;e<d;e++)c.push(a[e].apply(null,b));return c}(o,arguments);return b=p.apply(null,a)},...n),{resultFunc:g,memoizedResultFunc:p,dependencies:o,dependencyRecomputations:()=>e,resetDependencyRecomputations:()=>{e=0},lastResult:()=>b,recomputations:()=>d,resetRecomputations:()=>{d=0},memoize:h,argsMemoize:j})};return Object.assign(d,{withTypes:()=>d}),d}(cc),ce=Object.assign((a,b=cd)=>{!function(a,b=`expected an object, instead received ${typeof a}`){if("object"!=typeof a)throw TypeError(b)}(a,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof a}`);let c=Object.keys(a);return b(c.map(b=>a[b]),(...a)=>a.reduce((a,b,d)=>(a[c[d]]=b,a),{}))},{withTypes:()=>ce}),cf=c(39733),cg=(0,h.createContext)(null),ch=a=>a,ci=()=>{var a=(0,h.useContext)(cg);return a?a.store.dispatch:ch},cj=()=>{},ck=()=>cj,cl=(a,b)=>a===b;function cm(a){var b=(0,h.useContext)(cg);return(0,cf.useSyncExternalStoreWithSelector)(b?b.subscription.addNestedSub:ck,b?b.store.getState:cj,b?b.store.getState:cj,b?a:cj,cl)}var cn=c(10687),co=c.n(cn),cp=a=>a.legend.settings;function cq(a,b){if((e=a.length)>1)for(var c,d,e,f=1,g=a[b[0]],h=g.length;f<e;++f)for(d=g,g=a[b[f]],c=0;c<h;++c)g[c][1]+=g[c][0]=isNaN(d[c][1])?d[c][0]:d[c][1]}function cr(a){return"object"==typeof a&&"length"in a?a:Array.from(a)}function cs(a){return function(){return a}}function ct(a){for(var b=a.length,c=Array(b);--b>=0;)c[b]=b;return c}function cu(a,b){return a[b]}function cv(a){let b=[];return b.key=a,b}function cw(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cx(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cw(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cw(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}cd([a=>a.legend.payload,cp],(a,b)=>{var{itemSorter:c}=b,d=a.flat(1);return c?co()(d,c):d}),Array.prototype.slice;var cy=Math.PI/180,cz=(a,b,c,d)=>({x:a+Math.cos(-cy*d)*c,y:b+Math.sin(-cy*d)*c}),cA=function(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(a-(c.left||0)-(c.right||0)),Math.abs(b-(c.top||0)-(c.bottom||0)))/2};function cB(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cC(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cB(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cB(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cD(a,b,c){return null==a||null==b?c:z(b)?u()(a,b,c):"function"==typeof b?b(a):c}var cE=(a,b)=>"horizontal"===a&&"xAxis"===b||"vertical"===a&&"yAxis"===b||"centric"===a&&"angleAxis"===b||"radial"===a&&"radiusAxis"===b,cF=(a,b,c,d)=>{if(d)return a.map(a=>a.coordinate);var e,f,g=a.map(a=>(a.coordinate===b&&(e=!0),a.coordinate===c&&(f=!0),a.coordinate));return e||g.push(b),f||g.push(c),g},cG=(a,b,c)=>{if(!a)return null;var{duplicateDomain:d,type:e,range:f,scale:g,realScaleType:h,isCategorical:i,categoricalDomain:j,tickCount:k,ticks:l,niceTicks:m,axisType:n}=a;if(!g)return null;var o="scaleBand"===h&&g.bandwidth?g.bandwidth()/2:2,p=(b||c)&&"category"===e&&g.bandwidth?g.bandwidth()/o:0;return(p="angleAxis"===n&&f&&f.length>=2?2*v(f[0]-f[1])*p:p,b&&(l||m))?(l||m||[]).map((a,b)=>({coordinate:g(d?d.indexOf(a):a)+p,value:a,offset:p,index:b})).filter(a=>!w(a.coordinate)):i&&j?j.map((a,b)=>({coordinate:g(a)+p,value:a,index:b,offset:p})):g.ticks&&!c&&null!=k?g.ticks(k).map((a,b)=>({coordinate:g(a)+p,value:a,offset:p,index:b})):g.domain().map((a,b)=>({coordinate:g(a)+p,value:d?d[a]:a,index:b,offset:p}))},cH={sign:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0,g=0;g<b;++g){var h=w(a[g][c][1])?a[g][c][0]:a[g][c][1];h>=0?(a[g][c][0]=e,a[g][c][1]=e+h,e=a[g][c][1]):(a[g][c][0]=f,a[g][c][1]=f+h,f=a[g][c][1])}},expand:function(a,b){if((d=a.length)>0){for(var c,d,e,f=0,g=a[0].length;f<g;++f){for(e=c=0;c<d;++c)e+=a[c][f][1]||0;if(e)for(c=0;c<d;++c)a[c][f][1]/=e}cq(a,b)}},none:cq,silhouette:function(a,b){if((c=a.length)>0){for(var c,d=0,e=a[b[0]],f=e.length;d<f;++d){for(var g=0,h=0;g<c;++g)h+=a[g][d][1]||0;e[d][1]+=e[d][0]=-h/2}cq(a,b)}},wiggle:function(a,b){if((e=a.length)>0&&(d=(c=a[b[0]]).length)>0){for(var c,d,e,f=0,g=1;g<d;++g){for(var h=0,i=0,j=0;h<e;++h){for(var k=a[b[h]],l=k[g][1]||0,m=(l-(k[g-1][1]||0))/2,n=0;n<h;++n){var o=a[b[n]];m+=(o[g][1]||0)-(o[g-1][1]||0)}i+=l,j+=m*l}c[g-1][1]+=c[g-1][0]=f,i&&(f-=j/i)}c[g-1][1]+=c[g-1][0]=f,cq(a,b)}},positive:a=>{var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0;f<b;++f){var g=w(a[f][c][1])?a[f][c][0]:a[f][c][1];g>=0?(a[f][c][0]=e,a[f][c][1]=e+g,e=a[f][c][1]):(a[f][c][0]=0,a[f][c][1]=0)}}};function cI(a){var{axis:b,ticks:c,bandSize:d,entry:e,index:f,dataKey:g}=a;if("category"===b.type){if(!b.allowDuplicatedCategory&&b.dataKey&&null!=e[b.dataKey]){var h=F(c,"value",e[b.dataKey]);if(h)return h.coordinate+d/2}return c[f]?c[f].coordinate+d/2:null}var i=cD(e,null==g?b.dataKey:g);return null==i?null:b.scale(i)}var cJ=a=>{var{axis:b,ticks:c,offset:d,bandSize:e,entry:f,index:g}=a;if("category"===b.type)return c[g]?c[g].coordinate+d:null;var h=cD(f,b.dataKey,b.scale.domain()[g]);return null==h?null:b.scale(h)-e/2+d},cK=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cL=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cM=(a,b,c)=>{if(a&&a.scale&&a.scale.bandwidth){var d=a.scale.bandwidth();if(!c||d>0)return d}if(a&&b&&b.length>=2){for(var e=co()(b,a=>a.coordinate),f=1/0,g=1,h=e.length;g<h;g++){var i=e[g],j=e[g-1];f=Math.min((i.coordinate||0)-(j.coordinate||0),f)}return f===1/0?0:f}return c?void 0:0};function cN(a){var{tooltipEntrySettings:b,dataKey:c,payload:d,value:e,name:f}=a;return cC(cC({},b),{},{dataKey:c,payload:d,value:e,name:f})}function cO(a,b){return a?String(a):"string"==typeof b?b:void 0}var cP=a=>a.layout.width,cQ=a=>a.layout.height,cR=a=>a.layout.scale,cS=a=>a.layout.margin,cT=cd(a=>a.cartesianAxis.xAxis,a=>Object.values(a)),cU=cd(a=>a.cartesianAxis.yAxis,a=>Object.values(a)),cV="data-recharts-item-index",cW="data-recharts-item-data-key";function cX(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cY(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cX(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cX(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var cZ=cd([cP,cQ,cS,a=>a.brush.height,cT,cU,cp,a=>a.legend.size],(a,b,c,d,e,f,g,h)=>{var i=f.reduce((a,b)=>{var{orientation:c}=b;if(!b.mirror&&!b.hide){var d="number"==typeof b.width?b.width:60;return cY(cY({},a),{},{[c]:a[c]+d})}return a},{left:c.left||0,right:c.right||0}),j=e.reduce((a,b)=>{var{orientation:c}=b;return b.mirror||b.hide?a:cY(cY({},a),{},{[c]:u()(a,"".concat(c))+b.height})},{top:c.top||0,bottom:c.bottom||0}),k=cY(cY({},j),i),l=k.bottom;k.bottom+=d;var m=a-(k=((a,b,c)=>{if(b&&c){var{width:d,height:e}=c,{align:f,verticalAlign:g,layout:h}=b;if(("vertical"===h||"horizontal"===h&&"middle"===g)&&"center"!==f&&y(a[f]))return cC(cC({},a),{},{[f]:a[f]+(d||0)});if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==g&&y(a[g]))return cC(cC({},a),{},{[g]:a[g]+(e||0)})}return a})(k,g,h)).left-k.right,n=b-k.top-k.bottom;return cY(cY({brushBottom:l},k),{},{width:Math.max(m,0),height:Math.max(n,0)})}),c$=cd(cZ,a=>({x:a.left,y:a.top,width:a.width,height:a.height})),c_=cd(cP,cQ,(a,b)=>({x:0,y:0,width:a,height:b})),c0=(0,h.createContext)(null),c1=()=>null!=(0,h.useContext)(c0),c2=a=>a.brush,c3=cd([c2,cZ,cS],(a,b,c)=>({height:a.height,x:y(a.x)?a.x:b.left,y:y(a.y)?a.y:b.top+b.height+b.brushBottom-((null==c?void 0:c.bottom)||0),width:y(a.width)?a.width:b.width})),c4=()=>{var a,b=c1(),c=cm(c$),d=cm(c3),e=null==(a=cm(c2))?void 0:a.padding;return b&&d&&e?{width:d.width-e.left-e.right,height:d.height-e.top-e.bottom,x:e.left,y:e.top}:c},c5={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},c6=()=>{var a;return null!=(a=cm(cZ))?a:c5},c7=()=>cm(cP),c8=()=>cm(cQ),c9=a=>a.layout.layoutType,da=()=>cm(c9),db=c(30921),dc=c.n(db);function dd(a,b){switch(arguments.length){case 0:break;case 1:this.range(a);break;default:this.range(b).domain(a)}return this}function de(a,b){switch(arguments.length){case 0:break;case 1:"function"==typeof a?this.interpolator(a):this.range(a);break;default:this.domain(a),"function"==typeof b?this.interpolator(b):this.range(b)}return this}class df extends Map{constructor(a,b=dh){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:b}}),null!=a)for(let[b,c]of a)this.set(b,c)}get(a){return super.get(dg(this,a))}has(a){return super.has(dg(this,a))}set(a,b){return super.set(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):(a.set(d,c),c)}(this,a),b)}delete(a){return super.delete(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)&&(c=a.get(d),a.delete(d)),c}(this,a))}}function dg({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):c}function dh(a){return null!==a&&"object"==typeof a?a.valueOf():a}let di=Symbol("implicit");function dj(){var a=new df,b=[],c=[],d=di;function e(e){let f=a.get(e);if(void 0===f){if(d!==di)return d;a.set(e,f=b.push(e)-1)}return c[f%c.length]}return e.domain=function(c){if(!arguments.length)return b.slice();for(let d of(b=[],a=new df,c))a.has(d)||a.set(d,b.push(d)-1);return e},e.range=function(a){return arguments.length?(c=Array.from(a),e):c.slice()},e.unknown=function(a){return arguments.length?(d=a,e):d},e.copy=function(){return dj(b,c).unknown(d)},dd.apply(e,arguments),e}function dk(){var a,b,c=dj().unknown(void 0),d=c.domain,e=c.range,f=0,g=1,h=!1,i=0,j=0,k=.5;function l(){var c=d().length,l=g<f,m=l?g:f,n=l?f:g;a=(n-m)/Math.max(1,c-i+2*j),h&&(a=Math.floor(a)),m+=(n-m-a*(c-i))*k,b=a*(1-i),h&&(m=Math.round(m),b=Math.round(b));var o=(function(a,b,c){a*=1,b*=1,c=(e=arguments.length)<2?(b=a,a=0,1):e<3?1:+c;for(var d=-1,e=0|Math.max(0,Math.ceil((b-a)/c)),f=Array(e);++d<e;)f[d]=a+d*c;return f})(c).map(function(b){return m+a*b});return e(l?o.reverse():o)}return delete c.unknown,c.domain=function(a){return arguments.length?(d(a),l()):d()},c.range=function(a){return arguments.length?([f,g]=a,f*=1,g*=1,l()):[f,g]},c.rangeRound=function(a){return[f,g]=a,f*=1,g*=1,h=!0,l()},c.bandwidth=function(){return b},c.step=function(){return a},c.round=function(a){return arguments.length?(h=!!a,l()):h},c.padding=function(a){return arguments.length?(i=Math.min(1,j=+a),l()):i},c.paddingInner=function(a){return arguments.length?(i=Math.min(1,a),l()):i},c.paddingOuter=function(a){return arguments.length?(j=+a,l()):j},c.align=function(a){return arguments.length?(k=Math.max(0,Math.min(1,a)),l()):k},c.copy=function(){return dk(d(),[f,g]).round(h).paddingInner(i).paddingOuter(j).align(k)},dd.apply(l(),arguments)}function dl(){return function a(b){var c=b.copy;return b.padding=b.paddingOuter,delete b.paddingInner,delete b.paddingOuter,b.copy=function(){return a(c())},b}(dk.apply(null,arguments).paddingInner(1))}let dm=Math.sqrt(50),dn=Math.sqrt(10),dp=Math.sqrt(2);function dq(a,b,c){let d,e,f,g=(b-a)/Math.max(0,c),h=Math.floor(Math.log10(g)),i=g/Math.pow(10,h),j=i>=dm?10:i>=dn?5:i>=dp?2:1;return(h<0?(d=Math.round(a*(f=Math.pow(10,-h)/j)),e=Math.round(b*f),d/f<a&&++d,e/f>b&&--e,f=-f):(d=Math.round(a/(f=Math.pow(10,h)*j)),e=Math.round(b/f),d*f<a&&++d,e*f>b&&--e),e<d&&.5<=c&&c<2)?dq(a,b,2*c):[d,e,f]}function dr(a,b,c){if(b*=1,a*=1,!((c*=1)>0))return[];if(a===b)return[a];let d=b<a,[e,f,g]=d?dq(b,a,c):dq(a,b,c);if(!(f>=e))return[];let h=f-e+1,i=Array(h);if(d)if(g<0)for(let a=0;a<h;++a)i[a]=-((f-a)/g);else for(let a=0;a<h;++a)i[a]=(f-a)*g;else if(g<0)for(let a=0;a<h;++a)i[a]=-((e+a)/g);else for(let a=0;a<h;++a)i[a]=(e+a)*g;return i}function ds(a,b,c){return dq(a*=1,b*=1,c*=1)[2]}function dt(a,b,c){b*=1,a*=1,c*=1;let d=b<a,e=d?ds(b,a,c):ds(a,b,c);return(d?-1:1)*(e<0?-(1/e):e)}function du(a,b){return null==a||null==b?NaN:a<b?-1:a>b?1:a>=b?0:NaN}function dv(a,b){return null==a||null==b?NaN:b<a?-1:b>a?1:b>=a?0:NaN}function dw(a){let b,c,d;function e(a,d,f=0,g=a.length){if(f<g){if(0!==b(d,d))return g;do{let b=f+g>>>1;0>c(a[b],d)?f=b+1:g=b}while(f<g)}return f}return 2!==a.length?(b=du,c=(b,c)=>du(a(b),c),d=(b,c)=>a(b)-c):(b=a===du||a===dv?a:dx,c=a,d=a),{left:e,center:function(a,b,c=0,f=a.length){let g=e(a,b,c,f-1);return g>c&&d(a[g-1],b)>-d(a[g],b)?g-1:g},right:function(a,d,e=0,f=a.length){if(e<f){if(0!==b(d,d))return f;do{let b=e+f>>>1;0>=c(a[b],d)?e=b+1:f=b}while(e<f)}return e}}}function dx(){return 0}function dy(a){return null===a?NaN:+a}let dz=dw(du),dA=dz.right;function dB(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function dC(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function dD(){}dz.left,dw(dy).center;var dE="\\s*([+-]?\\d+)\\s*",dF="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",dG="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",dH=/^#([0-9a-f]{3,8})$/,dI=RegExp(`^rgb\\(${dE},${dE},${dE}\\)$`),dJ=RegExp(`^rgb\\(${dG},${dG},${dG}\\)$`),dK=RegExp(`^rgba\\(${dE},${dE},${dE},${dF}\\)$`),dL=RegExp(`^rgba\\(${dG},${dG},${dG},${dF}\\)$`),dM=RegExp(`^hsl\\(${dF},${dG},${dG}\\)$`),dN=RegExp(`^hsla\\(${dF},${dG},${dG},${dF}\\)$`),dO={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function dP(){return this.rgb().formatHex()}function dQ(){return this.rgb().formatRgb()}function dR(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=dH.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?dS(b):3===c?new dV(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?dT(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?dT(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=dI.exec(a))?new dV(b[1],b[2],b[3],1):(b=dJ.exec(a))?new dV(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=dK.exec(a))?dT(b[1],b[2],b[3],b[4]):(b=dL.exec(a))?dT(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=dM.exec(a))?d_(b[1],b[2]/100,b[3]/100,1):(b=dN.exec(a))?d_(b[1],b[2]/100,b[3]/100,b[4]):dO.hasOwnProperty(a)?dS(dO[a]):"transparent"===a?new dV(NaN,NaN,NaN,0):null}function dS(a){return new dV(a>>16&255,a>>8&255,255&a,1)}function dT(a,b,c,d){return d<=0&&(a=b=c=NaN),new dV(a,b,c,d)}function dU(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof dD||(e=dR(e)),e)?new dV((e=e.rgb()).r,e.g,e.b,e.opacity):new dV:new dV(a,b,c,null==d?1:d)}function dV(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function dW(){return`#${d$(this.r)}${d$(this.g)}${d$(this.b)}`}function dX(){let a=dY(this.opacity);return`${1===a?"rgb(":"rgba("}${dZ(this.r)}, ${dZ(this.g)}, ${dZ(this.b)}${1===a?")":`, ${a})`}`}function dY(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function dZ(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function d$(a){return((a=dZ(a))<16?"0":"")+a.toString(16)}function d_(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new d1(a,b,c,d)}function d0(a){if(a instanceof d1)return new d1(a.h,a.s,a.l,a.opacity);if(a instanceof dD||(a=dR(a)),!a)return new d1;if(a instanceof d1)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new d1(g,h,i,a.opacity)}function d1(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function d2(a){return(a=(a||0)%360)<0?a+360:a}function d3(a){return Math.max(0,Math.min(1,a||0))}function d4(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function d5(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}dB(dD,dR,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:dP,formatHex:dP,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return d0(this).formatHsl()},formatRgb:dQ,toString:dQ}),dB(dV,dU,dC(dD,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new dV(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new dV(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new dV(dZ(this.r),dZ(this.g),dZ(this.b),dY(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:dW,formatHex:dW,formatHex8:function(){return`#${d$(this.r)}${d$(this.g)}${d$(this.b)}${d$((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:dX,toString:dX})),dB(d1,function(a,b,c,d){return 1==arguments.length?d0(a):new d1(a,b,c,null==d?1:d)},dC(dD,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new d1(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new d1(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new dV(d4(a>=240?a-240:a+120,e,d),d4(a,e,d),d4(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new d1(d2(this.h),d3(this.s),d3(this.l),dY(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=dY(this.opacity);return`${1===a?"hsl(":"hsla("}${d2(this.h)}, ${100*d3(this.s)}%, ${100*d3(this.l)}%${1===a?")":`, ${a})`}`}}));let d6=a=>()=>a;function d7(a,b){var c=b-a;return c?function(b){return a+b*c}:d6(isNaN(a)?b:a)}let d8=function a(b){var c,d=1==(c=+b)?d7:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):d6(isNaN(a)?b:a)};function e(a,b){var c=d((a=dU(a)).r,(b=dU(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=d7(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function d9(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=dU(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}function ea(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}d9(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return d5((c-d/b)*b,g,e,f,h)}}),d9(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return d5((c-d/b)*b,e,f,g,h)}});var eb=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ec=RegExp(eb.source,"g");function ed(a,b){var c,d,e=typeof b;return null==b||"boolean"===e?d6(b):("number"===e?ea:"string"===e?(d=dR(b))?(b=d,d8):function(a,b){var c,d,e,f,g,h=eb.lastIndex=ec.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=eb.exec(a))&&(f=ec.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:ea(e,f)})),h=ec.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}:b instanceof dR?d8:b instanceof Date?function(a,b){var c=new Date;return a*=1,b*=1,function(d){return c.setTime(a*(1-d)+b*d),c}}:!ArrayBuffer.isView(c=b)||c instanceof DataView?Array.isArray(b)?function(a,b){var c,d=b?b.length:0,e=a?Math.min(d,a.length):0,f=Array(e),g=Array(d);for(c=0;c<e;++c)f[c]=ed(a[c],b[c]);for(;c<d;++c)g[c]=b[c];return function(a){for(c=0;c<e;++c)g[c]=f[c](a);return g}}:"function"!=typeof b.valueOf&&"function"!=typeof b.toString||isNaN(b)?function(a,b){var c,d={},e={};for(c in(null===a||"object"!=typeof a)&&(a={}),(null===b||"object"!=typeof b)&&(b={}),b)c in a?d[c]=ed(a[c],b[c]):e[c]=b[c];return function(a){for(c in d)e[c]=d[c](a);return e}}:ea:function(a,b){b||(b=[]);var c,d=a?Math.min(b.length,a.length):0,e=b.slice();return function(f){for(c=0;c<d;++c)e[c]=a[c]*(1-f)+b[c]*f;return e}})(a,b)}function ee(a,b){return a*=1,b*=1,function(c){return Math.round(a*(1-c)+b*c)}}function ef(a){return+a}var eg=[0,1];function eh(a){return a}function ei(a,b){var c;return(b-=a*=1)?function(c){return(c-a)/b}:(c=isNaN(b)?NaN:.5,function(){return c})}function ej(a,b,c){var d=a[0],e=a[1],f=b[0],g=b[1];return e<d?(d=ei(e,d),f=c(g,f)):(d=ei(d,e),f=c(f,g)),function(a){return f(d(a))}}function ek(a,b,c){var d=Math.min(a.length,b.length)-1,e=Array(d),f=Array(d),g=-1;for(a[d]<a[0]&&(a=a.slice().reverse(),b=b.slice().reverse());++g<d;)e[g]=ei(a[g],a[g+1]),f[g]=c(b[g],b[g+1]);return function(b){var c=dA(a,b,1,d)-1;return f[c](e[c](b))}}function el(a,b){return b.domain(a.domain()).range(a.range()).interpolate(a.interpolate()).clamp(a.clamp()).unknown(a.unknown())}function em(){var a,b,c,d,e,f,g=eg,h=eg,i=ed,j=eh;function k(){var a,b,c,i=Math.min(g.length,h.length);return j!==eh&&(a=g[0],b=g[i-1],a>b&&(c=a,a=b,b=c),j=function(c){return Math.max(a,Math.min(b,c))}),d=i>2?ek:ej,e=f=null,l}function l(b){return null==b||isNaN(b*=1)?c:(e||(e=d(g.map(a),h,i)))(a(j(b)))}return l.invert=function(c){return j(b((f||(f=d(h,g.map(a),ea)))(c)))},l.domain=function(a){return arguments.length?(g=Array.from(a,ef),k()):g.slice()},l.range=function(a){return arguments.length?(h=Array.from(a),k()):h.slice()},l.rangeRound=function(a){return h=Array.from(a),i=ee,k()},l.clamp=function(a){return arguments.length?(j=!!a||eh,k()):j!==eh},l.interpolate=function(a){return arguments.length?(i=a,k()):i},l.unknown=function(a){return arguments.length?(c=a,l):c},function(c,d){return a=c,b=d,k()}}function en(){return em()(eh,eh)}var eo=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ep(a){var b;if(!(b=eo.exec(a)))throw Error("invalid format: "+a);return new eq({fill:b[1],align:b[2],sign:b[3],symbol:b[4],zero:b[5],width:b[6],comma:b[7],precision:b[8]&&b[8].slice(1),trim:b[9],type:b[10]})}function eq(a){this.fill=void 0===a.fill?" ":a.fill+"",this.align=void 0===a.align?">":a.align+"",this.sign=void 0===a.sign?"-":a.sign+"",this.symbol=void 0===a.symbol?"":a.symbol+"",this.zero=!!a.zero,this.width=void 0===a.width?void 0:+a.width,this.comma=!!a.comma,this.precision=void 0===a.precision?void 0:+a.precision,this.trim=!!a.trim,this.type=void 0===a.type?"":a.type+""}function er(a,b){if((c=(a=b?a.toExponential(b-1):a.toExponential()).indexOf("e"))<0)return null;var c,d=a.slice(0,c);return[d.length>1?d[0]+d.slice(2):d,+a.slice(c+1)]}function es(a){return(a=er(Math.abs(a)))?a[1]:NaN}function et(a,b){var c=er(a,b);if(!c)return a+"";var d=c[0],e=c[1];return e<0?"0."+Array(-e).join("0")+d:d.length>e+1?d.slice(0,e+1)+"."+d.slice(e+1):d+Array(e-d.length+2).join("0")}ep.prototype=eq.prototype,eq.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eu={"%":(a,b)=>(100*a).toFixed(b),b:a=>Math.round(a).toString(2),c:a=>a+"",d:function(a){return Math.abs(a=Math.round(a))>=1e21?a.toLocaleString("en").replace(/,/g,""):a.toString(10)},e:(a,b)=>a.toExponential(b),f:(a,b)=>a.toFixed(b),g:(a,b)=>a.toPrecision(b),o:a=>Math.round(a).toString(8),p:(a,b)=>et(100*a,b),r:et,s:function(a,b){var c=er(a,b);if(!c)return a+"";var d=c[0],e=c[1],f=e-(gZ=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,g=d.length;return f===g?d:f>g?d+Array(f-g+1).join("0"):f>0?d.slice(0,f)+"."+d.slice(f):"0."+Array(1-f).join("0")+er(a,Math.max(0,b+f-1))[0]},X:a=>Math.round(a).toString(16).toUpperCase(),x:a=>Math.round(a).toString(16)};function ev(a){return a}var ew=Array.prototype.map,ex=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function ey(a,b,c,d){var e,f,g=dt(a,b,c);switch((d=ep(null==d?",f":d)).type){case"s":var h=Math.max(Math.abs(a),Math.abs(b));return null!=d.precision||isNaN(f=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(es(h)/3)))-es(Math.abs(g))))||(d.precision=f),g0(d,h);case"":case"e":case"g":case"p":case"r":null!=d.precision||isNaN(f=Math.max(0,es(Math.abs(Math.max(Math.abs(a),Math.abs(b)))-(e=Math.abs(e=g)))-es(e))+1)||(d.precision=f-("e"===d.type));break;case"f":case"%":null!=d.precision||isNaN(f=Math.max(0,-es(Math.abs(g))))||(d.precision=f-("%"===d.type)*2)}return g_(d)}function ez(a){var b=a.domain;return a.ticks=function(a){var c=b();return dr(c[0],c[c.length-1],null==a?10:a)},a.tickFormat=function(a,c){var d=b();return ey(d[0],d[d.length-1],null==a?10:a,c)},a.nice=function(c){null==c&&(c=10);var d,e,f=b(),g=0,h=f.length-1,i=f[g],j=f[h],k=10;for(j<i&&(e=i,i=j,j=e,e=g,g=h,h=e);k-- >0;){if((e=ds(i,j,c))===d)return f[g]=i,f[h]=j,b(f);if(e>0)i=Math.floor(i/e)*e,j=Math.ceil(j/e)*e;else if(e<0)i=Math.ceil(i*e)/e,j=Math.floor(j*e)/e;else break;d=e}return a},a}function eA(a,b){a=a.slice();var c,d=0,e=a.length-1,f=a[d],g=a[e];return g<f&&(c=d,d=e,e=c,c=f,f=g,g=c),a[d]=b.floor(f),a[e]=b.ceil(g),a}function eB(a){return Math.log(a)}function eC(a){return Math.exp(a)}function eD(a){return-Math.log(-a)}function eE(a){return-Math.exp(-a)}function eF(a){return isFinite(a)?+("1e"+a):a<0?0:a}function eG(a){return(b,c)=>-a(-b,c)}function eH(a){let b,c,d=a(eB,eC),e=d.domain,f=10;function g(){var g,h;return b=(g=f)===Math.E?Math.log:10===g&&Math.log10||2===g&&Math.log2||(g=Math.log(g),a=>Math.log(a)/g),c=10===(h=f)?eF:h===Math.E?Math.exp:a=>Math.pow(h,a),e()[0]<0?(b=eG(b),c=eG(c),a(eD,eE)):a(eB,eC),d}return d.base=function(a){return arguments.length?(f=+a,g()):f},d.domain=function(a){return arguments.length?(e(a),g()):e()},d.ticks=a=>{let d,g,h=e(),i=h[0],j=h[h.length-1],k=j<i;k&&([i,j]=[j,i]);let l=b(i),m=b(j),n=null==a?10:+a,o=[];if(!(f%1)&&m-l<n){if(l=Math.floor(l),m=Math.ceil(m),i>0){for(;l<=m;++l)for(d=1;d<f;++d)if(!((g=l<0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}}else for(;l<=m;++l)for(d=f-1;d>=1;--d)if(!((g=l>0?d/c(-l):d*c(l))<i)){if(g>j)break;o.push(g)}2*o.length<n&&(o=dr(i,j,n))}else o=dr(l,m,Math.min(m-l,n)).map(c);return k?o.reverse():o},d.tickFormat=(a,e)=>{if(null==a&&(a=10),null==e&&(e=10===f?"s":","),"function"!=typeof e&&(f%1||null!=(e=ep(e)).precision||(e.trim=!0),e=g_(e)),a===1/0)return e;let g=Math.max(1,f*a/d.ticks().length);return a=>{let d=a/c(Math.round(b(a)));return d*f<f-.5&&(d*=f),d<=g?e(a):""}},d.nice=()=>e(eA(e(),{floor:a=>c(Math.floor(b(a))),ceil:a=>c(Math.ceil(b(a)))})),d}function eI(a){return function(b){return Math.sign(b)*Math.log1p(Math.abs(b/a))}}function eJ(a){return function(b){return Math.sign(b)*Math.expm1(Math.abs(b))*a}}function eK(a){var b=1,c=a(eI(1),eJ(b));return c.constant=function(c){return arguments.length?a(eI(b=+c),eJ(b)):b},ez(c)}function eL(a){return function(b){return b<0?-Math.pow(-b,a):Math.pow(b,a)}}function eM(a){return a<0?-Math.sqrt(-a):Math.sqrt(a)}function eN(a){return a<0?-a*a:a*a}function eO(a){var b=a(eh,eh),c=1;return b.exponent=function(b){return arguments.length?1==(c=+b)?a(eh,eh):.5===c?a(eM,eN):a(eL(c),eL(1/c)):c},ez(b)}function eP(){var a=eO(em());return a.copy=function(){return el(a,eP()).exponent(a.exponent())},dd.apply(a,arguments),a}function eQ(){return eP.apply(null,arguments).exponent(.5)}function eR(a){return Math.sign(a)*a*a}function eS(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c<b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c<e||void 0===c&&e>=e)&&(c=e)}return c}function eT(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c>b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c>e||void 0===c&&e>=e)&&(c=e)}return c}function eU(a,b){return(null==a||!(a>=a))-(null==b||!(b>=b))||(a<b?-1:+(a>b))}function eV(a,b,c){let d=a[b];a[b]=a[c],a[c]=d}g_=(g$=function(a){var b,c,d,e=void 0===a.grouping||void 0===a.thousands?ev:(b=ew.call(a.grouping,Number),c=a.thousands+"",function(a,d){for(var e=a.length,f=[],g=0,h=b[0],i=0;e>0&&h>0&&(i+h+1>d&&(h=Math.max(1,d-i)),f.push(a.substring(e-=h,e+h)),!((i+=h+1)>d));)h=b[g=(g+1)%b.length];return f.reverse().join(c)}),f=void 0===a.currency?"":a.currency[0]+"",g=void 0===a.currency?"":a.currency[1]+"",h=void 0===a.decimal?".":a.decimal+"",i=void 0===a.numerals?ev:(d=ew.call(a.numerals,String),function(a){return a.replace(/[0-9]/g,function(a){return d[+a]})}),j=void 0===a.percent?"%":a.percent+"",k=void 0===a.minus?"−":a.minus+"",l=void 0===a.nan?"NaN":a.nan+"";function m(a){var b=(a=ep(a)).fill,c=a.align,d=a.sign,m=a.symbol,n=a.zero,o=a.width,p=a.comma,q=a.precision,r=a.trim,s=a.type;"n"===s?(p=!0,s="g"):eu[s]||(void 0===q&&(q=12),r=!0,s="g"),(n||"0"===b&&"="===c)&&(n=!0,b="0",c="=");var t="$"===m?f:"#"===m&&/[boxX]/.test(s)?"0"+s.toLowerCase():"",u="$"===m?g:/[%p]/.test(s)?j:"",v=eu[s],w=/[defgprs%]/.test(s);function x(a){var f,g,j,m=t,x=u;if("c"===s)x=v(a)+x,a="";else{var y=(a*=1)<0||1/a<0;if(a=isNaN(a)?l:v(Math.abs(a),q),r&&(a=function(a){a:for(var b,c=a.length,d=1,e=-1;d<c;++d)switch(a[d]){case".":e=b=d;break;case"0":0===e&&(e=d),b=d;break;default:if(!+a[d])break a;e>0&&(e=0)}return e>0?a.slice(0,e)+a.slice(b+1):a}(a)),y&&0==+a&&"+"!==d&&(y=!1),m=(y?"("===d?d:k:"-"===d||"("===d?"":d)+m,x=("s"===s?ex[8+gZ/3]:"")+x+(y&&"("===d?")":""),w){for(f=-1,g=a.length;++f<g;)if(48>(j=a.charCodeAt(f))||j>57){x=(46===j?h+a.slice(f+1):a.slice(f))+x,a=a.slice(0,f);break}}}p&&!n&&(a=e(a,1/0));var z=m.length+a.length+x.length,A=z<o?Array(o-z+1).join(b):"";switch(p&&n&&(a=e(A+a,A.length?o-x.length:1/0),A=""),c){case"<":a=m+a+x+A;break;case"=":a=m+A+a+x;break;case"^":a=A.slice(0,z=A.length>>1)+m+a+x+A.slice(z);break;default:a=A+m+a+x}return i(a)}return q=void 0===q?6:/[gprs]/.test(s)?Math.max(1,Math.min(21,q)):Math.max(0,Math.min(20,q)),x.toString=function(){return a+""},x}return{format:m,formatPrefix:function(a,b){var c=m(((a=ep(a)).type="f",a)),d=3*Math.max(-8,Math.min(8,Math.floor(es(b)/3))),e=Math.pow(10,-d),f=ex[8+d/3];return function(a){return c(e*a)+f}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,g0=g$.formatPrefix;let eW=new Date,eX=new Date;function eY(a,b,c,d){function e(b){return a(b=0==arguments.length?new Date:new Date(+b)),b}return e.floor=b=>(a(b=new Date(+b)),b),e.ceil=c=>(a(c=new Date(c-1)),b(c,1),a(c),c),e.round=a=>{let b=e(a),c=e.ceil(a);return a-b<c-a?b:c},e.offset=(a,c)=>(b(a=new Date(+a),null==c?1:Math.floor(c)),a),e.range=(c,d,f)=>{let g,h=[];if(c=e.ceil(c),f=null==f?1:Math.floor(f),!(c<d)||!(f>0))return h;do h.push(g=new Date(+c)),b(c,f),a(c);while(g<c&&c<d);return h},e.filter=c=>eY(b=>{if(b>=b)for(;a(b),!c(b);)b.setTime(b-1)},(a,d)=>{if(a>=a)if(d<0)for(;++d<=0;)for(;b(a,-1),!c(a););else for(;--d>=0;)for(;b(a,1),!c(a););}),c&&(e.count=(b,d)=>(eW.setTime(+b),eX.setTime(+d),a(eW),a(eX),Math.floor(c(eW,eX))),e.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e.filter(d?b=>d(b)%a==0:b=>e.count(0,b)%a==0):e:null),e}let eZ=eY(()=>{},(a,b)=>{a.setTime(+a+b)},(a,b)=>b-a);eZ.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?eY(b=>{b.setTime(Math.floor(b/a)*a)},(b,c)=>{b.setTime(+b+c*a)},(b,c)=>(c-b)/a):eZ:null,eZ.range;let e$=eY(a=>{a.setTime(a-a.getMilliseconds())},(a,b)=>{a.setTime(+a+1e3*b)},(a,b)=>(b-a)/1e3,a=>a.getUTCSeconds());e$.range;let e_=eY(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds())},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getMinutes());e_.range;let e0=eY(a=>{a.setUTCSeconds(0,0)},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getUTCMinutes());e0.range;let e1=eY(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds()-6e4*a.getMinutes())},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getHours());e1.range;let e2=eY(a=>{a.setUTCMinutes(0,0,0)},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getUTCHours());e2.range;let e3=eY(a=>a.setHours(0,0,0,0),(a,b)=>a.setDate(a.getDate()+b),(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/864e5,a=>a.getDate()-1);e3.range;let e4=eY(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>a.getUTCDate()-1);e4.range;let e5=eY(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>Math.floor(a/864e5));function e6(a){return eY(b=>{b.setDate(b.getDate()-(b.getDay()+7-a)%7),b.setHours(0,0,0,0)},(a,b)=>{a.setDate(a.getDate()+7*b)},(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/6048e5)}e5.range;let e7=e6(0),e8=e6(1),e9=e6(2),fa=e6(3),fb=e6(4),fc=e6(5),fd=e6(6);function fe(a){return eY(b=>{b.setUTCDate(b.getUTCDate()-(b.getUTCDay()+7-a)%7),b.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+7*b)},(a,b)=>(b-a)/6048e5)}e7.range,e8.range,e9.range,fa.range,fb.range,fc.range,fd.range;let ff=fe(0),fg=fe(1),fh=fe(2),fi=fe(3),fj=fe(4),fk=fe(5),fl=fe(6);ff.range,fg.range,fh.range,fi.range,fj.range,fk.range,fl.range;let fm=eY(a=>{a.setDate(1),a.setHours(0,0,0,0)},(a,b)=>{a.setMonth(a.getMonth()+b)},(a,b)=>b.getMonth()-a.getMonth()+(b.getFullYear()-a.getFullYear())*12,a=>a.getMonth());fm.range;let fn=eY(a=>{a.setUTCDate(1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCMonth(a.getUTCMonth()+b)},(a,b)=>b.getUTCMonth()-a.getUTCMonth()+(b.getUTCFullYear()-a.getUTCFullYear())*12,a=>a.getUTCMonth());fn.range;let fo=eY(a=>{a.setMonth(0,1),a.setHours(0,0,0,0)},(a,b)=>{a.setFullYear(a.getFullYear()+b)},(a,b)=>b.getFullYear()-a.getFullYear(),a=>a.getFullYear());fo.every=a=>isFinite(a=Math.floor(a))&&a>0?eY(b=>{b.setFullYear(Math.floor(b.getFullYear()/a)*a),b.setMonth(0,1),b.setHours(0,0,0,0)},(b,c)=>{b.setFullYear(b.getFullYear()+c*a)}):null,fo.range;let fp=eY(a=>{a.setUTCMonth(0,1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCFullYear(a.getUTCFullYear()+b)},(a,b)=>b.getUTCFullYear()-a.getUTCFullYear(),a=>a.getUTCFullYear());function fq(a,b,c,d,e,f){let g=[[e$,1,1e3],[e$,5,5e3],[e$,15,15e3],[e$,30,3e4],[f,1,6e4],[f,5,3e5],[f,15,9e5],[f,30,18e5],[e,1,36e5],[e,3,108e5],[e,6,216e5],[e,12,432e5],[d,1,864e5],[d,2,1728e5],[c,1,6048e5],[b,1,2592e6],[b,3,7776e6],[a,1,31536e6]];function h(b,c,d){let e=Math.abs(c-b)/d,f=dw(([,,a])=>a).right(g,e);if(f===g.length)return a.every(dt(b/31536e6,c/31536e6,d));if(0===f)return eZ.every(Math.max(dt(b,c,d),1));let[h,i]=g[e/g[f-1][2]<g[f][2]/e?f-1:f];return h.every(i)}return[function(a,b,c){let d=b<a;d&&([a,b]=[b,a]);let e=c&&"function"==typeof c.range?c:h(a,b,c),f=e?e.range(a,+b+1):[];return d?f.reverse():f},h]}fp.every=a=>isFinite(a=Math.floor(a))&&a>0?eY(b=>{b.setUTCFullYear(Math.floor(b.getUTCFullYear()/a)*a),b.setUTCMonth(0,1),b.setUTCHours(0,0,0,0)},(b,c)=>{b.setUTCFullYear(b.getUTCFullYear()+c*a)}):null,fp.range;let[fr,fs]=fq(fp,fn,ff,e5,e2,e0),[ft,fu]=fq(fo,fm,e7,e3,e1,e_);function fv(a){if(0<=a.y&&a.y<100){var b=new Date(-1,a.m,a.d,a.H,a.M,a.S,a.L);return b.setFullYear(a.y),b}return new Date(a.y,a.m,a.d,a.H,a.M,a.S,a.L)}function fw(a){if(0<=a.y&&a.y<100){var b=new Date(Date.UTC(-1,a.m,a.d,a.H,a.M,a.S,a.L));return b.setUTCFullYear(a.y),b}return new Date(Date.UTC(a.y,a.m,a.d,a.H,a.M,a.S,a.L))}function fx(a,b,c){return{y:a,m:b,d:c,H:0,M:0,S:0,L:0}}var fy={"-":"",_:" ",0:"0"},fz=/^\s*\d+/,fA=/^%/,fB=/[\\^$*+?|[\]().{}]/g;function fC(a,b,c){var d=a<0?"-":"",e=(d?-a:a)+"",f=e.length;return d+(f<c?Array(c-f+1).join(b)+e:e)}function fD(a){return a.replace(fB,"\\$&")}function fE(a){return RegExp("^(?:"+a.map(fD).join("|")+")","i")}function fF(a){return new Map(a.map((a,b)=>[a.toLowerCase(),b]))}function fG(a,b,c){var d=fz.exec(b.slice(c,c+1));return d?(a.w=+d[0],c+d[0].length):-1}function fH(a,b,c){var d=fz.exec(b.slice(c,c+1));return d?(a.u=+d[0],c+d[0].length):-1}function fI(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.U=+d[0],c+d[0].length):-1}function fJ(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.V=+d[0],c+d[0].length):-1}function fK(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.W=+d[0],c+d[0].length):-1}function fL(a,b,c){var d=fz.exec(b.slice(c,c+4));return d?(a.y=+d[0],c+d[0].length):-1}function fM(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.y=+d[0]+(+d[0]>68?1900:2e3),c+d[0].length):-1}function fN(a,b,c){var d=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(b.slice(c,c+6));return d?(a.Z=d[1]?0:-(d[2]+(d[3]||"00")),c+d[0].length):-1}function fO(a,b,c){var d=fz.exec(b.slice(c,c+1));return d?(a.q=3*d[0]-3,c+d[0].length):-1}function fP(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.m=d[0]-1,c+d[0].length):-1}function fQ(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.d=+d[0],c+d[0].length):-1}function fR(a,b,c){var d=fz.exec(b.slice(c,c+3));return d?(a.m=0,a.d=+d[0],c+d[0].length):-1}function fS(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.H=+d[0],c+d[0].length):-1}function fT(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.M=+d[0],c+d[0].length):-1}function fU(a,b,c){var d=fz.exec(b.slice(c,c+2));return d?(a.S=+d[0],c+d[0].length):-1}function fV(a,b,c){var d=fz.exec(b.slice(c,c+3));return d?(a.L=+d[0],c+d[0].length):-1}function fW(a,b,c){var d=fz.exec(b.slice(c,c+6));return d?(a.L=Math.floor(d[0]/1e3),c+d[0].length):-1}function fX(a,b,c){var d=fA.exec(b.slice(c,c+1));return d?c+d[0].length:-1}function fY(a,b,c){var d=fz.exec(b.slice(c));return d?(a.Q=+d[0],c+d[0].length):-1}function fZ(a,b,c){var d=fz.exec(b.slice(c));return d?(a.s=+d[0],c+d[0].length):-1}function f$(a,b){return fC(a.getDate(),b,2)}function f_(a,b){return fC(a.getHours(),b,2)}function f0(a,b){return fC(a.getHours()%12||12,b,2)}function f1(a,b){return fC(1+e3.count(fo(a),a),b,3)}function f2(a,b){return fC(a.getMilliseconds(),b,3)}function f3(a,b){return f2(a,b)+"000"}function f4(a,b){return fC(a.getMonth()+1,b,2)}function f5(a,b){return fC(a.getMinutes(),b,2)}function f6(a,b){return fC(a.getSeconds(),b,2)}function f7(a){var b=a.getDay();return 0===b?7:b}function f8(a,b){return fC(e7.count(fo(a)-1,a),b,2)}function f9(a){var b=a.getDay();return b>=4||0===b?fb(a):fb.ceil(a)}function ga(a,b){return a=f9(a),fC(fb.count(fo(a),a)+(4===fo(a).getDay()),b,2)}function gb(a){return a.getDay()}function gc(a,b){return fC(e8.count(fo(a)-1,a),b,2)}function gd(a,b){return fC(a.getFullYear()%100,b,2)}function ge(a,b){return fC((a=f9(a)).getFullYear()%100,b,2)}function gf(a,b){return fC(a.getFullYear()%1e4,b,4)}function gg(a,b){var c=a.getDay();return fC((a=c>=4||0===c?fb(a):fb.ceil(a)).getFullYear()%1e4,b,4)}function gh(a){var b=a.getTimezoneOffset();return(b>0?"-":(b*=-1,"+"))+fC(b/60|0,"0",2)+fC(b%60,"0",2)}function gi(a,b){return fC(a.getUTCDate(),b,2)}function gj(a,b){return fC(a.getUTCHours(),b,2)}function gk(a,b){return fC(a.getUTCHours()%12||12,b,2)}function gl(a,b){return fC(1+e4.count(fp(a),a),b,3)}function gm(a,b){return fC(a.getUTCMilliseconds(),b,3)}function gn(a,b){return gm(a,b)+"000"}function go(a,b){return fC(a.getUTCMonth()+1,b,2)}function gp(a,b){return fC(a.getUTCMinutes(),b,2)}function gq(a,b){return fC(a.getUTCSeconds(),b,2)}function gr(a){var b=a.getUTCDay();return 0===b?7:b}function gs(a,b){return fC(ff.count(fp(a)-1,a),b,2)}function gt(a){var b=a.getUTCDay();return b>=4||0===b?fj(a):fj.ceil(a)}function gu(a,b){return a=gt(a),fC(fj.count(fp(a),a)+(4===fp(a).getUTCDay()),b,2)}function gv(a){return a.getUTCDay()}function gw(a,b){return fC(fg.count(fp(a)-1,a),b,2)}function gx(a,b){return fC(a.getUTCFullYear()%100,b,2)}function gy(a,b){return fC((a=gt(a)).getUTCFullYear()%100,b,2)}function gz(a,b){return fC(a.getUTCFullYear()%1e4,b,4)}function gA(a,b){var c=a.getUTCDay();return fC((a=c>=4||0===c?fj(a):fj.ceil(a)).getUTCFullYear()%1e4,b,4)}function gB(){return"+0000"}function gC(){return"%"}function gD(a){return+a}function gE(a){return Math.floor(a/1e3)}function gF(a){return new Date(a)}function gG(a){return a instanceof Date?+a:+new Date(+a)}function gH(a,b,c,d,e,f,g,h,i,j){var k=en(),l=k.invert,m=k.domain,n=j(".%L"),o=j(":%S"),p=j("%I:%M"),q=j("%I %p"),r=j("%a %d"),s=j("%b %d"),t=j("%B"),u=j("%Y");function v(a){return(i(a)<a?n:h(a)<a?o:g(a)<a?p:f(a)<a?q:d(a)<a?e(a)<a?r:s:c(a)<a?t:u)(a)}return k.invert=function(a){return new Date(l(a))},k.domain=function(a){return arguments.length?m(Array.from(a,gG)):m().map(gF)},k.ticks=function(b){var c=m();return a(c[0],c[c.length-1],null==b?10:b)},k.tickFormat=function(a,b){return null==b?v:j(b)},k.nice=function(a){var c=m();return a&&"function"==typeof a.range||(a=b(c[0],c[c.length-1],null==a?10:a)),a?m(eA(c,a)):k},k.copy=function(){return el(k,gH(a,b,c,d,e,f,g,h,i,j))},k}function gI(){return dd.apply(gH(ft,fu,fo,fm,e7,e3,e1,e_,e$,g2).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function gJ(){return dd.apply(gH(fr,fs,fp,fn,ff,e4,e2,e0,e$,g3).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function gK(){var a,b,c,d,e,f=0,g=1,h=eh,i=!1;function j(b){return null==b||isNaN(b*=1)?e:h(0===c?.5:(b=(d(b)-a)*c,i?Math.max(0,Math.min(1,b)):b))}function k(a){return function(b){var c,d;return arguments.length?([c,d]=b,h=a(c,d),j):[h(0),h(1)]}}return j.domain=function(e){return arguments.length?([f,g]=e,a=d(f*=1),b=d(g*=1),c=a===b?0:1/(b-a),j):[f,g]},j.clamp=function(a){return arguments.length?(i=!!a,j):i},j.interpolator=function(a){return arguments.length?(h=a,j):h},j.range=k(ed),j.rangeRound=k(ee),j.unknown=function(a){return arguments.length?(e=a,j):e},function(e){return d=e,a=e(f),b=e(g),c=a===b?0:1/(b-a),j}}function gL(a,b){return b.domain(a.domain()).interpolator(a.interpolator()).clamp(a.clamp()).unknown(a.unknown())}function gM(){var a=eO(gK());return a.copy=function(){return gL(a,gM()).exponent(a.exponent())},de.apply(a,arguments)}function gN(){return gM.apply(null,arguments).exponent(.5)}function gO(){var a,b,c,d,e,f,g,h=0,i=.5,j=1,k=1,l=eh,m=!1;function n(a){return isNaN(a*=1)?g:(a=.5+((a=+f(a))-b)*(k*a<k*b?d:e),l(m?Math.max(0,Math.min(1,a)):a))}function o(a){return function(b){var c,d,e;return arguments.length?([c,d,e]=b,l=function(a,b){void 0===b&&(b=a,a=ed);for(var c=0,d=b.length-1,e=b[0],f=Array(d<0?0:d);c<d;)f[c]=a(e,e=b[++c]);return function(a){var b=Math.max(0,Math.min(d-1,Math.floor(a*=d)));return f[b](a-b)}}(a,[c,d,e]),n):[l(0),l(.5),l(1)]}}return n.domain=function(g){return arguments.length?([h,i,j]=g,a=f(h*=1),b=f(i*=1),c=f(j*=1),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n):[h,i,j]},n.clamp=function(a){return arguments.length?(m=!!a,n):m},n.interpolator=function(a){return arguments.length?(l=a,n):l},n.range=o(ed),n.rangeRound=o(ee),n.unknown=function(a){return arguments.length?(g=a,n):g},function(g){return f=g,a=g(h),b=g(i),c=g(j),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n}}function gP(){var a=eO(gO());return a.copy=function(){return gL(a,gP()).exponent(a.exponent())},de.apply(a,arguments)}function gQ(){return gP.apply(null,arguments).exponent(.5)}g2=(g1=function(a){var b=a.dateTime,c=a.date,d=a.time,e=a.periods,f=a.days,g=a.shortDays,h=a.months,i=a.shortMonths,j=fE(e),k=fF(e),l=fE(f),m=fF(f),n=fE(g),o=fF(g),p=fE(h),q=fF(h),r=fE(i),s=fF(i),t={a:function(a){return g[a.getDay()]},A:function(a){return f[a.getDay()]},b:function(a){return i[a.getMonth()]},B:function(a){return h[a.getMonth()]},c:null,d:f$,e:f$,f:f3,g:ge,G:gg,H:f_,I:f0,j:f1,L:f2,m:f4,M:f5,p:function(a){return e[+(a.getHours()>=12)]},q:function(a){return 1+~~(a.getMonth()/3)},Q:gD,s:gE,S:f6,u:f7,U:f8,V:ga,w:gb,W:gc,x:null,X:null,y:gd,Y:gf,Z:gh,"%":gC},u={a:function(a){return g[a.getUTCDay()]},A:function(a){return f[a.getUTCDay()]},b:function(a){return i[a.getUTCMonth()]},B:function(a){return h[a.getUTCMonth()]},c:null,d:gi,e:gi,f:gn,g:gy,G:gA,H:gj,I:gk,j:gl,L:gm,m:go,M:gp,p:function(a){return e[+(a.getUTCHours()>=12)]},q:function(a){return 1+~~(a.getUTCMonth()/3)},Q:gD,s:gE,S:gq,u:gr,U:gs,V:gu,w:gv,W:gw,x:null,X:null,y:gx,Y:gz,Z:gB,"%":gC},v={a:function(a,b,c){var d=n.exec(b.slice(c));return d?(a.w=o.get(d[0].toLowerCase()),c+d[0].length):-1},A:function(a,b,c){var d=l.exec(b.slice(c));return d?(a.w=m.get(d[0].toLowerCase()),c+d[0].length):-1},b:function(a,b,c){var d=r.exec(b.slice(c));return d?(a.m=s.get(d[0].toLowerCase()),c+d[0].length):-1},B:function(a,b,c){var d=p.exec(b.slice(c));return d?(a.m=q.get(d[0].toLowerCase()),c+d[0].length):-1},c:function(a,c,d){return y(a,b,c,d)},d:fQ,e:fQ,f:fW,g:fM,G:fL,H:fS,I:fS,j:fR,L:fV,m:fP,M:fT,p:function(a,b,c){var d=j.exec(b.slice(c));return d?(a.p=k.get(d[0].toLowerCase()),c+d[0].length):-1},q:fO,Q:fY,s:fZ,S:fU,u:fH,U:fI,V:fJ,w:fG,W:fK,x:function(a,b,d){return y(a,c,b,d)},X:function(a,b,c){return y(a,d,b,c)},y:fM,Y:fL,Z:fN,"%":fX};function w(a,b){return function(c){var d,e,f,g=[],h=-1,i=0,j=a.length;for(c instanceof Date||(c=new Date(+c));++h<j;)37===a.charCodeAt(h)&&(g.push(a.slice(i,h)),null!=(e=fy[d=a.charAt(++h)])?d=a.charAt(++h):e="e"===d?" ":"0",(f=b[d])&&(d=f(c,e)),g.push(d),i=h+1);return g.push(a.slice(i,h)),g.join("")}}function x(a,b){return function(c){var d,e,f=fx(1900,void 0,1);if(y(f,a,c+="",0)!=c.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(1e3*f.s+("L"in f?f.L:0));if(!b||"Z"in f||(f.Z=0),"p"in f&&(f.H=f.H%12+12*f.p),void 0===f.m&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(d=(e=(d=fw(fx(f.y,0,1))).getUTCDay())>4||0===e?fg.ceil(d):fg(d),d=e4.offset(d,(f.V-1)*7),f.y=d.getUTCFullYear(),f.m=d.getUTCMonth(),f.d=d.getUTCDate()+(f.w+6)%7):(d=(e=(d=fv(fx(f.y,0,1))).getDay())>4||0===e?e8.ceil(d):e8(d),d=e3.offset(d,(f.V-1)*7),f.y=d.getFullYear(),f.m=d.getMonth(),f.d=d.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:+("W"in f)),e="Z"in f?fw(fx(f.y,0,1)).getUTCDay():fv(fx(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+7*f.W-(e+5)%7:f.w+7*f.U-(e+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,fw(f)):fv(f)}}function y(a,b,c,d){for(var e,f,g=0,h=b.length,i=c.length;g<h;){if(d>=i)return -1;if(37===(e=b.charCodeAt(g++))){if(!(f=v[(e=b.charAt(g++))in fy?b.charAt(g++):e])||(d=f(a,c,d))<0)return -1}else if(e!=c.charCodeAt(d++))return -1}return d}return t.x=w(c,t),t.X=w(d,t),t.c=w(b,t),u.x=w(c,u),u.X=w(d,u),u.c=w(b,u),{format:function(a){var b=w(a+="",t);return b.toString=function(){return a},b},parse:function(a){var b=x(a+="",!1);return b.toString=function(){return a},b},utcFormat:function(a){var b=w(a+="",u);return b.toString=function(){return a},b},utcParse:function(a){var b=x(a+="",!0);return b.toString=function(){return a},b}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,g1.parse,g3=g1.utcFormat,g1.utcParse;var gR=a=>a.chartData,gS=cd([gR],a=>{var b=null!=a.chartData?a.chartData.length-1:0;return{chartData:a.chartData,computedData:a.computedData,dataEndIndex:b,dataStartIndex:0}}),gT=(a,b,c,d)=>d?gS(a):gR(a);function gU(a){return Number.isFinite(a)}function gV(a){return"number"==typeof a&&a>0&&Number.isFinite(a)}function gW(a){if(Array.isArray(a)&&2===a.length){var[b,c]=a;if(gU(b)&&gU(c))return!0}return!1}function gX(a,b,c){return c?a:[Math.min(a[0],b[0]),Math.max(a[1],b[1])]}var gY,gZ,g$,g_,g0,g1,g2,g3,g4,g5,g6=!0,g7="[DecimalError] ",g8=g7+"Invalid argument: ",g9=g7+"Exponent out of range: ",ha=Math.floor,hb=Math.pow,hc=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,hd=ha(1286742750677284.5),he={};function hf(a,b){var c,d,e,f,g,h,i,j,k=a.constructor,l=k.precision;if(!a.s||!b.s)return b.s||(b=new k(a)),g6?hp(b,l):b;if(i=a.d,j=b.d,g=a.e,e=b.e,i=i.slice(),f=g-e){for(f<0?(d=i,f=-f,h=j.length):(d=j,e=g,h=i.length),f>(h=(g=Math.ceil(l/7))>h?g+1:h+1)&&(f=h,d.length=1),d.reverse();f--;)d.push(0);d.reverse()}for((h=i.length)-(f=j.length)<0&&(f=h,d=j,j=i,i=d),c=0;f;)c=(i[--f]=i[f]+j[f]+c)/1e7|0,i[f]%=1e7;for(c&&(i.unshift(c),++e),h=i.length;0==i[--h];)i.pop();return b.d=i,b.e=e,g6?hp(b,l):b}function hg(a,b,c){if(a!==~~a||a<b||a>c)throw Error(g8+a)}function hh(a){var b,c,d,e=a.length-1,f="",g=a[0];if(e>0){for(f+=g,b=1;b<e;b++)(c=7-(d=a[b]+"").length)&&(f+=hm(c)),f+=d;(c=7-(d=(g=a[b])+"").length)&&(f+=hm(c))}else if(0===g)return"0";for(;g%10==0;)g/=10;return f+g}he.absoluteValue=he.abs=function(){var a=new this.constructor(this);return a.s&&(a.s=1),a},he.comparedTo=he.cmp=function(a){var b,c,d,e;if(a=new this.constructor(a),this.s!==a.s)return this.s||-a.s;if(this.e!==a.e)return this.e>a.e^this.s<0?1:-1;for(b=0,c=(d=this.d.length)<(e=a.d.length)?d:e;b<c;++b)if(this.d[b]!==a.d[b])return this.d[b]>a.d[b]^this.s<0?1:-1;return d===e?0:d>e^this.s<0?1:-1},he.decimalPlaces=he.dp=function(){var a=this.d.length-1,b=(a-this.e)*7;if(a=this.d[a])for(;a%10==0;a/=10)b--;return b<0?0:b},he.dividedBy=he.div=function(a){return hi(this,new this.constructor(a))},he.dividedToIntegerBy=he.idiv=function(a){var b=this.constructor;return hp(hi(this,new b(a),0,1),b.precision)},he.equals=he.eq=function(a){return!this.cmp(a)},he.exponent=function(){return hk(this)},he.greaterThan=he.gt=function(a){return this.cmp(a)>0},he.greaterThanOrEqualTo=he.gte=function(a){return this.cmp(a)>=0},he.isInteger=he.isint=function(){return this.e>this.d.length-2},he.isNegative=he.isneg=function(){return this.s<0},he.isPositive=he.ispos=function(){return this.s>0},he.isZero=function(){return 0===this.s},he.lessThan=he.lt=function(a){return 0>this.cmp(a)},he.lessThanOrEqualTo=he.lte=function(a){return 1>this.cmp(a)},he.logarithm=he.log=function(a){var b,c=this.constructor,d=c.precision,e=d+5;if(void 0===a)a=new c(10);else if((a=new c(a)).s<1||a.eq(g5))throw Error(g7+"NaN");if(this.s<1)throw Error(g7+(this.s?"NaN":"-Infinity"));return this.eq(g5)?new c(0):(g6=!1,b=hi(hn(this,e),hn(a,e),e),g6=!0,hp(b,d))},he.minus=he.sub=function(a){return a=new this.constructor(a),this.s==a.s?hq(this,a):hf(this,(a.s=-a.s,a))},he.modulo=he.mod=function(a){var b,c=this.constructor,d=c.precision;if(!(a=new c(a)).s)throw Error(g7+"NaN");return this.s?(g6=!1,b=hi(this,a,0,1).times(a),g6=!0,this.minus(b)):hp(new c(this),d)},he.naturalExponential=he.exp=function(){return hj(this)},he.naturalLogarithm=he.ln=function(){return hn(this)},he.negated=he.neg=function(){var a=new this.constructor(this);return a.s=-a.s||0,a},he.plus=he.add=function(a){return a=new this.constructor(a),this.s==a.s?hf(this,a):hq(this,(a.s=-a.s,a))},he.precision=he.sd=function(a){var b,c,d;if(void 0!==a&&!!a!==a&&1!==a&&0!==a)throw Error(g8+a);if(b=hk(this)+1,c=7*(d=this.d.length-1)+1,d=this.d[d]){for(;d%10==0;d/=10)c--;for(d=this.d[0];d>=10;d/=10)c++}return a&&b>c?b:c},he.squareRoot=he.sqrt=function(){var a,b,c,d,e,f,g,h=this.constructor;if(this.s<1){if(!this.s)return new h(0);throw Error(g7+"NaN")}for(a=hk(this),g6=!1,0==(e=Math.sqrt(+this))||e==1/0?(((b=hh(this.d)).length+a)%2==0&&(b+="0"),e=Math.sqrt(b),a=ha((a+1)/2)-(a<0||a%2),d=new h(b=e==1/0?"5e"+a:(b=e.toExponential()).slice(0,b.indexOf("e")+1)+a)):d=new h(e.toString()),e=g=(c=h.precision)+3;;)if(d=(f=d).plus(hi(this,f,g+2)).times(.5),hh(f.d).slice(0,g)===(b=hh(d.d)).slice(0,g)){if(b=b.slice(g-3,g+1),e==g&&"4999"==b){if(hp(f,c+1,0),f.times(f).eq(this)){d=f;break}}else if("9999"!=b)break;g+=4}return g6=!0,hp(d,c)},he.times=he.mul=function(a){var b,c,d,e,f,g,h,i,j,k=this.constructor,l=this.d,m=(a=new k(a)).d;if(!this.s||!a.s)return new k(0);for(a.s*=this.s,c=this.e+a.e,(i=l.length)<(j=m.length)&&(f=l,l=m,m=f,g=i,i=j,j=g),f=[],d=g=i+j;d--;)f.push(0);for(d=j;--d>=0;){for(b=0,e=i+d;e>d;)h=f[e]+m[d]*l[e-d-1]+b,f[e--]=h%1e7|0,b=h/1e7|0;f[e]=(f[e]+b)%1e7|0}for(;!f[--g];)f.pop();return b?++c:f.shift(),a.d=f,a.e=c,g6?hp(a,k.precision):a},he.toDecimalPlaces=he.todp=function(a,b){var c=this,d=c.constructor;return(c=new d(c),void 0===a)?c:(hg(a,0,1e9),void 0===b?b=d.rounding:hg(b,0,8),hp(c,a+hk(c)+1,b))},he.toExponential=function(a,b){var c,d=this,e=d.constructor;return void 0===a?c=hr(d,!0):(hg(a,0,1e9),void 0===b?b=e.rounding:hg(b,0,8),c=hr(d=hp(new e(d),a+1,b),!0,a+1)),c},he.toFixed=function(a,b){var c,d,e=this.constructor;return void 0===a?hr(this):(hg(a,0,1e9),void 0===b?b=e.rounding:hg(b,0,8),c=hr((d=hp(new e(this),a+hk(this)+1,b)).abs(),!1,a+hk(d)+1),this.isneg()&&!this.isZero()?"-"+c:c)},he.toInteger=he.toint=function(){var a=this.constructor;return hp(new a(this),hk(this)+1,a.rounding)},he.toNumber=function(){return+this},he.toPower=he.pow=function(a){var b,c,d,e,f,g,h=this,i=h.constructor,j=+(a=new i(a));if(!a.s)return new i(g5);if(!(h=new i(h)).s){if(a.s<1)throw Error(g7+"Infinity");return h}if(h.eq(g5))return h;if(d=i.precision,a.eq(g5))return hp(h,d);if(g=(b=a.e)>=(c=a.d.length-1),f=h.s,g){if((c=j<0?-j:j)<=0x1fffffffffffff){for(e=new i(g5),b=Math.ceil(d/7+4),g6=!1;c%2&&hs((e=e.times(h)).d,b),0!==(c=ha(c/2));)hs((h=h.times(h)).d,b);return g6=!0,a.s<0?new i(g5).div(e):hp(e,d)}}else if(f<0)throw Error(g7+"NaN");return f=f<0&&1&a.d[Math.max(b,c)]?-1:1,h.s=1,g6=!1,e=a.times(hn(h,d+12)),g6=!0,(e=hj(e)).s=f,e},he.toPrecision=function(a,b){var c,d,e=this,f=e.constructor;return void 0===a?(c=hk(e),d=hr(e,c<=f.toExpNeg||c>=f.toExpPos)):(hg(a,1,1e9),void 0===b?b=f.rounding:hg(b,0,8),c=hk(e=hp(new f(e),a,b)),d=hr(e,a<=c||c<=f.toExpNeg,a)),d},he.toSignificantDigits=he.tosd=function(a,b){var c=this.constructor;return void 0===a?(a=c.precision,b=c.rounding):(hg(a,1,1e9),void 0===b?b=c.rounding:hg(b,0,8)),hp(new c(this),a,b)},he.toString=he.valueOf=he.val=he.toJSON=he[Symbol.for("nodejs.util.inspect.custom")]=function(){var a=hk(this),b=this.constructor;return hr(this,a<=b.toExpNeg||a>=b.toExpPos)};var hi=function(){function a(a,b){var c,d=0,e=a.length;for(a=a.slice();e--;)c=a[e]*b+d,a[e]=c%1e7|0,d=c/1e7|0;return d&&a.unshift(d),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c){for(var d=0;c--;)a[c]-=d,d=+(a[c]<b[c]),a[c]=1e7*d+a[c]-b[c];for(;!a[0]&&a.length>1;)a.shift()}return function(d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=d.constructor,A=d.s==e.s?1:-1,B=d.d,C=e.d;if(!d.s)return new z(d);if(!e.s)throw Error(g7+"Division by zero");for(j=0,i=d.e-e.e,x=C.length,v=B.length,o=(n=new z(A)).d=[];C[j]==(B[j]||0);)++j;if(C[j]>(B[j]||0)&&--i,(s=null==f?f=z.precision:g?f+(hk(d)-hk(e))+1:f)<0)return new z(0);if(s=s/7+2|0,j=0,1==x)for(k=0,C=C[0],s++;(j<v||k)&&s--;j++)t=1e7*k+(B[j]||0),o[j]=t/C|0,k=t%C|0;else{for((k=1e7/(C[0]+1)|0)>1&&(C=a(C,k),B=a(B,k),x=C.length,v=B.length),u=x,q=(p=B.slice(0,x)).length;q<x;)p[q++]=0;(y=C.slice()).unshift(0),w=C[0],C[1]>=1e7/2&&++w;do k=0,(h=b(C,p,x,q))<0?(r=p[0],x!=q&&(r=1e7*r+(p[1]||0)),(k=r/w|0)>1?(k>=1e7&&(k=1e7-1),m=(l=a(C,k)).length,q=p.length,1==(h=b(l,p,m,q))&&(k--,c(l,x<m?y:C,m))):(0==k&&(h=k=1),l=C.slice()),(m=l.length)<q&&l.unshift(0),c(p,l,q),-1==h&&(q=p.length,(h=b(C,p,x,q))<1&&(k++,c(p,x<q?y:C,q))),q=p.length):0===h&&(k++,p=[0]),o[j++]=k,h&&p[0]?p[q++]=B[u]||0:(p=[B[u]],q=1);while((u++<v||void 0!==p[0])&&s--)}return o[0]||o.shift(),n.e=i,hp(n,g?f+hk(n)+1:f)}}();function hj(a,b){var c,d,e,f,g,h=0,i=0,j=a.constructor,k=j.precision;if(hk(a)>16)throw Error(g9+hk(a));if(!a.s)return new j(g5);for(null==b?(g6=!1,g=k):g=b,f=new j(.03125);a.abs().gte(.1);)a=a.times(f),i+=5;for(g+=Math.log(hb(2,i))/Math.LN10*2+5|0,c=d=e=new j(g5),j.precision=g;;){if(d=hp(d.times(a),g),c=c.times(++h),hh((f=e.plus(hi(d,c,g))).d).slice(0,g)===hh(e.d).slice(0,g)){for(;i--;)e=hp(e.times(e),g);return j.precision=k,null==b?(g6=!0,hp(e,k)):e}e=f}}function hk(a){for(var b=7*a.e,c=a.d[0];c>=10;c/=10)b++;return b}function hl(a,b,c){if(b>a.LN10.sd())throw g6=!0,c&&(a.precision=c),Error(g7+"LN10 precision limit exceeded");return hp(new a(a.LN10),b)}function hm(a){for(var b="";a--;)b+="0";return b}function hn(a,b){var c,d,e,f,g,h,i,j,k,l=1,m=a,n=m.d,o=m.constructor,p=o.precision;if(m.s<1)throw Error(g7+(m.s?"NaN":"-Infinity"));if(m.eq(g5))return new o(0);if(null==b?(g6=!1,j=p):j=b,m.eq(10))return null==b&&(g6=!0),hl(o,j);if(o.precision=j+=10,d=(c=hh(n)).charAt(0),!(15e14>Math.abs(f=hk(m))))return i=hl(o,j+2,p).times(f+""),m=hn(new o(d+"."+c.slice(1)),j-10).plus(i),o.precision=p,null==b?(g6=!0,hp(m,p)):m;for(;d<7&&1!=d||1==d&&c.charAt(1)>3;)d=(c=hh((m=m.times(a)).d)).charAt(0),l++;for(f=hk(m),d>1?(m=new o("0."+c),f++):m=new o(d+"."+c.slice(1)),h=g=m=hi(m.minus(g5),m.plus(g5),j),k=hp(m.times(m),j),e=3;;){if(g=hp(g.times(k),j),hh((i=h.plus(hi(g,new o(e),j))).d).slice(0,j)===hh(h.d).slice(0,j))return h=h.times(2),0!==f&&(h=h.plus(hl(o,j+2,p).times(f+""))),h=hi(h,new o(l),j),o.precision=p,null==b?(g6=!0,hp(h,p)):h;h=i,e+=2}}function ho(a,b){var c,d,e;for((c=b.indexOf("."))>-1&&(b=b.replace(".","")),(d=b.search(/e/i))>0?(c<0&&(c=d),c+=+b.slice(d+1),b=b.substring(0,d)):c<0&&(c=b.length),d=0;48===b.charCodeAt(d);)++d;for(e=b.length;48===b.charCodeAt(e-1);)--e;if(b=b.slice(d,e)){if(e-=d,a.e=ha((c=c-d-1)/7),a.d=[],d=(c+1)%7,c<0&&(d+=7),d<e){for(d&&a.d.push(+b.slice(0,d)),e-=7;d<e;)a.d.push(+b.slice(d,d+=7));d=7-(b=b.slice(d)).length}else d-=e;for(;d--;)b+="0";if(a.d.push(+b),g6&&(a.e>hd||a.e<-hd))throw Error(g9+c)}else a.s=0,a.e=0,a.d=[0];return a}function hp(a,b,c){var d,e,f,g,h,i,j,k,l=a.d;for(g=1,f=l[0];f>=10;f/=10)g++;if((d=b-g)<0)d+=7,e=b,j=l[k=0];else{if((k=Math.ceil((d+1)/7))>=(f=l.length))return a;for(g=1,j=f=l[k];f>=10;f/=10)g++;d%=7,e=d-7+g}if(void 0!==c&&(h=j/(f=hb(10,g-e-1))%10|0,i=b<0||void 0!==l[k+1]||j%f,i=c<4?(h||i)&&(0==c||c==(a.s<0?3:2)):h>5||5==h&&(4==c||i||6==c&&(d>0?e>0?j/hb(10,g-e):0:l[k-1])%10&1||c==(a.s<0?8:7))),b<1||!l[0])return i?(f=hk(a),l.length=1,b=b-f-1,l[0]=hb(10,(7-b%7)%7),a.e=ha(-b/7)||0):(l.length=1,l[0]=a.e=a.s=0),a;if(0==d?(l.length=k,f=1,k--):(l.length=k+1,f=hb(10,7-d),l[k]=e>0?(j/hb(10,g-e)%hb(10,e)|0)*f:0),i)for(;;)if(0==k){1e7==(l[0]+=f)&&(l[0]=1,++a.e);break}else{if(l[k]+=f,1e7!=l[k])break;l[k--]=0,f=1}for(d=l.length;0===l[--d];)l.pop();if(g6&&(a.e>hd||a.e<-hd))throw Error(g9+hk(a));return a}function hq(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.constructor,n=m.precision;if(!a.s||!b.s)return b.s?b.s=-b.s:b=new m(a),g6?hp(b,n):b;if(i=a.d,l=b.d,d=b.e,j=a.e,i=i.slice(),g=j-d){for((k=g<0)?(c=i,g=-g,h=l.length):(c=l,d=j,h=i.length),g>(e=Math.max(Math.ceil(n/7),h)+2)&&(g=e,c.length=1),c.reverse(),e=g;e--;)c.push(0);c.reverse()}else{for((k=(e=i.length)<(h=l.length))&&(h=e),e=0;e<h;e++)if(i[e]!=l[e]){k=i[e]<l[e];break}g=0}for(k&&(c=i,i=l,l=c,b.s=-b.s),h=i.length,e=l.length-h;e>0;--e)i[h++]=0;for(e=l.length;e>g;){if(i[--e]<l[e]){for(f=e;f&&0===i[--f];)i[f]=1e7-1;--i[f],i[e]+=1e7}i[e]-=l[e]}for(;0===i[--h];)i.pop();for(;0===i[0];i.shift())--d;return i[0]?(b.d=i,b.e=d,g6?hp(b,n):b):new m(0)}function hr(a,b,c){var d,e=hk(a),f=hh(a.d),g=f.length;return b?(c&&(d=c-g)>0?f=f.charAt(0)+"."+f.slice(1)+hm(d):g>1&&(f=f.charAt(0)+"."+f.slice(1)),f=f+(e<0?"e":"e+")+e):e<0?(f="0."+hm(-e-1)+f,c&&(d=c-g)>0&&(f+=hm(d))):e>=g?(f+=hm(e+1-g),c&&(d=c-e-1)>0&&(f=f+"."+hm(d))):((d=e+1)<g&&(f=f.slice(0,d)+"."+f.slice(d)),c&&(d=c-g)>0&&(e+1===g&&(f+="."),f+=hm(d))),a.s<0?"-"+f:f}function hs(a,b){if(a.length>b)return a.length=b,!0}function ht(a){if(!a||"object"!=typeof a)throw Error(g7+"Object expected");var b,c,d,e=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(b=0;b<e.length;b+=3)if(void 0!==(d=a[c=e[b]]))if(ha(d)===d&&d>=e[b+1]&&d<=e[b+2])this[c]=d;else throw Error(g8+c+": "+d);if(void 0!==(d=a[c="LN10"]))if(d==Math.LN10)this[c]=new this(d);else throw Error(g8+c+": "+d);return this}var g4=function a(b){var c,d,e;function f(a){if(!(this instanceof f))return new f(a);if(this.constructor=f,a instanceof f){this.s=a.s,this.e=a.e,this.d=(a=a.d)?a.slice():a;return}if("number"==typeof a){if(0*a!=0)throw Error(g8+a);if(a>0)this.s=1;else if(a<0)a=-a,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(a===~~a&&a<1e7){this.e=0,this.d=[a];return}return ho(this,a.toString())}if("string"!=typeof a)throw Error(g8+a);if(45===a.charCodeAt(0)?(a=a.slice(1),this.s=-1):this.s=1,hc.test(a))ho(this,a);else throw Error(g8+a)}if(f.prototype=he,f.ROUND_UP=0,f.ROUND_DOWN=1,f.ROUND_CEIL=2,f.ROUND_FLOOR=3,f.ROUND_HALF_UP=4,f.ROUND_HALF_DOWN=5,f.ROUND_HALF_EVEN=6,f.ROUND_HALF_CEIL=7,f.ROUND_HALF_FLOOR=8,f.clone=a,f.config=f.set=ht,void 0===b&&(b={}),b)for(c=0,e=["precision","rounding","toExpNeg","toExpPos","LN10"];c<e.length;)b.hasOwnProperty(d=e[c++])||(b[d]=this[d]);return f.config(b),f}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});g5=new g4(1);let hu=g4;var hv=a=>a,hw={},hx=a=>function b(){let c;return 0==arguments.length||1==arguments.length&&(c=arguments.length<=0?void 0:arguments[0],c===hw)?b:a(...arguments)},hy=(a,b)=>1===a?b:hx(function(){for(var c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];var f=d.filter(a=>a!==hw).length;return f>=a?b(...d):hy(a-f,hx(function(){for(var a=arguments.length,c=Array(a),e=0;e<a;e++)c[e]=arguments[e];return b(...d.map(a=>a===hw?c.shift():a),...c)}))}),hz=a=>hy(a.length,a),hA=(a,b)=>{for(var c=[],d=a;d<b;++d)c[d-a]=d;return c},hB=hz((a,b)=>Array.isArray(b)?b.map(a):Object.keys(b).map(a=>b[a]).map(a)),hC=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];if(!b.length)return hv;var d=b.reverse(),e=d[0],f=d.slice(1);return function(){return f.reduce((a,b)=>b(a),e(...arguments))}},hD=a=>Array.isArray(a)?a.reverse():a.split("").reverse().join(""),hE=a=>{var b=null,c=null;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return b&&e.every((a,c)=>{var d;return a===(null==(d=b)?void 0:d[c])})?c:(b=e,c=a(...e))}};function hF(a){return 0===a?1:Math.floor(new hu(a).abs().log(10).toNumber())+1}function hG(a,b,c){for(var d=new hu(a),e=0,f=[];d.lt(b)&&e<1e5;)f.push(d.toNumber()),d=d.add(c),e++;return f}hz((a,b,c)=>{var d=+a;return d+c*(b-d)}),hz((a,b,c)=>{var d=b-a;return(c-a)/(d=d||1/0)}),hz((a,b,c)=>{var d=b-a;return Math.max(0,Math.min(1,(c-a)/(d=d||1/0)))});var hH=a=>{var[b,c]=a,[d,e]=[b,c];return b>c&&([d,e]=[c,b]),[d,e]},hI=(a,b,c)=>{if(a.lte(0))return new hu(0);var d=hF(a.toNumber()),e=new hu(10).pow(d),f=a.div(e),g=1!==d?.05:.1,h=new hu(Math.ceil(f.div(g).toNumber())).add(c).mul(g).mul(e);return new hu(b?h.toNumber():Math.ceil(h.toNumber()))},hJ=function(a,b,c,d){var e,f=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((b-a)/(c-1)))return{step:new hu(0),tickMin:new hu(0),tickMax:new hu(0)};var g=hI(new hu(b).sub(a).div(c-1),d,f),h=Math.ceil((e=a<=0&&b>=0?new hu(0):(e=new hu(a).add(b).div(2)).sub(new hu(e).mod(g))).sub(a).div(g).toNumber()),i=Math.ceil(new hu(b).sub(e).div(g).toNumber()),j=h+i+1;return j>c?hJ(a,b,c,d,f+1):(j<c&&(i=b>0?i+(c-j):i,h=b>0?h:h+(c-j)),{step:g,tickMin:e.sub(new hu(h).mul(g)),tickMax:e.add(new hu(i).mul(g))})},hK=hE(function(a){var[b,c]=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],f=Math.max(d,2),[g,h]=hH([b,c]);if(g===-1/0||h===1/0){var i=h===1/0?[g,...hA(0,d-1).map(()=>1/0)]:[...hA(0,d-1).map(()=>-1/0),h];return b>c?hD(i):i}if(g===h){var j=new hu(1),k=new hu(g);if(!k.isint()&&e){var l=Math.abs(g);l<1?(j=new hu(10).pow(hF(g)-1),k=new hu(Math.floor(k.div(j).toNumber())).mul(j)):l>1&&(k=new hu(Math.floor(g)))}else 0===g?k=new hu(Math.floor((d-1)/2)):e||(k=new hu(Math.floor(g)));var m=Math.floor((d-1)/2);return hC(hB(a=>k.add(new hu(a-m).mul(j)).toNumber()),hA)(0,d)}var{step:n,tickMin:o,tickMax:p}=hJ(g,h,f,e,0),q=hG(o,p.add(new hu(.1).mul(n)),n);return b>c?hD(q):q}),hL=hE(function(a,b){var[c,d]=a,e=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[f,g]=hH([c,d]);if(f===-1/0||g===1/0)return[c,d];if(f===g)return[f];var h=Math.max(b,2),i=hI(new hu(g).sub(f).div(h-1),e,0),j=[...hG(new hu(f),new hu(g),i),g];return!1===e&&(j=j.map(a=>Math.round(a))),c>d?hD(j):j}),hM=a=>a.rootProps.maxBarSize,hN=a=>a.rootProps.barCategoryGap,hO=a=>a.rootProps.stackOffset,hP=a=>a.options.chartName,hQ=a=>a.rootProps.syncId,hR=a=>a.rootProps.syncMethod,hS=a=>a.options.eventEmitter,hT={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},hU={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},hV=(a,b)=>{if(a&&b)return null!=a&&a.reversed?[b[1],b[0]]:b},hW={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:hT.angleAxisId,includeHidden:!1,name:void 0,reversed:hT.reversed,scale:hT.scale,tick:hT.tick,tickCount:void 0,ticks:void 0,type:hT.type,unit:void 0},hX={allowDataOverflow:hU.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hU.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hU.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hU.scale,tick:hU.tick,tickCount:hU.tickCount,ticks:void 0,type:hU.type,unit:void 0},hY={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:hT.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hT.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hT.scale,tick:hT.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},hZ={allowDataOverflow:hU.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:hU.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:hU.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:hU.scale,tick:hU.tick,tickCount:hU.tickCount,ticks:void 0,type:"category",unit:void 0},h$=(a,b)=>null!=a.polarAxis.angleAxis[b]?a.polarAxis.angleAxis[b]:"radial"===a.layout.layoutType?hY:hW,h_=(a,b)=>null!=a.polarAxis.radiusAxis[b]?a.polarAxis.radiusAxis[b]:"radial"===a.layout.layoutType?hZ:hX,h0=a=>a.polarOptions,h1=cd([cP,cQ,cZ],cA),h2=cd([h0,h1],(a,b)=>{if(null!=a)return C(a.innerRadius,b,0)}),h3=cd([h0,h1],(a,b)=>{if(null!=a)return C(a.outerRadius,b,.8*b)}),h4=cd([h0],a=>{if(null==a)return[0,0];var{startAngle:b,endAngle:c}=a;return[b,c]});cd([h$,h4],hV);var h5=cd([h1,h2,h3],(a,b,c)=>{if(null!=a&&null!=b&&null!=c)return[b,c]});cd([h_,h5],hV);var h6=cd([c9,h0,h2,h3,cP,cQ],(a,b,c,d,e,f)=>{if(("centric"===a||"radial"===a)&&null!=b&&null!=c&&null!=d){var{cx:g,cy:h,startAngle:i,endAngle:j}=b;return{cx:C(g,e,e/2),cy:C(h,f,f/2),innerRadius:c,outerRadius:d,startAngle:i,endAngle:j,clockWise:!1}}}),h7=(a,b)=>b,h8=(a,b,c)=>c;function h9(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ia(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?h9(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):h9(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var ib=[0,"auto"],ic={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},id=(a,b)=>{var c=a.cartesianAxis.xAxis[b];return null==c?ic:c},ie={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ib,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},ig=(a,b)=>{var c=a.cartesianAxis.yAxis[b];return null==c?ie:c},ih={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ii=(a,b)=>{var c=a.cartesianAxis.zAxis[b];return null==c?ih:c},ij=(a,b,c)=>{switch(b){case"xAxis":return id(a,c);case"yAxis":return ig(a,c);case"zAxis":return ii(a,c);case"angleAxis":return h$(a,c);case"radiusAxis":return h_(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},ik=(a,b,c)=>{switch(b){case"xAxis":return id(a,c);case"yAxis":return ig(a,c);case"angleAxis":return h$(a,c);case"radiusAxis":return h_(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},il=a=>a.graphicalItems.countOfBars>0;function im(a,b){return c=>{switch(a){case"xAxis":return"xAxisId"in c&&c.xAxisId===b;case"yAxis":return"yAxisId"in c&&c.yAxisId===b;case"zAxis":return"zAxisId"in c&&c.zAxisId===b;case"angleAxis":return"angleAxisId"in c&&c.angleAxisId===b;case"radiusAxis":return"radiusAxisId"in c&&c.radiusAxisId===b;default:return!1}}}var io=a=>a.graphicalItems.cartesianItems,ip=cd([h7,h8],im),iq=(a,b,c)=>a.filter(c).filter(a=>(null==b?void 0:b.includeHidden)===!0||!a.hide),ir=cd([io,ij,ip],iq),is=a=>a.filter(a=>void 0===a.stackId),it=cd([ir],is),iu=a=>a.map(a=>a.data).filter(Boolean).flat(1),iv=cd([ir],iu),iw=(a,b)=>{var{chartData:c=[],dataStartIndex:d,dataEndIndex:e}=b;return a.length>0?a:c.slice(d,e+1)},ix=cd([iv,gT],iw),iy=(a,b,c)=>(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cD(a,b.dataKey)})):c.length>0?c.map(a=>a.dataKey).flatMap(b=>a.map(a=>({value:cD(a,b)}))):a.map(a=>({value:a})),iz=cd([ix,ij,ir],iy);function iA(a,b){switch(a){case"xAxis":return"x"===b.direction;case"yAxis":return"y"===b.direction;default:return!1}}function iB(a){return a.filter(a=>z(a)||a instanceof Date).map(Number).filter(a=>!1===w(a))}var iC=(a,b,c)=>Object.fromEntries(Object.entries(b.reduce((a,b)=>(null==b.stackId||(null==a[b.stackId]&&(a[b.stackId]=[]),a[b.stackId].push(b)),a),{})).map(b=>{var[d,e]=b;return[d,{stackedData:((a,b,c)=>{var d=cH[c];return(function(){var a=cs([]),b=ct,c=cq,d=cu;function e(e){var f,g,h=Array.from(a.apply(this,arguments),cv),i=h.length,j=-1;for(let a of e)for(f=0,++j;f<i;++f)(h[f][j]=[0,+d(a,h[f].key,j,e)]).data=a;for(f=0,g=cr(b(h));f<i;++f)h[g[f]].index=f;return c(h,g),h}return e.keys=function(b){return arguments.length?(a="function"==typeof b?b:cs(Array.from(b)),e):a},e.value=function(a){return arguments.length?(d="function"==typeof a?a:cs(+a),e):d},e.order=function(a){return arguments.length?(b=null==a?ct:"function"==typeof a?a:cs(Array.from(a)),e):b},e.offset=function(a){return arguments.length?(c=null==a?cq:a,e):c},e})().keys(b).value((a,b)=>+cD(a,b,0)).order(ct).offset(d)(a)})(a,e.map(a=>a.dataKey),c),graphicalItems:e}]})),iD=cd([ix,ir,hO],iC),iE=(a,b,c)=>{var{dataStartIndex:d,dataEndIndex:e}=b;if("zAxis"!==c){var f=((a,b,c)=>{if(null!=a)return(a=>[a[0]===1/0?0:a[0],a[1]===-1/0?0:a[1]])(Object.keys(a).reduce((d,e)=>{var{stackedData:f}=a[e],g=f.reduce((a,d)=>{var e=(a=>{var b=a.flat(2).filter(y);return[Math.min(...b),Math.max(...b)]})(d.slice(b,c+1));return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0]);return[Math.min(g[0],d[0]),Math.max(g[1],d[1])]},[1/0,-1/0]))})(a,d,e);if(null==f||0!==f[0]||0!==f[1])return f}},iF=cd([iD,gR,h7],iE),iG=(a,b,c,d)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var e,f,g=null==(e=c.errorBars)?void 0:e.filter(a=>iA(d,a)),h=cD(a,null!=(f=b.dataKey)?f:c.dataKey);return{value:h,errorDomain:function(a,b,c){return!c||"number"!=typeof b||w(b)||!c.length?[]:iB(c.flatMap(c=>{var d,e,f=cD(a,c.dataKey);if(Array.isArray(f)?[d,e]=f:d=e=f,gU(d)&&gU(e))return[b-d,b+e]}))}(a,h,g)}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cD(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]})),iH=cd(ix,ij,it,h7,iG);function iI(a){var{value:b}=a;if(z(b)||b instanceof Date)return b}var iJ=a=>{var b;if(null==a||!("domain"in a))return ib;if(null!=a.domain)return a.domain;if(null!=a.ticks){if("number"===a.type){var c=iB(a.ticks);return[Math.min(...c),Math.max(...c)]}if("category"===a.type)return a.ticks.map(String)}return null!=(b=null==a?void 0:a.domain)?b:ib},iK=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];var d=b.filter(Boolean);if(0!==d.length){var e=d.flat();return[Math.min(...e),Math.max(...e)]}},iL=a=>a.referenceElements.dots,iM=(a,b,c)=>a.filter(a=>"extendDomain"===a.ifOverflow).filter(a=>"xAxis"===b?a.xAxisId===c:a.yAxisId===c),iN=cd([iL,h7,h8],iM),iO=a=>a.referenceElements.areas,iP=cd([iO,h7,h8],iM),iQ=a=>a.referenceElements.lines,iR=cd([iQ,h7,h8],iM),iS=(a,b)=>{var c=iB(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iT=cd(iN,h7,iS),iU=(a,b)=>{var c=iB(a.flatMap(a=>["xAxis"===b?a.x1:a.y1,"xAxis"===b?a.x2:a.y2]));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iV=cd([iP,h7],iU),iW=(a,b)=>{var c=iB(a.map(a=>"xAxis"===b?a.x:a.y));if(0!==c.length)return[Math.min(...c),Math.max(...c)]},iX=cd(iR,h7,iW),iY=cd(iT,iX,iV,(a,b,c)=>iK(a,c,b)),iZ=cd([ij],iJ),i$=(a,b,c,d,e)=>{var f=function(a,b){if(b&&"function"!=typeof a&&Array.isArray(a)&&2===a.length){var c,d,[e,f]=a;if(gU(e))c=e;else if("function"==typeof e)return;if(gU(f))d=f;else if("function"==typeof f)return;var g=[c,d];if(gW(g))return g}}(b,a.allowDataOverflow);return null!=f?f:function(a,b,c){if(c||null!=b){if("function"==typeof a&&null!=b)try{var d=a(b,c);if(gW(d))return gX(d,b,c)}catch(a){}if(Array.isArray(a)&&2===a.length){var e,f,[g,h]=a;if("auto"===g)null!=b&&(e=Math.min(...b));else if(y(g))e=g;else if("function"==typeof g)try{null!=b&&(e=g(null==b?void 0:b[0]))}catch(a){}else if("string"==typeof g&&cK.test(g)){var i=cK.exec(g);if(null==i||null==b)e=void 0;else{var j=+i[1];e=b[0]-j}}else e=null==b?void 0:b[0];if("auto"===h)null!=b&&(f=Math.max(...b));else if(y(h))f=h;else if("function"==typeof h)try{null!=b&&(f=h(null==b?void 0:b[1]))}catch(a){}else if("string"==typeof h&&cL.test(h)){var k=cL.exec(h);if(null==k||null==b)f=void 0;else{var l=+k[1];f=b[1]+l}}else f=null==b?void 0:b[1];var m=[e,f];if(gW(m))return null==b?m:gX(m,b,c)}}}(b,iK(c,e,(a=>{var b=iB(a.flatMap(a=>[a.value,a.errorDomain]).flat(1));if(0!==b.length)return[Math.min(...b),Math.max(...b)]})(d)),a.allowDataOverflow)},i_=cd([ij,iZ,iF,iH,iY],i$),i0=[0,1],i1=(a,b,c,d,e,f,g)=>{if(null!=a&&null!=c&&0!==c.length){var{dataKey:h,type:i}=a,j=cE(b,f);return j&&null==h?dc()(0,c.length):"category"===i?((a,b,c)=>{var d=a.map(iI).filter(a=>null!=a);return c&&(null==b.dataKey||b.allowDuplicatedCategory&&D(d))?dc()(0,a.length):b.allowDuplicatedCategory?d:Array.from(new Set(d))})(d,a,j):"expand"===e?i0:g}},i2=cd([ij,c9,ix,iz,hO,h7,i_],i1),i3=(a,b,c,e,f)=>{if(null!=a){var{scale:g,type:h}=a;if("auto"===g)return"radial"===b&&"radiusAxis"===f?"band":"radial"===b&&"angleAxis"===f?"linear":"category"===h&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!c)?"point":"category"===h?"band":"linear";if("string"==typeof g){var i="scale".concat(G(g));return i in d?i:"point"}}},i4=cd([ij,c9,il,hP,h7],i3);function i5(a,b,c,e){if(null!=c&&null!=e){if("function"==typeof a.scale)return a.scale.copy().domain(c).range(e);var f=function(a){if(null!=a){if(a in d)return d[a]();var b="scale".concat(G(a));if(b in d)return d[b]()}}(b);if(null!=f){var g=f.domain(c).range(e);return(a=>{var b=a.domain();if(b&&!(b.length<=2)){var c=b.length,d=a.range(),e=Math.min(d[0],d[1])-1e-4,f=Math.max(d[0],d[1])+1e-4,g=a(b[0]),h=a(b[c-1]);(g<e||g>f||h<e||h>f)&&a.domain([b[0],b[c-1]])}})(g),g}}}var i6=(a,b,c)=>{var d=iJ(b);if("auto"===c||"linear"===c){if(null!=b&&b.tickCount&&Array.isArray(d)&&("auto"===d[0]||"auto"===d[1])&&gW(a))return hK(a,b.tickCount,b.allowDecimals);if(null!=b&&b.tickCount&&"number"===b.type&&gW(a))return hL(a,b.tickCount,b.allowDecimals)}},i7=cd([i2,ik,i4],i6),i8=(a,b,c,d)=>"angleAxis"!==d&&(null==a?void 0:a.type)==="number"&&gW(b)&&Array.isArray(c)&&c.length>0?[Math.min(b[0],c[0]),Math.max(b[1],c[c.length-1])]:b,i9=cd([ij,i2,i7,h7],i8),ja=cd(iz,ij,(a,b)=>{if(b&&"number"===b.type){var c=1/0,d=Array.from(iB(a.map(a=>a.value))).sort((a,b)=>a-b);if(d.length<2)return 1/0;var e=d[d.length-1]-d[0];if(0===e)return 1/0;for(var f=0;f<d.length-1;f++)c=Math.min(c,d[f+1]-d[f]);return c/e}}),jb=cd(ja,c9,hN,cZ,(a,b,c,d)=>d,(a,b,c,d,e)=>{if(!gU(a))return 0;var f="vertical"===b?d.height:d.width;if("gap"===e)return a*f/2;if("no-gap"===e){var g=C(c,a*f),h=a*f/2;return h-g-(h-g)/f*g}return 0}),jc=cd(id,(a,b)=>{var c=id(a,b);return null==c||"string"!=typeof c.padding?0:jb(a,"xAxis",b,c.padding)},(a,b)=>{if(null==a)return{left:0,right:0};var c,d,{padding:e}=a;return"string"==typeof e?{left:b,right:b}:{left:(null!=(c=e.left)?c:0)+b,right:(null!=(d=e.right)?d:0)+b}}),jd=cd(ig,(a,b)=>{var c=ig(a,b);return null==c||"string"!=typeof c.padding?0:jb(a,"yAxis",b,c.padding)},(a,b)=>{if(null==a)return{top:0,bottom:0};var c,d,{padding:e}=a;return"string"==typeof e?{top:b,bottom:b}:{top:(null!=(c=e.top)?c:0)+b,bottom:(null!=(d=e.bottom)?d:0)+b}}),je=cd([cZ,jc,c3,c2,(a,b,c)=>c],(a,b,c,d,e)=>{var{padding:f}=d;return e?[f.left,c.width-f.right]:[a.left+b.left,a.left+a.width-b.right]}),jf=cd([cZ,c9,jd,c3,c2,(a,b,c)=>c],(a,b,c,d,e,f)=>{var{padding:g}=e;return f?[d.height-g.bottom,g.top]:"horizontal"===b?[a.top+a.height-c.bottom,a.top+c.top]:[a.top+c.top,a.top+a.height-c.bottom]}),jg=(a,b,c,d)=>{var e;switch(b){case"xAxis":return je(a,c,d);case"yAxis":return jf(a,c,d);case"zAxis":return null==(e=ii(a,c))?void 0:e.range;case"angleAxis":return h4(a);case"radiusAxis":return h5(a,c);default:return}},jh=cd([ij,jg],hV),ji=cd([ij,i4,i9,jh],i5);function jj(a,b){return a.id<b.id?-1:+(a.id>b.id)}cd(ir,h7,(a,b)=>a.flatMap(a=>{var b;return null!=(b=a.errorBars)?b:[]}).filter(a=>iA(b,a)));var jk=(a,b)=>b,jl=(a,b,c)=>c,jm=cd(cT,jk,jl,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(jj)),jn=cd(cU,jk,jl,(a,b,c)=>a.filter(a=>a.orientation===b).filter(a=>a.mirror===c).sort(jj)),jo=(a,b)=>({width:a.width,height:b.height}),jp=cd(cZ,id,jo),jq=cd(cQ,cZ,jm,jk,jl,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=jo(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"top":return a.top;case"bottom":return c-a.bottom;default:return 0}})(b,d,a));var i="top"===d&&!e||"bottom"===d&&e;g[c.id]=f-Number(i)*h.height,f+=(i?-1:1)*h.height}),g}),jr=cd(cP,cZ,jn,jk,jl,(a,b,c,d,e)=>{var f,g={};return c.forEach(c=>{var h=((a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height}))(b,c);null==f&&(f=((a,b,c)=>{switch(b){case"left":return a.left;case"right":return c-a.right;default:return 0}})(b,d,a));var i="left"===d&&!e||"right"===d&&e;g[c.id]=f-Number(i)*h.width,f+=(i?-1:1)*h.width}),g}),js=cd(cZ,ig,(a,b)=>({width:"number"==typeof b.width?b.width:60,height:a.height})),jt=(a,b,c)=>{switch(b){case"xAxis":return jp(a,c).width;case"yAxis":return js(a,c).height;default:return}},ju=(a,b,c,d)=>{if(null!=c){var{allowDuplicatedCategory:e,type:f,dataKey:g}=c,h=cE(a,d),i=b.map(a=>a.value);if(g&&h&&"category"===f&&e&&D(i))return i}},jv=cd([c9,iz,ij,h7],ju),jw=(a,b,c,d)=>{if(null!=c&&null!=c.dataKey){var{type:e,scale:f}=c;if(cE(a,d)&&("number"===e||"auto"!==f))return b.map(a=>a.value)}},jx=cd([c9,iz,ik,h7],jw),jy=cd([c9,(a,b,c)=>{switch(b){case"xAxis":return id(a,c);case"yAxis":return ig(a,c);default:throw Error("Unexpected axis type: ".concat(b))}},i4,ji,jv,jx,jg,i7,h7],(a,b,c,d,e,f,g,h,i)=>{if(null==b)return null;var j=cE(a,i);return{angle:b.angle,interval:b.interval,minTickGap:b.minTickGap,orientation:b.orientation,tick:b.tick,tickCount:b.tickCount,tickFormatter:b.tickFormatter,ticks:b.ticks,type:b.type,unit:b.unit,axisType:i,categoricalDomain:f,duplicateDomain:e,isCategorical:j,niceTicks:h,range:g,realScaleType:c,scale:d}}),jz=cd([c9,ik,i4,ji,i7,jg,jv,jx,h7],(a,b,c,d,e,f,g,h,i)=>{if(null!=b&&null!=d){var j=cE(a,i),{type:k,ticks:l,tickCount:m}=b,n="scaleBand"===c&&"function"==typeof d.bandwidth?d.bandwidth()/2:2,o="category"===k&&d.bandwidth?d.bandwidth()/n:0;o="angleAxis"===i&&null!=f&&f.length>=2?2*v(f[0]-f[1])*o:o;var p=l||e;return p?p.map((a,b)=>({index:b,coordinate:d(g?g.indexOf(a):a)+o,value:a,offset:o})).filter(a=>!w(a.coordinate)):j&&h?h.map((a,b)=>({coordinate:d(a)+o,value:a,index:b,offset:o})):d.ticks?d.ticks(m).map(a=>({coordinate:d(a)+o,value:a,offset:o})):d.domain().map((a,b)=>({coordinate:d(a)+o,value:g?g[a]:a,index:b,offset:o}))}}),jA=cd([c9,ik,ji,jg,jv,jx,h7],(a,b,c,d,e,f,g)=>{if(null!=b&&null!=c&&null!=d&&d[0]!==d[1]){var h=cE(a,g),{tickCount:i}=b,j=0;return(j="angleAxis"===g&&(null==d?void 0:d.length)>=2?2*v(d[0]-d[1])*j:j,h&&f)?f.map((a,b)=>({coordinate:c(a)+j,value:a,index:b,offset:j})):c.ticks?c.ticks(i).map(a=>({coordinate:c(a)+j,value:a,offset:j})):c.domain().map((a,b)=>({coordinate:c(a)+j,value:e?e[a]:a,index:b,offset:j}))}}),jB=cd(ij,ji,(a,b)=>{if(null!=a&&null!=b)return ia(ia({},a),{},{scale:b})}),jC=cd([ij,i4,i2,jh],i5);cd((a,b,c)=>ii(a,c),jC,(a,b)=>{if(null!=a&&null!=b)return ia(ia({},a),{},{scale:b})});var jD=cd([c9,cT,cU],(a,b,c)=>{switch(a){case"horizontal":return b.some(a=>a.reversed)?"right-to-left":"left-to-right";case"vertical":return c.some(a=>a.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),jE=a=>a.options.defaultTooltipEventType,jF=a=>a.options.validateTooltipEventTypes;function jG(a,b,c){if(null==a)return b;var d=a?"axis":"item";return null==c?b:c.includes(d)?d:b}function jH(a,b){return jG(b,jE(a),jF(a))}var jI=(a,b)=>{var c,d=Number(b);if(!w(d)&&null!=b)return d>=0?null==a||null==(c=a[d])?void 0:c.value:void 0};function jJ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jK(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jJ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jJ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jL=(a,b,c,d)=>{if(null==b)return bz;var e=function(a,b,c){return"axis"===b?"click"===c?a.axisInteraction.click:a.axisInteraction.hover:"click"===c?a.itemInteraction.click:a.itemInteraction.hover}(a,b,c);if(null==e)return bz;if(e.active)return e;if(a.keyboardInteraction.active)return a.keyboardInteraction;if(a.syncInteraction.active&&null!=a.syncInteraction.index)return a.syncInteraction;var f=!0===a.settings.active;if(null!=e.index){if(f)return jK(jK({},e),{},{active:!0})}else if(null!=d)return{active:!0,coordinate:void 0,dataKey:void 0,index:d};return jK(jK({},bz),{},{coordinate:e.coordinate})},jM=(a,b)=>{var c=null==a?void 0:a.index;if(null==c)return null;var d=Number(c);if(!gU(d))return c;var e=Infinity;return b.length>0&&(e=b.length-1),String(Math.max(0,Math.min(d,e)))},jN=(a,b,c,d,e,f,g,h)=>{if(null!=f&&null!=h){var i=g[0],j=null==i?void 0:h(i.positions,f);if(null!=j)return j;var k=null==e?void 0:e[Number(f)];if(k)if("horizontal"===c)return{x:k.coordinate,y:(d.top+b)/2};else return{x:(d.left+a)/2,y:k.coordinate}}},jO=(a,b,c,d)=>{var e;return"axis"===b?a.tooltipItemPayloads:0===a.tooltipItemPayloads.length?[]:null==(e="hover"===c?a.itemInteraction.hover.dataKey:a.itemInteraction.click.dataKey)&&null!=d?[a.tooltipItemPayloads[0]]:a.tooltipItemPayloads.filter(a=>{var b;return(null==(b=a.settings)?void 0:b.dataKey)===e})},jP=a=>a.options.tooltipPayloadSearcher,jQ=a=>a.tooltip;function jR(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function jS(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?jR(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):jR(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var jT=(a,b,c,d,e,f,g)=>{if(null!=b&&null!=f){var{chartData:h,computedData:i,dataStartIndex:j,dataEndIndex:k}=c;return a.reduce((a,c)=>{var l,m,n,o,p,{dataDefinedOnItem:q,settings:r}=c,s=function(a,b,c){return Array.isArray(a)&&a&&b+c!==0?a.slice(b,c+1):a}((l=q,m=h,null!=l?l:m),j,k),t=null!=(n=null==r?void 0:r.dataKey)?n:null==d?void 0:d.dataKey,u=null==r?void 0:r.nameKey;return Array.isArray(o=null!=d&&d.dataKey&&Array.isArray(s)&&!Array.isArray(s[0])&&"axis"===g?F(s,d.dataKey,e):f(s,b,i,u))?o.forEach(b=>{var c=jS(jS({},r),{},{name:b.name,unit:b.unit,color:void 0,fill:void 0});a.push(cN({tooltipEntrySettings:c,dataKey:b.dataKey,payload:b.payload,value:cD(b.payload,b.dataKey),name:b.name}))}):a.push(cN({tooltipEntrySettings:r,dataKey:t,payload:o,value:cD(o,t),name:null!=(p=cD(o,u))?p:null==r?void 0:r.name})),a},[])}},jU=a=>{var b=c9(a);return"horizontal"===b?"xAxis":"vertical"===b?"yAxis":"centric"===b?"angleAxis":"radiusAxis"},jV=a=>a.tooltip.settings.axisId,jW=a=>{var b=jU(a),c=jV(a);return ik(a,b,c)},jX=cd([jW,c9,il,hP,jU],i3),jY=cd([a=>a.graphicalItems.cartesianItems,a=>a.graphicalItems.polarItems],(a,b)=>[...a,...b]),jZ=cd([jU,jV],im),j$=cd([jY,jW,jZ],iq),j_=cd([j$],iu),j0=cd([j_,gR],iw),j1=cd([j0,jW,j$],iy),j2=cd([jW],iJ),j3=cd([j0,j$,hO],iC),j4=cd([j3,gR,jU],iE),j5=cd([j$],is),j6=cd([j0,jW,j5,jU],iG),j7=cd([iL,jU,jV],iM),j8=cd([j7,jU],iS),j9=cd([iO,jU,jV],iM),ka=cd([j9,jU],iU),kb=cd([iQ,jU,jV],iM),kc=cd([kb,jU],iW),kd=cd([j8,kc,ka],iK),ke=cd([jW,j2,j4,j6,kd],i$),kf=cd([jW,c9,j0,j1,hO,jU,ke],i1),kg=cd([kf,jW,jX],i6),kh=cd([jW,kf,kg,jU],i8),ki=a=>{var b=jU(a),c=jV(a);return jg(a,b,c,!1)},kj=cd([jW,ki],hV),kk=cd([jW,jX,kh,kj],i5),kl=cd([c9,j1,jW,jU],ju),km=cd([c9,j1,jW,jU],jw),kn=cd([c9,jW,jX,kk,ki,kl,km,jU],(a,b,c,d,e,f,g,h)=>{if(b){var{type:i}=b,j=cE(a,h);if(d){var k="scaleBand"===c&&d.bandwidth?d.bandwidth()/2:2,l="category"===i&&d.bandwidth?d.bandwidth()/k:0;return(l="angleAxis"===h&&null!=e&&(null==e?void 0:e.length)>=2?2*v(e[0]-e[1])*l:l,j&&g)?g.map((a,b)=>({coordinate:d(a)+l,value:a,index:b,offset:l})):d.domain().map((a,b)=>({coordinate:d(a)+l,value:f?f[a]:a,index:b,offset:l}))}}}),ko=cd([jE,jF,a=>a.tooltip.settings],(a,b,c)=>jG(c.shared,a,b)),kp=a=>a.tooltip.settings.trigger,kq=a=>a.tooltip.settings.defaultIndex,kr=cd([jQ,ko,kp,kq],jL),ks=cd([kr,j0],jM),kt=cd([kn,ks],jI),ku=cd([kr],a=>{if(a)return a.dataKey}),kv=cd([jQ,ko,kp,kq],jO),kw=cd([cP,cQ,c9,cZ,kn,kq,kv,jP],jN),kx=cd([kr,kw],(a,b)=>null!=a&&a.coordinate?a.coordinate:b),ky=cd([kr],a=>a.active),kz=cd([kv,ks,gR,jW,kt,jP,ko],jT),kA=cd([kz],a=>{if(null!=a)return Array.from(new Set(a.map(a=>a.payload).filter(a=>null!=a)))}),kB=(a,b)=>b,kC=(a,b,c)=>c,kD=(a,b,c,d)=>d,kE=cd(kn,a=>co()(a,a=>a.coordinate)),kF=cd([jQ,kB,kC,kD],jL),kG=cd([kF,j0],jM),kH=cd([jQ,kB,kC,kD],jO),kI=cd([cP,cQ,c9,cZ,kn,kD,kH,jP],jN),kJ=cd([kF,kI],(a,b)=>{var c;return null!=(c=a.coordinate)?c:b}),kK=cd(kn,kG,jI),kL=cd([kH,kG,gR,jW,kK,jP,kB],jT),kM=cd([kF],a=>({isActive:a.active,activeIndex:a.index})),kN=cd([(a,b)=>b,c9,h6,jU,kj,kn,kE,cZ],(a,b,c,d,e,f,g,h)=>{if(a&&b&&d&&e&&f){var i=function(a,b,c,d,e){return"horizontal"===c||"vertical"===c?a>=e.left&&a<=e.left+e.width&&b>=e.top&&b<=e.top+e.height?{x:a,y:b}:null:d?((a,b)=>{var c,{x:d,y:e}=a,{radius:f,angle:g}=((a,b)=>{var{x:c,y:d}=a,{cx:e,cy:f}=b,g=((a,b)=>{var{x:c,y:d}=a,{x:e,y:f}=b;return Math.sqrt((c-e)**2+(d-f)**2)})({x:c,y:d},{x:e,y:f});if(g<=0)return{radius:g,angle:0};var h=Math.acos((c-e)/g);return d>f&&(h=2*Math.PI-h),{radius:g,angle:180*h/Math.PI,angleInRadian:h}})({x:d,y:e},b),{innerRadius:h,outerRadius:i}=b;if(f<h||f>i||0===f)return null;var{startAngle:j,endAngle:k}=(a=>{var{startAngle:b,endAngle:c}=a,d=Math.min(Math.floor(b/360),Math.floor(c/360));return{startAngle:b-360*d,endAngle:c-360*d}})(b),l=g;if(j<=k){for(;l>k;)l-=360;for(;l<j;)l+=360;c=l>=j&&l<=k}else{for(;l>j;)l-=360;for(;l<k;)l+=360;c=l>=k&&l<=j}return c?cx(cx({},b),{},{radius:f,angle:((a,b)=>{var{startAngle:c,endAngle:d}=b;return a+360*Math.min(Math.floor(c/360),Math.floor(d/360))})(l,b)}):null})({x:a,y:b},d):null}(a.chartX,a.chartY,b,c,h);if(i){var j=((a,b,c,d,e)=>{var f,g=-1,h=null!=(f=null==b?void 0:b.length)?f:0;if(h<=1||null==a)return 0;if("angleAxis"===d&&null!=e&&1e-6>=Math.abs(Math.abs(e[1]-e[0])-360))for(var i=0;i<h;i++){var j=i>0?c[i-1].coordinate:c[h-1].coordinate,k=c[i].coordinate,l=i>=h-1?c[0].coordinate:c[i+1].coordinate,m=void 0;if(v(k-j)!==v(l-k)){var n=[];if(v(l-k)===v(e[1]-e[0])){m=l;var o=k+e[1]-e[0];n[0]=Math.min(o,(o+j)/2),n[1]=Math.max(o,(o+j)/2)}else{m=j;var p=l+e[1]-e[0];n[0]=Math.min(k,(p+k)/2),n[1]=Math.max(k,(p+k)/2)}var q=[Math.min(k,(m+k)/2),Math.max(k,(m+k)/2)];if(a>q[0]&&a<=q[1]||a>=n[0]&&a<=n[1]){({index:g}=c[i]);break}}else{var r=Math.min(j,l),s=Math.max(j,l);if(a>(r+k)/2&&a<=(s+k)/2){({index:g}=c[i]);break}}}else if(b){for(var t=0;t<h;t++)if(0===t&&a<=(b[t].coordinate+b[t+1].coordinate)/2||t>0&&t<h-1&&a>(b[t].coordinate+b[t-1].coordinate)/2&&a<=(b[t].coordinate+b[t+1].coordinate)/2||t===h-1&&a>(b[t].coordinate+b[t-1].coordinate)/2){({index:g}=b[t]);break}}return g})(((a,b)=>"horizontal"===b?a.x:"vertical"===b?a.y:"centric"===b?a.angle:a.radius)(i,b),g,f,d,e),k=((a,b,c,d)=>{var e=b.find(a=>a&&a.index===c);if(e){if("horizontal"===a)return{x:e.coordinate,y:d.y};if("vertical"===a)return{x:d.x,y:e.coordinate};if("centric"===a){var f=e.coordinate,{radius:g}=d;return cC(cC(cC({},d),cz(d.cx,d.cy,g,f)),{},{angle:f,radius:g})}var h=e.coordinate,{angle:i}=d;return cC(cC(cC({},d),cz(d.cx,d.cy,h,i)),{},{angle:i,radius:h})}return{x:0,y:0}})(b,f,j,i);return{activeIndex:String(j),activeCoordinate:k}}}}),kO=a=>{var b=a.currentTarget.getBoundingClientRect(),c=b.width/a.currentTarget.offsetWidth,d=b.height/a.currentTarget.offsetHeight;return{chartX:Math.round((a.clientX-b.left)/c),chartY:Math.round((a.clientY-b.top)/d)}},kP=aJ("mouseClick"),kQ=bl();kQ.startListening({actionCreator:kP,effect:(a,b)=>{var c=a.payload,d=kN(b.getState(),kO(c));(null==d?void 0:d.activeIndex)!=null&&b.dispatch(bJ({activeIndex:d.activeIndex,activeDataKey:void 0,activeCoordinate:d.activeCoordinate}))}});var kR=aJ("mouseMove"),kS=bl();function kT(a,b){return b instanceof HTMLElement?"HTMLElement <".concat(b.tagName,' class="').concat(b.className,'">'):b===window?"global.window":b}function kU(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function kV(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?kU(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):kU(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}kS.startListening({actionCreator:kR,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jH(d,d.tooltip.settings.shared),f=kN(d,kO(c));"axis"===e&&((null==f?void 0:f.activeIndex)!=null?b.dispatch(bI({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate})):b.dispatch(bG()))}});var kW=aR({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(a,b){a.xAxis[b.payload.id]=b.payload},removeXAxis(a,b){delete a.xAxis[b.payload.id]},addYAxis(a,b){a.yAxis[b.payload.id]=b.payload},removeYAxis(a,b){delete a.yAxis[b.payload.id]},addZAxis(a,b){a.zAxis[b.payload.id]=b.payload},removeZAxis(a,b){delete a.zAxis[b.payload.id]},updateYAxisWidth(a,b){var{id:c,width:d}=b.payload;a.yAxis[c]&&(a.yAxis[c]=kV(kV({},a.yAxis[c]),{},{width:d}))}}}),{addXAxis:kX,removeXAxis:kY,addYAxis:kZ,removeYAxis:k$,addZAxis:k_,removeZAxis:k0,updateYAxisWidth:k1}=kW.actions,k2=kW.reducer,k3=aR({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(a){a.countOfBars+=1},removeBar(a){a.countOfBars-=1},addCartesianGraphicalItem(a,b){a.cartesianItems.push(b.payload)},replaceCartesianGraphicalItem(a,b){var{prev:c,next:d}=b.payload,e=aF(a).cartesianItems.indexOf(c);e>-1&&(a.cartesianItems[e]=d)},removeCartesianGraphicalItem(a,b){var c=aF(a).cartesianItems.indexOf(b.payload);c>-1&&a.cartesianItems.splice(c,1)},addPolarGraphicalItem(a,b){a.polarItems.push(b.payload)},removePolarGraphicalItem(a,b){var c=aF(a).polarItems.indexOf(b.payload);c>-1&&a.polarItems.splice(c,1)}}}),{addBar:k4,removeBar:k5,addCartesianGraphicalItem:k6,replaceCartesianGraphicalItem:k7,removeCartesianGraphicalItem:k8,addPolarGraphicalItem:k9,removePolarGraphicalItem:la}=k3.actions,lb=k3.reducer,lc=aR({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(a,b)=>{a.dots.push(b.payload)},removeDot:(a,b)=>{var c=aF(a).dots.findIndex(a=>a===b.payload);-1!==c&&a.dots.splice(c,1)},addArea:(a,b)=>{a.areas.push(b.payload)},removeArea:(a,b)=>{var c=aF(a).areas.findIndex(a=>a===b.payload);-1!==c&&a.areas.splice(c,1)},addLine:(a,b)=>{a.lines.push(b.payload)},removeLine:(a,b)=>{var c=aF(a).lines.findIndex(a=>a===b.payload);-1!==c&&a.lines.splice(c,1)}}}),{addDot:ld,removeDot:le,addArea:lf,removeArea:lg,addLine:lh,removeLine:li}=lc.actions,lj=lc.reducer,lk={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},ll=aR({name:"brush",initialState:lk,reducers:{setBrushSettings:(a,b)=>null==b.payload?lk:b.payload}}),{setBrushSettings:lm}=ll.actions,ln=ll.reducer,lo=aR({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(a,b){a.size.width=b.payload.width,a.size.height=b.payload.height},setLegendSettings(a,b){a.settings.align=b.payload.align,a.settings.layout=b.payload.layout,a.settings.verticalAlign=b.payload.verticalAlign,a.settings.itemSorter=b.payload.itemSorter},addLegendPayload(a,b){a.payload.push(b.payload)},removeLegendPayload(a,b){var c=aF(a).payload.indexOf(b.payload);c>-1&&a.payload.splice(c,1)}}}),{setLegendSize:lp,setLegendSettings:lq,addLegendPayload:lr,removeLegendPayload:ls}=lo.actions,lt=lo.reducer,lu={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},lv=aR({name:"rootProps",initialState:lu,reducers:{updateOptions:(a,b)=>{var c;a.accessibilityLayer=b.payload.accessibilityLayer,a.barCategoryGap=b.payload.barCategoryGap,a.barGap=null!=(c=b.payload.barGap)?c:lu.barGap,a.barSize=b.payload.barSize,a.maxBarSize=b.payload.maxBarSize,a.stackOffset=b.payload.stackOffset,a.syncId=b.payload.syncId,a.syncMethod=b.payload.syncMethod,a.className=b.payload.className}}}),lw=lv.reducer,{updateOptions:lx}=lv.actions,ly=aR({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(a,b){a.radiusAxis[b.payload.id]=b.payload},removeRadiusAxis(a,b){delete a.radiusAxis[b.payload.id]},addAngleAxis(a,b){a.angleAxis[b.payload.id]=b.payload},removeAngleAxis(a,b){delete a.angleAxis[b.payload.id]}}}),{addRadiusAxis:lz,removeRadiusAxis:lA,addAngleAxis:lB,removeAngleAxis:lC}=ly.actions,lD=ly.reducer,lE=aR({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(a,b)=>b.payload}}),{updatePolarOptions:lF}=lE.actions,lG=lE.reducer,lH=aJ("keyDown"),lI=aJ("focus"),lJ=bl();lJ.startListening({actionCreator:lH,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip,e=a.payload;if("ArrowRight"===e||"ArrowLeft"===e||"Enter"===e){var f=Number(jM(d,j0(c))),g=kn(c);if("Enter"===e){var h=kI(c,"axis","hover",String(d.index));b.dispatch(bL({active:!d.active,activeIndex:d.index,activeDataKey:d.dataKey,activeCoordinate:h}));return}var i=f+("ArrowRight"===e?1:-1)*("left-to-right"===jD(c)?1:-1);if(null!=g&&!(i>=g.length)&&!(i<0)){var j=kI(c,"axis","hover",String(i));b.dispatch(bL({active:!0,activeIndex:i.toString(),activeDataKey:void 0,activeCoordinate:j}))}}}}}),lJ.startListening({actionCreator:lI,effect:(a,b)=>{var c=b.getState();if(!1!==c.rootProps.accessibilityLayer){var{keyboardInteraction:d}=c.tooltip;if(!d.active&&null==d.index){var e=kI(c,"axis","hover",String("0"));b.dispatch(bL({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:e}))}}}});var lK=aJ("externalEvent"),lL=bl();lL.startListening({actionCreator:lK,effect:(a,b)=>{if(null!=a.payload.handler){var c=b.getState(),d={activeCoordinate:kx(c),activeDataKey:ku(c),activeIndex:ks(c),activeLabel:kt(c),activeTooltipIndex:ks(c),isTooltipActive:ky(c)};a.payload.handler(d,a.payload.reactEvent)}}});var lM=cd([jQ],a=>a.tooltipItemPayloads),lN=cd([lM,jP,(a,b,c)=>b,(a,b,c)=>c],(a,b,c,d)=>{var e=a.find(a=>a.settings.dataKey===d);if(null!=e){var{positions:f}=e;if(null!=f)return b(f,c)}}),lO=aJ("touchMove"),lP=bl();lP.startListening({actionCreator:lO,effect:(a,b)=>{var c=a.payload,d=b.getState(),e=jH(d,d.tooltip.settings.shared);if("axis"===e){var f=kN(d,kO({clientX:c.touches[0].clientX,clientY:c.touches[0].clientY,currentTarget:c.currentTarget}));(null==f?void 0:f.activeIndex)!=null&&b.dispatch(bI({activeIndex:f.activeIndex,activeDataKey:void 0,activeCoordinate:f.activeCoordinate}))}else if("item"===e){var g,h=c.touches[0],i=document.elementFromPoint(h.clientX,h.clientY);if(!i||!i.getAttribute)return;var j=i.getAttribute(cV),k=null!=(g=i.getAttribute(cW))?g:void 0,l=lN(b.getState(),j,k);b.dispatch(bE({activeDataKey:k,activeIndex:j,activeCoordinate:l}))}}});var lQ=Q({brush:ln,cartesianAxis:k2,chartData:bR,graphicalItems:lb,layout:bX,legend:lt,options:bp,polarAxis:lD,polarOptions:lG,referenceElements:lj,rootProps:lw,tooltip:bM}),lR=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(a){let b,c,d,e=function(a){let{thunk:b=!0,immutableCheck:c=!0,serializableCheck:d=!0,actionCreatorCheck:e=!0}=a??{},f=new aK;return b&&("boolean"==typeof b?f.push(U):f.push(T(b.extraArgument))),f},{reducer:f,middleware:g,devTools:h=!0,duplicateMiddlewareCheck:i=!0,preloadedState:j,enhancers:k}=a||{};if("function"==typeof f)b=f;else if(P(f))b=Q(f);else throw Error(bm(1));c="function"==typeof g?g(e):e();let l=R;h&&(l=aI({trace:!1,..."object"==typeof h&&h}));let m=(d=function(...a){return b=>(c,d)=>{let e=b(c,d),f=()=>{throw Error(L(15))},g={getState:e.getState,dispatch:(a,...b)=>f(a,...b)};return f=R(...a.map(a=>a(g)))(e.dispatch),{...e,dispatch:f}}}(...c),function(a){let{autoBatch:b=!0}=a??{},c=new aK(d);return b&&c.push(((a={type:"raf"})=>b=>(...c)=>{let d=b(...c),e=!0,f=!1,g=!1,h=new Set,i="tick"===a.type?queueMicrotask:"raf"===a.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:aN(10):"callback"===a.type?a.queueNotification:aN(a.timeout),j=()=>{g=!1,f&&(f=!1,h.forEach(a=>a()))};return Object.assign({},d,{subscribe(a){let b=d.subscribe(()=>e&&a());return h.add(a),()=>{b(),h.delete(a)}},dispatch(a){try{return(f=!(e=!a?.meta?.RTK_autoBatch))&&!g&&(g=!0,i(j)),d.dispatch(a)}finally{e=!0}}})})("object"==typeof b?b:void 0)),c});return function a(b,c,d){if("function"!=typeof b)throw Error(L(2));if("function"==typeof c&&"function"==typeof d||"function"==typeof d&&"function"==typeof arguments[3])throw Error(L(0));if("function"==typeof c&&void 0===d&&(d=c,c=void 0),void 0!==d){if("function"!=typeof d)throw Error(L(1));return d(a)(b,c)}let e=b,f=c,g=new Map,h=g,i=0,j=!1;function k(){h===g&&(h=new Map,g.forEach((a,b)=>{h.set(b,a)}))}function l(){if(j)throw Error(L(3));return f}function m(a){if("function"!=typeof a)throw Error(L(4));if(j)throw Error(L(5));let b=!0;k();let c=i++;return h.set(c,a),function(){if(b){if(j)throw Error(L(6));b=!1,k(),h.delete(c),g=null}}}function n(a){if(!P(a))throw Error(L(7));if(void 0===a.type)throw Error(L(8));if("string"!=typeof a.type)throw Error(L(17));if(j)throw Error(L(9));try{j=!0,f=e(f,a)}finally{j=!1}return(g=h).forEach(a=>{a()}),a}return n({type:O.INIT}),{dispatch:n,subscribe:m,getState:l,replaceReducer:function(a){if("function"!=typeof a)throw Error(L(10));e=a,n({type:O.REPLACE})},[M]:function(){return{subscribe(a){if("object"!=typeof a||null===a)throw Error(L(11));function b(){a.next&&a.next(l())}return b(),{unsubscribe:m(b)}},[M](){return this}}}}}(b,j,l(..."function"==typeof k?k(m):m()))}({reducer:lQ,preloadedState:a,middleware:a=>a({serializableCheck:!1}).concat([kQ.middleware,kS.middleware,lJ.middleware,lL.middleware,lP.middleware]),devTools:{serialize:{replacer:kT},name:"recharts-".concat(b)}})};function lS(a){var{preloadedState:b,children:c,reduxStoreName:d}=a,e=c1(),f=(0,h.useRef)(null);return e?c:(null==f.current&&(f.current=lR(b,d)),h.createElement(by,{context:cg,store:f.current},c))}var lT=a=>{var{chartData:b}=a,c=ci(),d=c1();return(0,h.useEffect)(()=>d?()=>{}:(c(bO(b)),()=>{c(bO(void 0))}),[b,c,d]),null};function lU(a){var{layout:b,width:c,height:d,margin:e}=a;return ci(),c1(),null}function lV(a){return ci(),null}var lW=c(29632),lX=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],lY=["points","pathLength"],lZ={svg:["viewBox","children"],polygon:lY,polyline:lY},l$=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],l_=(a,b)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var c=a;if((0,h.isValidElement)(a)&&(c=a.props),"object"!=typeof c&&"function"!=typeof c)return null;var d={};return Object.keys(c).forEach(a=>{l$.includes(a)&&(d[a]=b||(b=>c[a](c,b)))}),d},l0=(a,b,c)=>{if(null===a||"object"!=typeof a&&"function"!=typeof a)return null;var d=null;return Object.keys(a).forEach(e=>{var f=a[e];l$.includes(e)&&"function"==typeof f&&(d||(d={}),d[e]=a=>(f(b,c,a),null))}),d},l1=a=>"string"==typeof a?a:a?a.displayName||a.name||"Component":"",l2=null,l3=null,l4=a=>{if(a===l2&&Array.isArray(l3))return l3;var b=[];return h.Children.forEach(a,a=>{null==a||((0,lW.isFragment)(a)?b=b.concat(l4(a.props.children)):b.push(a))}),l3=b,l2=a,b};function l5(a,b){var c=[],d=[];return d=Array.isArray(b)?b.map(a=>l1(a)):[l1(b)],l4(a).forEach(a=>{var b=u()(a,"type.displayName")||u()(a,"type.name");-1!==d.indexOf(b)&&c.push(a)}),c}var l6=a=>!a||"object"!=typeof a||!("clipDot"in a)||!!a.clipDot,l7=(a,b,c)=>{if(!a||"function"==typeof a||"boolean"==typeof a)return null;var d=a;if((0,h.isValidElement)(a)&&(d=a.props),"object"!=typeof d&&"function"!=typeof d)return null;var e={};return Object.keys(d).forEach(a=>{var f;((a,b,c,d)=>{var e,f=null!=(e=d&&(null==lZ?void 0:lZ[d]))?e:[];return b.startsWith("data-")||"function"!=typeof a&&(d&&f.includes(b)||lX.includes(b))||c&&l$.includes(b)})(null==(f=d)?void 0:f[a],a,b,c)&&(e[a]=d[a])}),e},l8=()=>cm(a=>a.rootProps.accessibilityLayer),l9=["children","width","height","viewBox","className","style","title","desc"];function ma(){return(ma=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var mb=(0,h.forwardRef)((a,b)=>{var{children:c,width:d,height:e,viewBox:f,className:g,style:i,title:j,desc:k}=a,l=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,l9),m=f||{width:d,height:e,x:0,y:0},n=(0,q.$)("recharts-surface",g);return h.createElement("svg",ma({},l7(l,!0,"svg"),{className:n,width:d,height:e,style:i,viewBox:"".concat(m.x," ").concat(m.y," ").concat(m.width," ").concat(m.height),ref:b}),h.createElement("title",null,j),h.createElement("desc",null,k),c)}),mc=["children"];function md(){return(md=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var me={width:"100%",height:"100%"},mf=(0,h.forwardRef)((a,b)=>{var c,d,e=c7(),f=c8(),g=l8();if(!gV(e)||!gV(f))return null;var{children:i,otherAttributes:j,title:k,desc:l}=a;return c="number"==typeof j.tabIndex?j.tabIndex:g?0:void 0,d="string"==typeof j.role?j.role:g?"application":void 0,h.createElement(mb,md({},j,{title:k,desc:l,role:d,tabIndex:c,width:e,height:f,style:me,ref:b}),i)}),mg=a=>{var{children:b}=a,c=cm(c3);if(!c)return null;var{width:d,height:e,y:f,x:g}=c;return h.createElement(mb,{width:d,height:e,x:g,y:f},b)},mh=(0,h.forwardRef)((a,b)=>{var{children:c}=a,d=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mc);return c1()?h.createElement(mg,null,c):h.createElement(mf,md({ref:b},d),c)});function mi(a){return a.tooltip.syncInteraction}new(c(11117));var mj=(0,h.createContext)(null),mk=(0,h.createContext)(null);function ml(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var mm=(0,h.forwardRef)((a,b)=>{var{children:c,className:d,height:e,onClick:f,onContextMenu:g,onDoubleClick:i,onMouseDown:j,onMouseEnter:k,onMouseLeave:l,onMouseMove:m,onMouseUp:n,onTouchEnd:o,onTouchMove:p,onTouchStart:r,style:s,width:t}=a,u=ci(),[v,w]=(0,h.useState)(null),[x,y]=(0,h.useState)(null);ci(),cm(hQ),cm(hS),ci(),cm(hR),cm(kn),da(),c4(),cm(a=>a.rootProps.className),cm(hQ),cm(hS),ci();var z=function(){ci();var[a,b]=(0,h.useState)(null);return cm(cR),b}(),A=(0,h.useCallback)(a=>{z(a),"function"==typeof b&&b(a),w(a),y(a)},[z,b,w,y]),B=(0,h.useCallback)(a=>{u(kP(a)),u(lK({handler:f,reactEvent:a}))},[u,f]),C=(0,h.useCallback)(a=>{u(kR(a)),u(lK({handler:k,reactEvent:a}))},[u,k]),D=(0,h.useCallback)(a=>{u(bG()),u(lK({handler:l,reactEvent:a}))},[u,l]),E=(0,h.useCallback)(a=>{u(kR(a)),u(lK({handler:m,reactEvent:a}))},[u,m]),F=(0,h.useCallback)(()=>{u(lI())},[u]),G=(0,h.useCallback)(a=>{u(lH(a.key))},[u]),H=(0,h.useCallback)(a=>{u(lK({handler:g,reactEvent:a}))},[u,g]),I=(0,h.useCallback)(a=>{u(lK({handler:i,reactEvent:a}))},[u,i]),J=(0,h.useCallback)(a=>{u(lK({handler:j,reactEvent:a}))},[u,j]),K=(0,h.useCallback)(a=>{u(lK({handler:n,reactEvent:a}))},[u,n]),L=(0,h.useCallback)(a=>{u(lK({handler:r,reactEvent:a}))},[u,r]),M=(0,h.useCallback)(a=>{u(lO(a)),u(lK({handler:p,reactEvent:a}))},[u,p]),N=(0,h.useCallback)(a=>{u(lK({handler:o,reactEvent:a}))},[u,o]);return h.createElement(mj.Provider,{value:v},h.createElement(mk.Provider,{value:x},h.createElement("div",{className:(0,q.$)("recharts-wrapper",d),style:function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ml(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ml(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({position:"relative",cursor:"default",width:t,height:e},s),onClick:B,onContextMenu:H,onDoubleClick:I,onFocus:F,onKeyDown:G,onMouseDown:J,onMouseEnter:C,onMouseLeave:D,onMouseMove:E,onMouseUp:K,onTouchEnd:N,onTouchMove:M,onTouchStart:L,ref:A},c)))}),mn=cd([cZ],a=>{if(a)return{top:a.top,bottom:a.bottom,left:a.left,right:a.right}}),mo=cd([mn,cP,cQ],(a,b,c)=>{if(a&&null!=b&&null!=c)return{x:a.left,y:a.top,width:Math.max(0,b-a.left-a.right),height:Math.max(0,c-a.top-a.bottom)}}),mp=()=>cm(mo),mq=(0,h.createContext)(void 0),mr=a=>{var{children:b}=a,[c]=(0,h.useState)("".concat(B("recharts"),"-clip")),d=mp();if(null==d)return null;var{x:e,y:f,width:g,height:i}=d;return h.createElement(mq.Provider,{value:c},h.createElement("defs",null,h.createElement("clipPath",{id:c},h.createElement("rect",{x:e,y:f,height:i,width:g}))),b)},ms=["children","className","width","height","style","compact","title","desc"],mt=(0,h.forwardRef)((a,b)=>{var{children:c,className:d,width:e,height:f,style:g,compact:i,title:j,desc:k}=a,l=l7(function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,ms),!1);return i?h.createElement(mh,{otherAttributes:l,title:j,desc:k},c):h.createElement(mm,{className:d,style:g,width:e,height:f,onClick:a.onClick,onMouseLeave:a.onMouseLeave,onMouseEnter:a.onMouseEnter,onMouseMove:a.onMouseMove,onMouseDown:a.onMouseDown,onMouseUp:a.onMouseUp,onContextMenu:a.onContextMenu,onDoubleClick:a.onDoubleClick,onTouchStart:a.onTouchStart,onTouchMove:a.onTouchMove,onTouchEnd:a.onTouchEnd},h.createElement(mh,{otherAttributes:l,title:j,desc:k,ref:b},h.createElement(mr,null,c)))});function mu(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mv(a,b){var c=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mu(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mu(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return Object.keys(b).reduce((a,c)=>(void 0===a[c]&&void 0!==b[c]&&(a[c]=b[c]),a),c)}var mw=["width","height"];function mx(){return(mx=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var my={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},mz=(0,h.forwardRef)(function(a,b){var c,d=mv(a.categoricalChartProps,my),{width:e,height:f}=d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,mw);if(!gV(e)||!gV(f))return null;var{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,categoricalChartProps:m}=a;return h.createElement(lS,{preloadedState:{options:{chartName:i,defaultTooltipEventType:j,validateTooltipEventTypes:k,tooltipPayloadSearcher:l,eventEmitter:void 0}},reduxStoreName:null!=(c=m.id)?c:i},h.createElement(lT,{chartData:m.data}),h.createElement(lU,{width:e,height:f,layout:d.layout,margin:d.margin}),h.createElement(lV,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),h.createElement(mt,mx({},g,{width:e,height:f,ref:b})))}),mA=["axis"],mB=(0,h.forwardRef)((a,b)=>h.createElement(mz,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:mA,tooltipPayloadSearcher:bn,categoricalChartProps:a,ref:b})),mC={isSsr:!0};function mD(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mE(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mD(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mD(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var mF={widthCache:{},cacheCount:0},mG={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},mH="recharts_measurement_span",mI=function(a){var b,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==a||mC.isSsr)return{width:0,height:0};var d=(Object.keys(b=mE({},c)).forEach(a=>{b[a]||delete b[a]}),b),e=JSON.stringify({text:a,copyStyle:d});if(mF.widthCache[e])return mF.widthCache[e];try{var f=document.getElementById(mH);f||((f=document.createElement("span")).setAttribute("id",mH),f.setAttribute("aria-hidden","true"),document.body.appendChild(f));var g=mE(mE({},mG),d);Object.assign(f.style,g),f.textContent="".concat(a);var h=f.getBoundingClientRect(),i={width:h.width,height:h.height};return mF.widthCache[e]=i,++mF.cacheCount>2e3&&(mF.cacheCount=0,mF.widthCache={}),i}catch(a){return{width:0,height:0}}};class mJ{static create(a){return new mJ(a)}constructor(a){this.scale=a}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(a){var{bandAware:b,position:c}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==a){if(c)switch(c){case"start":default:return this.scale(a);case"middle":var d=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+d;case"end":var e=this.bandwidth?this.bandwidth():0;return this.scale(a)+e}if(b){var f=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+f}return this.scale(a)}}isInRange(a){var b=this.range(),c=b[0],d=b[b.length-1];return c<=d?a>=c&&a<=d:a>=d&&a<=c}}!function(a,b,c){var d;(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:1e-4,enumerable:!0,configurable:!0,writable:!0}):a[b]=1e-4}(mJ,"EPS",1e-4);var mK=function(a){var{width:b,height:c}=a,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=(d%180+180)%180*Math.PI/180,f=Math.atan(c/b);return Math.abs(e>f&&e<Math.PI-f?c/Math.sin(e):b/Math.cos(e))};function mL(a,b,c){if(b<1)return[];if(1===b&&void 0===c)return a;for(var d=[],e=0;e<a.length;e+=b)if(void 0!==c&&!0!==c(a[e]))return;else d.push(a[e]);return d}function mM(a,b,c,d,e){if(a*b<a*d||a*b>a*e)return!1;var f=c();return a*(b-a*f/2-d)>=0&&a*(b+a*f/2-e)<=0}function mN(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function mO(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?mN(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):mN(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function mP(a,b,c){var d,{tick:e,ticks:f,viewBox:g,minTickGap:h,orientation:i,interval:j,tickFormatter:k,unit:l,angle:m}=a;if(!f||!f.length||!e)return[];if(y(j)||mC.isSsr)return null!=(d=mL(f,(y(j)?j:0)+1))?d:[];var n="top"===i||"bottom"===i?"width":"height",o=l&&"width"===n?mI(l,{fontSize:b,letterSpacing:c}):{width:0,height:0},p=(a,d)=>{var e,f="function"==typeof k?k(a.value,d):a.value;return"width"===n?(e=mI(f,{fontSize:b,letterSpacing:c}),mK({width:e.width+o.width,height:e.height+o.height},m)):mI(f,{fontSize:b,letterSpacing:c})[n]},q=f.length>=2?v(f[1].coordinate-f[0].coordinate):1,r=function(a,b,c){var d="width"===c,{x:e,y:f,width:g,height:h}=a;return 1===b?{start:d?e:f,end:d?e+g:f+h}:{start:d?e+g:f+h,end:d?e:f}}(g,q,n);return"equidistantPreserveStart"===j?function(a,b,c,d,e){for(var f,g=(d||[]).slice(),{start:h,end:i}=b,j=0,k=1,l=h;k<=g.length;)if(f=function(){var b,f=null==d?void 0:d[j];if(void 0===f)return{v:mL(d,k)};var g=j,m=()=>(void 0===b&&(b=c(f,g)),b),n=f.coordinate,o=0===j||mM(a,n,m,l,i);o||(j=0,l=h,k+=1),o&&(l=n+a*(m()/2+e),j+=k)}())return f.v;return[]}(q,r,p,f,h):("preserveStart"===j||"preserveStartEnd"===j?function(a,b,c,d,e,f){var g=(d||[]).slice(),h=g.length,{start:i,end:j}=b;if(f){var k=d[h-1],l=c(k,h-1),m=a*(k.coordinate+a*l/2-j);g[h-1]=k=mO(mO({},k),{},{tickCoord:m>0?k.coordinate-m*a:k.coordinate}),mM(a,k.tickCoord,()=>l,i,j)&&(j=k.tickCoord-a*(l/2+e),g[h-1]=mO(mO({},k),{},{isShow:!0}))}for(var n=f?h-1:h,o=function(b){var d,f=g[b],h=()=>(void 0===d&&(d=c(f,b)),d);if(0===b){var k=a*(f.coordinate-a*h()/2-i);g[b]=f=mO(mO({},f),{},{tickCoord:k<0?f.coordinate-k*a:f.coordinate})}else g[b]=f=mO(mO({},f),{},{tickCoord:f.coordinate});mM(a,f.tickCoord,h,i,j)&&(i=f.tickCoord+a*(h()/2+e),g[b]=mO(mO({},f),{},{isShow:!0}))},p=0;p<n;p++)o(p);return g}(q,r,p,f,h,"preserveStartEnd"===j):function(a,b,c,d,e){for(var f=(d||[]).slice(),g=f.length,{start:h}=b,{end:i}=b,j=function(b){var d,j=f[b],k=()=>(void 0===d&&(d=c(j,b)),d);if(b===g-1){var l=a*(j.coordinate+a*k()/2-i);f[b]=j=mO(mO({},j),{},{tickCoord:l>0?j.coordinate-l*a:j.coordinate})}else f[b]=j=mO(mO({},j),{},{tickCoord:j.coordinate});mM(a,j.tickCoord,k,h,i)&&(i=j.tickCoord-a*(k()/2+e),f[b]=mO(mO({},j),{},{isShow:!0}))},k=g-1;k>=0;k--)j(k);return f}(q,r,p,f,h)).filter(a=>a.isShow)}function mQ(a,b){for(var c in a)if(({}).hasOwnProperty.call(a,c)&&(!({}).hasOwnProperty.call(b,c)||a[c]!==b[c]))return!1;for(var d in b)if(({}).hasOwnProperty.call(b,d)&&!({}).hasOwnProperty.call(a,d))return!1;return!0}var mR=["children","className"];function mS(){return(mS=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var mT=h.forwardRef((a,b)=>{var{children:c,className:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,mR),f=(0,q.$)("recharts-layer",d);return h.createElement("g",mS({className:f},l7(e,!0),{ref:b}),c)}),mU=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,mV=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,mW=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,mX=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,mY={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},mZ=Object.keys(mY);class m${static parse(a){var b,[,c,d]=null!=(b=mX.exec(a))?b:[];return new m$(parseFloat(c),null!=d?d:"")}constructor(a,b){this.num=a,this.unit=b,this.num=a,this.unit=b,w(a)&&(this.unit=""),""===b||mW.test(b)||(this.num=NaN,this.unit=""),mZ.includes(b)&&(this.num=a*mY[b],this.unit="px")}add(a){return this.unit!==a.unit?new m$(NaN,""):new m$(this.num+a.num,this.unit)}subtract(a){return this.unit!==a.unit?new m$(NaN,""):new m$(this.num-a.num,this.unit)}multiply(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new m$(NaN,""):new m$(this.num*a.num,this.unit||a.unit)}divide(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new m$(NaN,""):new m$(this.num/a.num,this.unit||a.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return w(this.num)}}function m_(a){if(a.includes("NaN"))return"NaN";for(var b=a;b.includes("*")||b.includes("/");){var c,[,d,e,f]=null!=(c=mU.exec(b))?c:[],g=m$.parse(null!=d?d:""),h=m$.parse(null!=f?f:""),i="*"===e?g.multiply(h):g.divide(h);if(i.isNaN())return"NaN";b=b.replace(mU,i.toString())}for(;b.includes("+")||/.-\d+(?:\.\d+)?/.test(b);){var j,[,k,l,m]=null!=(j=mV.exec(b))?j:[],n=m$.parse(null!=k?k:""),o=m$.parse(null!=m?m:""),p="+"===l?n.add(o):n.subtract(o);if(p.isNaN())return"NaN";b=b.replace(mV,p.toString())}return b}var m0=/\(([^()]*)\)/;function m1(a){var b=function(a){try{var b;return b=a.replace(/\s+/g,""),b=function(a){for(var b,c=a;null!=(b=m0.exec(c));){var[,d]=b;c=c.replace(m0,m_(d))}return c}(b),b=m_(b)}catch(a){return"NaN"}}(a.slice(5,-1));return"NaN"===b?"":b}var m2=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],m3=["dx","dy","angle","className","breakAll"];function m4(){return(m4=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function m5(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var m6=/[ \f\n\r\t\v\u2028\u2029]+/,m7=a=>{var{children:b,breakAll:c,style:d}=a;try{let a;var e=[];a=b,null==a||(e=c?b.toString().split(""):b.toString().split(m6));var f=e.map(a=>({word:a,width:mI(a,d).width})),g=c?0:mI("\xa0",d).width;return{wordsWithComputedWidth:f,spaceWidth:g}}catch(a){return null}},m8=a=>[{words:null==a?[]:a.toString().split(m6)}],m9="#808080",na=(0,h.forwardRef)((a,b)=>{var c,{x:d=0,y:e=0,lineHeight:f="1em",capHeight:g="0.71em",scaleToFit:i=!1,textAnchor:j="start",verticalAnchor:k="end",fill:l=m9}=a,m=m5(a,m2),n=(0,h.useMemo)(()=>(a=>{var{width:b,scaleToFit:c,children:d,style:e,breakAll:f,maxLines:g}=a;if((b||c)&&!mC.isSsr){var h=m7({breakAll:f,children:d,style:e});if(!h)return m8(d);var{wordsWithComputedWidth:i,spaceWidth:j}=h;return((a,b,c,d,e)=>{var f,{maxLines:g,children:h,style:i,breakAll:j}=a,k=y(g),l=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return a.reduce((a,b)=>{var{word:f,width:g}=b,h=a[a.length-1];return h&&(null==d||e||h.width+g+c<Number(d))?(h.words.push(f),h.width+=g+c):a.push({words:[f],width:g}),a},[])},m=l(b),n=a=>a.reduce((a,b)=>a.width>b.width?a:b);if(!k||e||!(m.length>g||n(m).width>Number(d)))return m;for(var o=a=>{var b=l(m7({breakAll:j,style:i,children:h.slice(0,a)+"…"}).wordsWithComputedWidth);return[b.length>g||n(b).width>Number(d),b]},p=0,q=h.length-1,r=0;p<=q&&r<=h.length-1;){var s=Math.floor((p+q)/2),[t,u]=o(s-1),[v]=o(s);if(t||v||(p=s+1),t&&v&&(q=s-1),!t&&v){f=u;break}r++}return f||m})({breakAll:f,children:d,maxLines:g,style:e},i,j,b,c)}return m8(d)})({breakAll:m.breakAll,children:m.children,maxLines:m.maxLines,scaleToFit:i,style:m.style,width:m.width}),[m.breakAll,m.children,m.maxLines,i,m.style,m.width]),{dx:o,dy:p,angle:r,className:s,breakAll:t}=m,u=m5(m,m3);if(!z(d)||!z(e))return null;var v=d+(y(o)?o:0),w=e+(y(p)?p:0);switch(k){case"start":c=m1("calc(".concat(g,")"));break;case"middle":c=m1("calc(".concat((n.length-1)/2," * -").concat(f," + (").concat(g," / 2))"));break;default:c=m1("calc(".concat(n.length-1," * -").concat(f,")"))}var x=[];if(i){var A=n[0].width,{width:B}=m;x.push("scale(".concat(y(B)?B/A:1,")"))}return r&&x.push("rotate(".concat(r,", ").concat(v,", ").concat(w,")")),x.length&&(u.transform=x.join(" ")),h.createElement("text",m4({},l7(u,!0),{ref:b,x:v,y:w,className:(0,q.$)("recharts-text",s),textAnchor:j,fill:l.includes("url")?m9:l}),n.map((a,b)=>{var d=a.words.join(t?"":" ");return h.createElement("tspan",{x:v,dy:0===b?c:f,key:"".concat(d,"-").concat(b)},d)}))});na.displayName="Text";var nb=["offset"],nc=["labelRef"];function nd(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function ne(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nf(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ne(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ne(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function ng(){return(ng=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var nh=a=>null!=a&&"function"==typeof a;function ni(a){var b,{offset:c=5}=a,d=nf({offset:c},nd(a,nb)),{viewBox:e,position:f,value:g,children:i,content:j,className:k="",textBreakAll:l,labelRef:m}=d,n=c4(),o=e||n;if(!o||null==g&&null==i&&!(0,h.isValidElement)(j)&&"function"!=typeof j)return null;if((0,h.isValidElement)(j)){var{labelRef:p}=d,r=nd(d,nc);return(0,h.cloneElement)(j,r)}if("function"==typeof j){if(b=(0,h.createElement)(j,d),(0,h.isValidElement)(b))return b}else b=(a=>{var{value:b,formatter:c}=a,d=null==a.children?b:a.children;return"function"==typeof c?c(d):d})(d);var s="cx"in o&&y(o.cx),t=l7(d,!0);if(s&&("insideStart"===f||"insideEnd"===f||"end"===f))return((a,b,c)=>{let d,e;var f,g,{position:i,viewBox:j,offset:k,className:l}=a,{cx:m,cy:n,innerRadius:o,outerRadius:p,startAngle:r,endAngle:s,clockWise:t}=j,u=(o+p)/2,w=(d=r,v((e=s)-d)*Math.min(Math.abs(e-d),360)),x=w>=0?1:-1;"insideStart"===i?(f=r+x*k,g=t):"insideEnd"===i?(f=s-x*k,g=!t):"end"===i&&(f=s+x*k,g=t),g=w<=0?g:!g;var y=cz(m,n,u,f),z=cz(m,n,u,f+(g?1:-1)*359),A="M".concat(y.x,",").concat(y.y,"\n    A").concat(u,",").concat(u,",0,1,").concat(+!g,",\n    ").concat(z.x,",").concat(z.y),C=null==a.id?B("recharts-radial-line-"):a.id;return h.createElement("text",ng({},c,{dominantBaseline:"central",className:(0,q.$)("recharts-radial-bar-label",l)}),h.createElement("defs",null,h.createElement("path",{id:C,d:A})),h.createElement("textPath",{xlinkHref:"#".concat(C)},b))})(d,b,t);var u=s?(a=>{var{viewBox:b,offset:c,position:d}=a,{cx:e,cy:f,innerRadius:g,outerRadius:h,startAngle:i,endAngle:j}=b,k=(i+j)/2;if("outside"===d){var{x:l,y:m}=cz(e,f,h+c,k);return{x:l,y:m,textAnchor:l>=e?"start":"end",verticalAnchor:"middle"}}if("center"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"end"};var{x:n,y:o}=cz(e,f,(g+h)/2,k);return{x:n,y:o,textAnchor:"middle",verticalAnchor:"middle"}})(d):((a,b)=>{var{parentViewBox:c,offset:d,position:e}=a,{x:f,y:g,width:h,height:i}=b,j=i>=0?1:-1,k=j*d,l=j>0?"end":"start",m=j>0?"start":"end",n=h>=0?1:-1,o=n*d,p=n>0?"end":"start",q=n>0?"start":"end";if("top"===e)return nf(nf({},{x:f+h/2,y:g-j*d,textAnchor:"middle",verticalAnchor:l}),c?{height:Math.max(g-c.y,0),width:h}:{});if("bottom"===e)return nf(nf({},{x:f+h/2,y:g+i+k,textAnchor:"middle",verticalAnchor:m}),c?{height:Math.max(c.y+c.height-(g+i),0),width:h}:{});if("left"===e){var r={x:f-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"};return nf(nf({},r),c?{width:Math.max(r.x-c.x,0),height:i}:{})}if("right"===e){var s={x:f+h+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"};return nf(nf({},s),c?{width:Math.max(c.x+c.width-s.x,0),height:i}:{})}var t=c?{width:h,height:i}:{};return"insideLeft"===e?nf({x:f+o,y:g+i/2,textAnchor:q,verticalAnchor:"middle"},t):"insideRight"===e?nf({x:f+h-o,y:g+i/2,textAnchor:p,verticalAnchor:"middle"},t):"insideTop"===e?nf({x:f+h/2,y:g+k,textAnchor:"middle",verticalAnchor:m},t):"insideBottom"===e?nf({x:f+h/2,y:g+i-k,textAnchor:"middle",verticalAnchor:l},t):"insideTopLeft"===e?nf({x:f+o,y:g+k,textAnchor:q,verticalAnchor:m},t):"insideTopRight"===e?nf({x:f+h-o,y:g+k,textAnchor:p,verticalAnchor:m},t):"insideBottomLeft"===e?nf({x:f+o,y:g+i-k,textAnchor:q,verticalAnchor:l},t):"insideBottomRight"===e?nf({x:f+h-o,y:g+i-k,textAnchor:p,verticalAnchor:l},t):e&&"object"==typeof e&&(y(e.x)||x(e.x))&&(y(e.y)||x(e.y))?nf({x:f+C(e.x,h),y:g+C(e.y,i),textAnchor:"end",verticalAnchor:"end"},t):nf({x:f+h/2,y:g+i/2,textAnchor:"middle",verticalAnchor:"middle"},t)})(d,o);return h.createElement(na,ng({ref:m,className:(0,q.$)("recharts-label",k)},t,u,{breakAll:l}),b)}ni.displayName="Label";var nj=a=>{var{cx:b,cy:c,angle:d,startAngle:e,endAngle:f,r:g,radius:h,innerRadius:i,outerRadius:j,x:k,y:l,top:m,left:n,width:o,height:p,clockWise:q,labelViewBox:r}=a;if(r)return r;if(y(o)&&y(p)){if(y(k)&&y(l))return{x:k,y:l,width:o,height:p};if(y(m)&&y(n))return{x:m,y:n,width:o,height:p}}return y(k)&&y(l)?{x:k,y:l,width:0,height:0}:y(b)&&y(c)?{cx:b,cy:c,startAngle:e||d||0,endAngle:f||d||0,innerRadius:i||0,outerRadius:j||h||g||0,clockWise:q}:a.viewBox?a.viewBox:void 0};ni.parseViewBox=nj,ni.renderCallByParent=function(a,b){var c=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&c&&!a.label)return null;var{children:d,labelRef:e}=a,f=nj(a),g=l5(d,ni).map((a,c)=>(0,h.cloneElement)(a,{viewBox:b||f,key:"label-".concat(c)}));return c?[((a,b,c)=>{if(!a)return null;var d={viewBox:b,labelRef:c};return!0===a?h.createElement(ni,ng({key:"label-implicit"},d)):z(a)?h.createElement(ni,ng({key:"label-implicit",value:a},d)):(0,h.isValidElement)(a)?a.type===ni?(0,h.cloneElement)(a,nf({key:"label-implicit"},d)):h.createElement(ni,ng({key:"label-implicit",content:a},d)):nh(a)?h.createElement(ni,ng({key:"label-implicit",content:a},d)):a&&"object"==typeof a?h.createElement(ni,ng({},a,{key:"label-implicit"},d)):null})(a.label,b||f,e),...g]:g};var nk=["viewBox"],nl=["viewBox"];function nm(){return(nm=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nn(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function no(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nn(Object(c),!0).forEach(function(b){nq(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nn(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function np(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function nq(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class nr extends h.Component{constructor(a){super(a),this.tickRefs=h.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(a,b){var{viewBox:c}=a,d=np(a,nk),e=this.props,{viewBox:f}=e,g=np(e,nl);return!mQ(c,f)||!mQ(d,g)||!mQ(b,this.state)}getTickLineCoord(a){var b,c,d,e,f,g,{x:h,y:i,width:j,height:k,orientation:l,tickSize:m,mirror:n,tickMargin:o}=this.props,p=n?-1:1,q=a.tickSize||m,r=y(a.tickCoord)?a.tickCoord:a.coordinate;switch(l){case"top":b=c=a.coordinate,g=(d=(e=i+!n*k)-p*q)-p*o,f=r;break;case"left":d=e=a.coordinate,f=(b=(c=h+!n*j)-p*q)-p*o,g=r;break;case"right":d=e=a.coordinate,f=(b=(c=h+n*j)+p*q)+p*o,g=r;break;default:b=c=a.coordinate,g=(d=(e=i+n*k)+p*q)+p*o,f=r}return{line:{x1:b,y1:d,x2:c,y2:e},tick:{x:f,y:g}}}getTickTextAnchor(){var a,{orientation:b,mirror:c}=this.props;switch(b){case"left":a=c?"start":"end";break;case"right":a=c?"end":"start";break;default:a="middle"}return a}getTickVerticalAnchor(){var{orientation:a,mirror:b}=this.props;switch(a){case"left":case"right":return"middle";case"top":return b?"start":"end";default:return b?"end":"start"}}renderAxisLine(){var{x:a,y:b,width:c,height:d,orientation:e,mirror:f,axisLine:g}=this.props,i=no(no(no({},l7(this.props,!1)),l7(g,!1)),{},{fill:"none"});if("top"===e||"bottom"===e){var j=+("top"===e&&!f||"bottom"===e&&f);i=no(no({},i),{},{x1:a,y1:b+j*d,x2:a+c,y2:b+j*d})}else{var k=+("left"===e&&!f||"right"===e&&f);i=no(no({},i),{},{x1:a+k*c,y1:b,x2:a+k*c,y2:b+d})}return h.createElement("line",nm({},i,{className:(0,q.$)("recharts-cartesian-axis-line",u()(g,"className"))}))}static renderTickItem(a,b,c){var d,e=(0,q.$)(b.className,"recharts-cartesian-axis-tick-value");if(h.isValidElement(a))d=h.cloneElement(a,no(no({},b),{},{className:e}));else if("function"==typeof a)d=a(no(no({},b),{},{className:e}));else{var f="recharts-cartesian-axis-tick-value";"boolean"!=typeof a&&(f=(0,q.$)(f,a.className)),d=h.createElement(na,nm({},b,{className:f}),c)}return d}renderTicks(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:d,stroke:e,tick:f,tickFormatter:g,unit:i}=this.props,j=mP(no(no({},this.props),{},{ticks:c}),a,b),k=this.getTickTextAnchor(),l=this.getTickVerticalAnchor(),m=l7(this.props,!1),n=l7(f,!1),o=no(no({},m),{},{fill:"none"},l7(d,!1)),p=j.map((a,b)=>{var{line:c,tick:p}=this.getTickLineCoord(a),r=no(no(no(no({textAnchor:k,verticalAnchor:l},m),{},{stroke:"none",fill:e},n),p),{},{index:b,payload:a,visibleTicksCount:j.length,tickFormatter:g});return h.createElement(mT,nm({className:"recharts-cartesian-axis-tick",key:"tick-".concat(a.value,"-").concat(a.coordinate,"-").concat(a.tickCoord)},l0(this.props,a,b)),d&&h.createElement("line",nm({},o,c,{className:(0,q.$)("recharts-cartesian-axis-tick-line",u()(d,"className"))})),f&&nr.renderTickItem(f,r,"".concat("function"==typeof g?g(a.value,b):a.value).concat(i||"")))});return p.length>0?h.createElement("g",{className:"recharts-cartesian-axis-ticks"},p):null}render(){var{axisLine:a,width:b,height:c,className:d,hide:e}=this.props;if(e)return null;var{ticks:f}=this.props;return null!=b&&b<=0||null!=c&&c<=0?null:h.createElement(mT,{className:(0,q.$)("recharts-cartesian-axis",d),ref:a=>{if(a){var b=a.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(b);var c=b[0];if(c){var d=window.getComputedStyle(c).fontSize,e=window.getComputedStyle(c).letterSpacing;(d!==this.state.fontSize||e!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(c).fontSize,letterSpacing:window.getComputedStyle(c).letterSpacing})}}}},a&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,f),ni.renderCallByParent(this.props))}}nq(nr,"displayName","CartesianAxis"),nq(nr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var ns=["x1","y1","x2","y2","key"],nt=["offset"],nu=["xAxisId","yAxisId"],nv=["xAxisId","yAxisId"];function nw(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function nx(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?nw(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):nw(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function ny(){return(ny=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nz(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var nA=a=>{var{fill:b}=a;if(!b||"none"===b)return null;var{fillOpacity:c,x:d,y:e,width:f,height:g,ry:i}=a;return h.createElement("rect",{x:d,y:e,ry:i,width:f,height:g,stroke:"none",fill:b,fillOpacity:c,className:"recharts-cartesian-grid-bg"})};function nB(a,b){var c;if(h.isValidElement(a))c=h.cloneElement(a,b);else if("function"==typeof a)c=a(b);else{var{x1:d,y1:e,x2:f,y2:g,key:i}=b,j=l7(nz(b,ns),!1),{offset:k}=j,l=nz(j,nt);c=h.createElement("line",ny({},l,{x1:d,y1:e,x2:f,y2:g,fill:"none",key:i}))}return c}function nC(a){var{x:b,width:c,horizontal:d=!0,horizontalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:f,yAxisId:g}=a,i=nz(a,nu),j=e.map((a,e)=>nB(d,nx(nx({},i),{},{x1:b,y1:a,x2:b+c,y2:a,key:"line-".concat(e),index:e})));return h.createElement("g",{className:"recharts-cartesian-grid-horizontal"},j)}function nD(a){var{y:b,height:c,vertical:d=!0,verticalPoints:e}=a;if(!d||!e||!e.length)return null;var{xAxisId:f,yAxisId:g}=a,i=nz(a,nv),j=e.map((a,e)=>nB(d,nx(nx({},i),{},{x1:a,y1:b,x2:a,y2:b+c,key:"line-".concat(e),index:e})));return h.createElement("g",{className:"recharts-cartesian-grid-vertical"},j)}function nE(a){var{horizontalFill:b,fillOpacity:c,x:d,y:e,width:f,height:g,horizontalPoints:i,horizontal:j=!0}=a;if(!j||!b||!b.length)return null;var k=i.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==k[0]&&k.unshift(0);var l=k.map((a,i)=>{var j=k[i+1]?k[i+1]-a:e+g-a;if(j<=0)return null;var l=i%b.length;return h.createElement("rect",{key:"react-".concat(i),y:a,x:d,height:j,width:f,stroke:"none",fill:b[l],fillOpacity:c,className:"recharts-cartesian-grid-bg"})});return h.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function nF(a){var{vertical:b=!0,verticalFill:c,fillOpacity:d,x:e,y:f,width:g,height:i,verticalPoints:j}=a;if(!b||!c||!c.length)return null;var k=j.map(a=>Math.round(a+e-e)).sort((a,b)=>a-b);e!==k[0]&&k.unshift(0);var l=k.map((a,b)=>{var j=k[b+1]?k[b+1]-a:e+g-a;if(j<=0)return null;var l=b%c.length;return h.createElement("rect",{key:"react-".concat(b),x:a,y:f,width:j,height:i,stroke:"none",fill:c[l],fillOpacity:d,className:"recharts-cartesian-grid-bg"})});return h.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var nG=(a,b)=>{var{xAxis:c,width:d,height:e,offset:f}=a;return cF(mP(nx(nx(nx({},nr.defaultProps),c),{},{ticks:cG(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.left,f.left+f.width,b)},nH=(a,b)=>{var{yAxis:c,width:d,height:e,offset:f}=a;return cF(mP(nx(nx(nx({},nr.defaultProps),c),{},{ticks:cG(c,!0),viewBox:{x:0,y:0,width:d,height:e}})),f.top,f.top+f.height,b)},nI={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function nJ(a){var b=c7(),c=c8(),d=c6(),e=nx(nx({},mv(a,nI)),{},{x:y(a.x)?a.x:d.left,y:y(a.y)?a.y:d.top,width:y(a.width)?a.width:d.width,height:y(a.height)?a.height:d.height}),{xAxisId:f,yAxisId:g,x:i,y:j,width:k,height:l,syncWithTicks:m,horizontalValues:n,verticalValues:o}=e,p=c1(),q=cm(a=>jy(a,"xAxis",f,p)),r=cm(a=>jy(a,"yAxis",g,p));if(!y(k)||k<=0||!y(l)||l<=0||!y(i)||i!==+i||!y(j)||j!==+j)return null;var s=e.verticalCoordinatesGenerator||nG,t=e.horizontalCoordinatesGenerator||nH,{horizontalPoints:u,verticalPoints:v}=e;if((!u||!u.length)&&"function"==typeof t){var w=n&&n.length,x=t({yAxis:r?nx(nx({},r),{},{ticks:w?n:r.ticks}):void 0,width:b,height:c,offset:d},!!w||m);H(Array.isArray(x),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof x,"]")),Array.isArray(x)&&(u=x)}if((!v||!v.length)&&"function"==typeof s){var z=o&&o.length,A=s({xAxis:q?nx(nx({},q),{},{ticks:z?o:q.ticks}):void 0,width:b,height:c,offset:d},!!z||m);H(Array.isArray(A),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof A,"]")),Array.isArray(A)&&(v=A)}return h.createElement("g",{className:"recharts-cartesian-grid"},h.createElement(nA,{fill:e.fill,fillOpacity:e.fillOpacity,x:e.x,y:e.y,width:e.width,height:e.height,ry:e.ry}),h.createElement(nE,ny({},e,{horizontalPoints:u})),h.createElement(nF,ny({},e,{verticalPoints:v})),h.createElement(nC,ny({},e,{offset:d,horizontalPoints:u,xAxis:q,yAxis:r})),h.createElement(nD,ny({},e,{offset:d,verticalPoints:v,xAxis:q,yAxis:r})))}nJ.displayName="CartesianGrid";var nK=["children"],nL=["dangerouslySetInnerHTML","ticks"];function nM(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function nN(){return(nN=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nO(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function nP(a){ci();var b=(0,h.useMemo)(()=>{var{children:b}=a;return nO(a,nK)},[a]),c=cm(a=>id(a,b.id));return b===c?a.children:null}var nQ=a=>{var{xAxisId:b,className:c}=a,d=cm(c_),e=c1(),f="xAxis",g=cm(a=>ji(a,f,b,e)),i=cm(a=>jz(a,f,b,e)),j=cm(a=>jp(a,b)),k=cm(a=>((a,b)=>{var c=cZ(a),d=id(a,b);if(null!=d){var e=jq(a,d.orientation,d.mirror)[b];return null==e?{x:c.left,y:0}:{x:c.left,y:e}}})(a,b));if(null==j||null==k)return null;var{dangerouslySetInnerHTML:l,ticks:m}=a,n=nO(a,nL);return h.createElement(nr,nN({},n,{scale:g,x:k.x,y:k.y,width:j.width,height:j.height,className:(0,q.$)("recharts-".concat(f," ").concat(f),c),viewBox:d,ticks:i}))},nR=a=>{var b,c,d,e,f;return h.createElement(nP,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.xAxisId,scale:a.scale,type:a.type,padding:a.padding,allowDataOverflow:a.allowDataOverflow,domain:a.domain,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,height:a.height,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(f=a.tick)||f,tickFormatter:a.tickFormatter},h.createElement(nQ,a))};class nS extends h.Component{render(){return h.createElement(nR,this.props)}}nM(nS,"displayName","XAxis"),nM(nS,"defaultProps",{allowDataOverflow:ic.allowDataOverflow,allowDecimals:ic.allowDecimals,allowDuplicatedCategory:ic.allowDuplicatedCategory,height:ic.height,hide:!1,mirror:ic.mirror,orientation:ic.orientation,padding:ic.padding,reversed:ic.reversed,scale:ic.scale,tickCount:ic.tickCount,type:ic.type,xAxisId:0});var nT=["dangerouslySetInnerHTML","ticks"];function nU(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function nV(){return(nV=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function nW(a){return ci(),null}var nX=a=>{var b,{yAxisId:c,className:d,width:e,label:f}=a,g=(0,h.useRef)(null),i=(0,h.useRef)(null),j=cm(c_),k=c1(),l=ci(),m="yAxis",n=cm(a=>ji(a,m,c,k)),o=cm(a=>js(a,c)),p=cm(a=>((a,b)=>{var c=cZ(a),d=ig(a,b);if(null!=d){var e=jr(a,d.orientation,d.mirror)[b];return null==e?{x:0,y:c.top}:{x:e,y:c.top}}})(a,c)),r=cm(a=>jz(a,m,c,k));if((0,h.useLayoutEffect)(()=>{if(!("auto"!==e||!o||nh(f)||(0,h.isValidElement)(f))){var a,b=g.current,d=null==b||null==(a=b.tickRefs)?void 0:a.current,{tickSize:j,tickMargin:k}=b.props,m=(a=>{var{ticks:b,label:c,labelGapWithTick:d=5,tickSize:e=0,tickMargin:f=0}=a,g=0;if(b){b.forEach(a=>{if(a){var b=a.getBoundingClientRect();b.width>g&&(g=b.width)}});var h=c?c.getBoundingClientRect().width:0;return Math.round(g+(e+f)+h+(c?d:0))}return 0})({ticks:d,label:i.current,labelGapWithTick:5,tickSize:j,tickMargin:k});Math.round(o.width)!==Math.round(m)&&l(k1({id:c,width:m}))}},[g,null==g||null==(b=g.current)||null==(b=b.tickRefs)?void 0:b.current,null==o?void 0:o.width,o,l,f,c,e]),null==o||null==p)return null;var{dangerouslySetInnerHTML:s,ticks:t}=a,u=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,nT);return h.createElement(nr,nV({},u,{ref:g,labelRef:i,scale:n,x:p.x,y:p.y,width:o.width,height:o.height,className:(0,q.$)("recharts-".concat(m," ").concat(m),d),viewBox:j,ticks:r}))},nY=a=>{var b,c,d,e,f;return h.createElement(h.Fragment,null,h.createElement(nW,{interval:null!=(b=a.interval)?b:"preserveEnd",id:a.yAxisId,scale:a.scale,type:a.type,domain:a.domain,allowDataOverflow:a.allowDataOverflow,dataKey:a.dataKey,allowDuplicatedCategory:a.allowDuplicatedCategory,allowDecimals:a.allowDecimals,tickCount:a.tickCount,padding:a.padding,includeHidden:null!=(c=a.includeHidden)&&c,reversed:a.reversed,ticks:a.ticks,width:a.width,orientation:a.orientation,mirror:a.mirror,hide:a.hide,unit:a.unit,name:a.name,angle:null!=(d=a.angle)?d:0,minTickGap:null!=(e=a.minTickGap)?e:5,tick:null==(f=a.tick)||f,tickFormatter:a.tickFormatter}),h.createElement(nX,a))},nZ={allowDataOverflow:ie.allowDataOverflow,allowDecimals:ie.allowDecimals,allowDuplicatedCategory:ie.allowDuplicatedCategory,hide:!1,mirror:ie.mirror,orientation:ie.orientation,padding:ie.padding,reversed:ie.reversed,scale:ie.scale,tickCount:ie.tickCount,type:ie.type,width:ie.width,yAxisId:0};class n$ extends h.Component{render(){return h.createElement(nY,this.props)}}nU(n$,"displayName","YAxis"),nU(n$,"defaultProps",nZ);var n_=c(51215);function n0(){return(n0=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function n1(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n2(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n1(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n1(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function n3(a){return Array.isArray(a)&&z(a[0])&&z(a[1])?a.join(" ~ "):a}var n4=a=>{var{separator:b=" : ",contentStyle:c={},itemStyle:d={},labelStyle:e={},payload:f,formatter:g,itemSorter:i,wrapperClassName:j,labelClassName:k,label:l,labelFormatter:m,accessibilityLayer:n=!1}=a,o=n2({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},c),p=n2({margin:0},e),r=null!=l,s=r?l:"",t=(0,q.$)("recharts-default-tooltip",j),u=(0,q.$)("recharts-tooltip-label",k);return r&&m&&null!=f&&(s=m(l,f)),h.createElement("div",n0({className:t,style:o},n?{role:"status","aria-live":"assertive"}:{}),h.createElement("p",{className:u,style:p},h.isValidElement(s)?s:"".concat(s)),(()=>{if(f&&f.length){var a=(i?co()(f,i):f).map((a,c)=>{if("none"===a.type)return null;var e=a.formatter||g||n3,{value:i,name:j}=a,k=i,l=j;if(e){var m=e(i,j,a,c,f);if(Array.isArray(m))[k,l]=m;else{if(null==m)return null;k=m}}var n=n2({display:"block",paddingTop:4,paddingBottom:4,color:a.color||"#000"},d);return h.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(c),style:n},z(l)?h.createElement("span",{className:"recharts-tooltip-item-name"},l):null,z(l)?h.createElement("span",{className:"recharts-tooltip-item-separator"},b):null,h.createElement("span",{className:"recharts-tooltip-item-value"},k),h.createElement("span",{className:"recharts-tooltip-item-unit"},a.unit||""))});return h.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},a)}return null})())},n5="recharts-tooltip-wrapper",n6={visibility:"hidden"};function n7(a){var{allowEscapeViewBox:b,coordinate:c,key:d,offsetTopLeft:e,position:f,reverseDirection:g,tooltipDimension:h,viewBox:i,viewBoxDimension:j}=a;if(f&&y(f[d]))return f[d];var k=c[d]-h-(e>0?e:0),l=c[d]+e;if(b[d])return g[d]?k:l;var m=i[d];return null==m?0:g[d]?k<m?Math.max(l,m):Math.max(k,m):null==j?0:l+h>m+j?Math.max(k,m):Math.max(l,m)}function n8(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n9(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?n8(Object(c),!0).forEach(function(b){oa(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):n8(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function oa(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class ob extends h.PureComponent{constructor(){super(...arguments),oa(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),oa(this,"handleKeyDown",a=>{if("Escape"===a.key){var b,c,d,e;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(b=null==(c=this.props.coordinate)?void 0:c.x)?b:0,y:null!=(d=null==(e=this.props.coordinate)?void 0:e.y)?d:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var a,b;this.state.dismissed&&((null==(a=this.props.coordinate)?void 0:a.x)!==this.state.dismissedAtCoordinate.x||(null==(b=this.props.coordinate)?void 0:b.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:a,allowEscapeViewBox:b,animationDuration:c,animationEasing:d,children:e,coordinate:f,hasPayload:g,isAnimationActive:i,offset:j,position:k,reverseDirection:l,useTranslate3d:m,viewBox:n,wrapperStyle:o,lastBoundingBox:p,innerRef:r,hasPortalFromProps:s}=this.props,{cssClasses:t,cssProperties:u}=function(a){var b,c,d,{allowEscapeViewBox:e,coordinate:f,offsetTopLeft:g,position:h,reverseDirection:i,tooltipBox:j,useTranslate3d:k,viewBox:l}=a;return{cssProperties:b=j.height>0&&j.width>0&&f?function(a){var{translateX:b,translateY:c,useTranslate3d:d}=a;return{transform:d?"translate3d(".concat(b,"px, ").concat(c,"px, 0)"):"translate(".concat(b,"px, ").concat(c,"px)")}}({translateX:c=n7({allowEscapeViewBox:e,coordinate:f,key:"x",offsetTopLeft:g,position:h,reverseDirection:i,tooltipDimension:j.width,viewBox:l,viewBoxDimension:l.width}),translateY:d=n7({allowEscapeViewBox:e,coordinate:f,key:"y",offsetTopLeft:g,position:h,reverseDirection:i,tooltipDimension:j.height,viewBox:l,viewBoxDimension:l.height}),useTranslate3d:k}):n6,cssClasses:function(a){var{coordinate:b,translateX:c,translateY:d}=a;return(0,q.$)(n5,{["".concat(n5,"-right")]:y(c)&&b&&y(b.x)&&c>=b.x,["".concat(n5,"-left")]:y(c)&&b&&y(b.x)&&c<b.x,["".concat(n5,"-bottom")]:y(d)&&b&&y(b.y)&&d>=b.y,["".concat(n5,"-top")]:y(d)&&b&&y(b.y)&&d<b.y})}({translateX:c,translateY:d,coordinate:f})}}({allowEscapeViewBox:b,coordinate:f,offsetTopLeft:j,position:k,reverseDirection:l,tooltipBox:{height:p.height,width:p.width},useTranslate3d:m,viewBox:n}),v=s?{}:n9(n9({transition:i&&a?"transform ".concat(c,"ms ").concat(d):void 0},u),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&g?"visible":"hidden",position:"absolute",top:0,left:0}),w=n9(n9({},v),{},{visibility:!this.state.dismissed&&a&&g?"visible":"hidden"},o);return h.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:t,style:w,ref:r},e)}}var oc=c(23854),od=c.n(oc);function oe(){}function of(a,b,c){a._context.bezierCurveTo((2*a._x0+a._x1)/3,(2*a._y0+a._y1)/3,(a._x0+2*a._x1)/3,(a._y0+2*a._y1)/3,(a._x0+4*a._x1+b)/6,(a._y0+4*a._y1+c)/6)}function og(a){this._context=a}function oh(a){this._context=a}function oi(a){this._context=a}og.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:of(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:of(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},oh.prototype={areaStart:oe,areaEnd:oe,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._x2=a,this._y2=b;break;case 1:this._point=2,this._x3=a,this._y3=b;break;case 2:this._point=3,this._x4=a,this._y4=b,this._context.moveTo((this._x0+4*this._x1+a)/6,(this._y0+4*this._y1+b)/6);break;default:of(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},oi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var c=(this._x0+4*this._x1+a)/6,d=(this._y0+4*this._y1+b)/6;this._line?this._context.lineTo(c,d):this._context.moveTo(c,d);break;case 3:this._point=4;default:of(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}};class oj{constructor(a,b){this._context=a,this._x=b}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+a)/2,this._y0,this._x0,b,a,b):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+b)/2,a,this._y0,a,b)}this._x0=a,this._y0=b}}function ok(a){this._context=a}function ol(a){this._context=a}function om(a){return new ol(a)}ok.prototype={areaStart:oe,areaEnd:oe,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(a,b){a*=1,b*=1,this._point?this._context.lineTo(a,b):(this._point=1,this._context.moveTo(a,b))}};function on(a,b,c){var d=a._x1-a._x0,e=b-a._x1,f=(a._y1-a._y0)/(d||e<0&&-0),g=(c-a._y1)/(e||d<0&&-0);return((f<0?-1:1)+(g<0?-1:1))*Math.min(Math.abs(f),Math.abs(g),.5*Math.abs((f*e+g*d)/(d+e)))||0}function oo(a,b){var c=a._x1-a._x0;return c?(3*(a._y1-a._y0)/c-b)/2:b}function op(a,b,c){var d=a._x0,e=a._y0,f=a._x1,g=a._y1,h=(f-d)/3;a._context.bezierCurveTo(d+h,e+h*b,f-h,g-h*c,f,g)}function oq(a){this._context=a}function or(a){this._context=new os(a)}function os(a){this._context=a}function ot(a){this._context=a}function ou(a){var b,c,d=a.length-1,e=Array(d),f=Array(d),g=Array(d);for(e[0]=0,f[0]=2,g[0]=a[0]+2*a[1],b=1;b<d-1;++b)e[b]=1,f[b]=4,g[b]=4*a[b]+2*a[b+1];for(e[d-1]=2,f[d-1]=7,g[d-1]=8*a[d-1]+a[d],b=1;b<d;++b)c=e[b]/f[b-1],f[b]-=c,g[b]-=c*g[b-1];for(e[d-1]=g[d-1]/f[d-1],b=d-2;b>=0;--b)e[b]=(g[b]-e[b+1])/f[b];for(b=0,f[d-1]=(a[d]+e[d-1])/2;b<d-1;++b)f[b]=2*a[b+1]-e[b+1];return[e,f]}function ov(a,b){this._context=a,this._t=b}ol.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._context.lineTo(a,b)}}},oq.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:op(this,this._t0,oo(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){var c=NaN;if(b*=1,(a*=1)!==this._x1||b!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,op(this,oo(this,c=on(this,a,b)),c);break;default:op(this,this._t0,c=on(this,a,b))}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b,this._t0=c}}},(or.prototype=Object.create(oq.prototype)).point=function(a,b){oq.prototype.point.call(this,b,a)},os.prototype={moveTo:function(a,b){this._context.moveTo(b,a)},closePath:function(){this._context.closePath()},lineTo:function(a,b){this._context.lineTo(b,a)},bezierCurveTo:function(a,b,c,d,e,f){this._context.bezierCurveTo(b,a,d,c,f,e)}},ot.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var a=this._x,b=this._y,c=a.length;if(c)if(this._line?this._context.lineTo(a[0],b[0]):this._context.moveTo(a[0],b[0]),2===c)this._context.lineTo(a[1],b[1]);else for(var d=ou(a),e=ou(b),f=0,g=1;g<c;++f,++g)this._context.bezierCurveTo(d[0][f],e[0][f],d[1][f],e[1][f],a[g],b[g]);(this._line||0!==this._line&&1===c)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(a,b){this._x.push(+a),this._y.push(+b)}},ov.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,b),this._context.lineTo(a,b);else{var c=this._x*(1-this._t)+a*this._t;this._context.lineTo(c,this._y),this._context.lineTo(c,b)}}this._x=a,this._y=b}};let ow=Math.PI,ox=2*ow,oy=ox-1e-6;function oz(a){this._+=a[0];for(let b=1,c=a.length;b<c;++b)this._+=arguments[b]+a[b]}class oA{constructor(a){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==a?oz:function(a){let b=Math.floor(a);if(!(b>=0))throw Error(`invalid digits: ${a}`);if(b>15)return oz;let c=10**b;return function(a){this._+=a[0];for(let b=1,d=a.length;b<d;++b)this._+=Math.round(arguments[b]*c)/c+a[b]}}(a)}moveTo(a,b){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(a,b){this._append`L${this._x1=+a},${this._y1=+b}`}quadraticCurveTo(a,b,c,d){this._append`Q${+a},${+b},${this._x1=+c},${this._y1=+d}`}bezierCurveTo(a,b,c,d,e,f){this._append`C${+a},${+b},${+c},${+d},${this._x1=+e},${this._y1=+f}`}arcTo(a,b,c,d,e){if(a*=1,b*=1,c*=1,d*=1,(e*=1)<0)throw Error(`negative radius: ${e}`);let f=this._x1,g=this._y1,h=c-a,i=d-b,j=f-a,k=g-b,l=j*j+k*k;if(null===this._x1)this._append`M${this._x1=a},${this._y1=b}`;else if(l>1e-6)if(Math.abs(k*h-i*j)>1e-6&&e){let m=c-f,n=d-g,o=h*h+i*i,p=Math.sqrt(o),q=Math.sqrt(l),r=e*Math.tan((ow-Math.acos((o+l-(m*m+n*n))/(2*p*q)))/2),s=r/q,t=r/p;Math.abs(s-1)>1e-6&&this._append`L${a+s*j},${b+s*k}`,this._append`A${e},${e},0,0,${+(k*m>j*n)},${this._x1=a+t*h},${this._y1=b+t*i}`}else this._append`L${this._x1=a},${this._y1=b}`}arc(a,b,c,d,e,f){if(a*=1,b*=1,c*=1,f=!!f,c<0)throw Error(`negative radius: ${c}`);let g=c*Math.cos(d),h=c*Math.sin(d),i=a+g,j=b+h,k=1^f,l=f?d-e:e-d;null===this._x1?this._append`M${i},${j}`:(Math.abs(this._x1-i)>1e-6||Math.abs(this._y1-j)>1e-6)&&this._append`L${i},${j}`,c&&(l<0&&(l=l%ox+ox),l>oy?this._append`A${c},${c},0,1,${k},${a-g},${b-h}A${c},${c},0,1,${k},${this._x1=i},${this._y1=j}`:l>1e-6&&this._append`A${c},${c},0,${+(l>=ow)},${k},${this._x1=a+c*Math.cos(e)},${this._y1=b+c*Math.sin(e)}`)}rect(a,b,c,d){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}h${c*=1}v${+d}h${-c}Z`}toString(){return this._}}function oB(a){let b=3;return a.digits=function(c){if(!arguments.length)return b;if(null==c)b=null;else{let a=Math.floor(c);if(!(a>=0))throw RangeError(`invalid digits: ${c}`);b=a}return a},()=>new oA(b)}function oC(a){return a[0]}function oD(a){return a[1]}function oE(a,b){var c=cs(!0),d=null,e=om,f=null,g=oB(h);function h(h){var i,j,k,l=(h=cr(h)).length,m=!1;for(null==d&&(f=e(k=g())),i=0;i<=l;++i)!(i<l&&c(j=h[i],i,h))===m&&((m=!m)?f.lineStart():f.lineEnd()),m&&f.point(+a(j,i,h),+b(j,i,h));if(k)return f=null,k+""||null}return a="function"==typeof a?a:void 0===a?oC:cs(a),b="function"==typeof b?b:void 0===b?oD:cs(b),h.x=function(b){return arguments.length?(a="function"==typeof b?b:cs(+b),h):a},h.y=function(a){return arguments.length?(b="function"==typeof a?a:cs(+a),h):b},h.defined=function(a){return arguments.length?(c="function"==typeof a?a:cs(!!a),h):c},h.curve=function(a){return arguments.length?(e=a,null!=d&&(f=e(d)),h):e},h.context=function(a){return arguments.length?(null==a?d=f=null:f=e(d=a),h):d},h}function oF(a,b,c){var d=null,e=cs(!0),f=null,g=om,h=null,i=oB(j);function j(j){var k,l,m,n,o,p=(j=cr(j)).length,q=!1,r=Array(p),s=Array(p);for(null==f&&(h=g(o=i())),k=0;k<=p;++k){if(!(k<p&&e(n=j[k],k,j))===q)if(q=!q)l=k,h.areaStart(),h.lineStart();else{for(h.lineEnd(),h.lineStart(),m=k-1;m>=l;--m)h.point(r[m],s[m]);h.lineEnd(),h.areaEnd()}q&&(r[k]=+a(n,k,j),s[k]=+b(n,k,j),h.point(d?+d(n,k,j):r[k],c?+c(n,k,j):s[k]))}if(o)return h=null,o+""||null}function k(){return oE().defined(e).curve(g).context(f)}return a="function"==typeof a?a:void 0===a?oC:cs(+a),b="function"==typeof b?b:void 0===b?cs(0):cs(+b),c="function"==typeof c?c:void 0===c?oD:cs(+c),j.x=function(b){return arguments.length?(a="function"==typeof b?b:cs(+b),d=null,j):a},j.x0=function(b){return arguments.length?(a="function"==typeof b?b:cs(+b),j):a},j.x1=function(a){return arguments.length?(d=null==a?null:"function"==typeof a?a:cs(+a),j):d},j.y=function(a){return arguments.length?(b="function"==typeof a?a:cs(+a),c=null,j):b},j.y0=function(a){return arguments.length?(b="function"==typeof a?a:cs(+a),j):b},j.y1=function(a){return arguments.length?(c=null==a?null:"function"==typeof a?a:cs(+a),j):c},j.lineX0=j.lineY0=function(){return k().x(a).y(b)},j.lineY1=function(){return k().x(a).y(c)},j.lineX1=function(){return k().x(d).y(b)},j.defined=function(a){return arguments.length?(e="function"==typeof a?a:cs(!!a),j):e},j.curve=function(a){return arguments.length?(g=a,null!=f&&(h=g(f)),j):g},j.context=function(a){return arguments.length?(null==a?f=h=null:h=g(f=a),j):f},j}function oG(){return(oG=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function oH(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function oI(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oH(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oH(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}oA.prototype;var oJ={curveBasisClosed:function(a){return new oh(a)},curveBasisOpen:function(a){return new oi(a)},curveBasis:function(a){return new og(a)},curveBumpX:function(a){return new oj(a,!0)},curveBumpY:function(a){return new oj(a,!1)},curveLinearClosed:function(a){return new ok(a)},curveLinear:om,curveMonotoneX:function(a){return new oq(a)},curveMonotoneY:function(a){return new or(a)},curveNatural:function(a){return new ot(a)},curveStep:function(a){return new ov(a,.5)},curveStepAfter:function(a){return new ov(a,1)},curveStepBefore:function(a){return new ov(a,0)}},oK=a=>gU(a.x)&&gU(a.y),oL=a=>a.x,oM=a=>a.y,oN=a=>{var{className:b,points:c,path:d,pathRef:e}=a;if((!c||!c.length)&&!d)return null;var f=c&&c.length?(a=>{var b,{type:c="linear",points:d=[],baseLine:e,layout:f,connectNulls:g=!1}=a,h=((a,b)=>{if("function"==typeof a)return a;var c="curve".concat(G(a));return("curveMonotone"===c||"curveBump"===c)&&b?oJ["".concat(c).concat("vertical"===b?"Y":"X")]:oJ[c]||om})(c,f),i=g?d.filter(oK):d;if(Array.isArray(e)){var j=g?e.filter(a=>oK(a)):e,k=i.map((a,b)=>oI(oI({},a),{},{base:j[b]}));return(b="vertical"===f?oF().y(oM).x1(oL).x0(a=>a.base.x):oF().x(oL).y1(oM).y0(a=>a.base.y)).defined(oK).curve(h),b(k)}return(b="vertical"===f&&y(e)?oF().y(oM).x1(oL).x0(e):y(e)?oF().x(oL).y1(oM).y0(e):oE().x(oL).y(oM)).defined(oK).curve(h),b(i)})(a):d;return h.createElement("path",oG({},l7(a,!1),l_(a),{className:(0,q.$)("recharts-curve",b),d:null===f?void 0:f,ref:e}))},oO=["x","y","top","left","width","height","className"];function oP(){return(oP=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function oQ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var oR=a=>{var{x:b=0,y:c=0,top:d=0,left:e=0,width:f=0,height:g=0,className:i}=a,j=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oQ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oQ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({x:b,y:c,top:d,left:e,width:f,height:g},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,oO));return y(b)&&y(c)&&y(f)&&y(g)&&y(d)&&y(e)?h.createElement("path",oP({},l7(j,!0),{className:(0,q.$)("recharts-cross",i),d:"M".concat(b,",").concat(d,"v").concat(g,"M").concat(e,",").concat(c,"h").concat(f)})):null},oS=c(12728),oT=c.n(oS),oU=(a,b)=>[0,3*a,3*b-6*a,3*a-3*b+1],oV=(a,b)=>a.map((a,c)=>a*b**c).reduce((a,b)=>a+b),oW=(a,b)=>c=>oV(oU(a,b),c),oX=function(){let a,b;for(var c,d,e,f,g=arguments.length,h=Array(g),i=0;i<g;i++)h[i]=arguments[i];if(1===h.length)switch(h[0]){case"linear":[c,e,d,f]=[0,0,1,1];break;case"ease":[c,e,d,f]=[.25,.1,.25,1];break;case"ease-in":[c,e,d,f]=[.42,0,1,1];break;case"ease-out":[c,e,d,f]=[.42,0,.58,1];break;case"ease-in-out":[c,e,d,f]=[0,0,.58,1];break;default:var j=h[0].split("(");"cubic-bezier"===j[0]&&4===j[1].split(")")[0].split(",").length&&([c,e,d,f]=j[1].split(")")[0].split(",").map(a=>parseFloat(a)))}else 4===h.length&&([c,e,d,f]=h);var k=oW(c,d),l=oW(e,f),m=(a=c,b=d,c=>oV([...oU(a,b).map((a,b)=>a*b).slice(1),0],c)),n=a=>a>1?1:a<0?0:a,o=a=>{for(var b=a>1?1:a,c=b,d=0;d<8;++d){var e=k(c)-b,f=m(c);if(1e-4>Math.abs(e-b)||f<1e-4)break;c=n(c-e/f)}return l(c)};return o.isStepper=!1,o},oY=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:b=100,damping:c=8,dt:d=17}=a,e=(a,e,f)=>{var g=f+(-(a-e)*b-f*c)*d/1e3,h=f*d/1e3+a;return 1e-4>Math.abs(h-e)&&1e-4>Math.abs(g)?[e,0]:[h,g]};return e.isStepper=!0,e.dt=d,e};function oZ(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function o$(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?oZ(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):oZ(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var o_=(a,b)=>Object.keys(b).reduce((c,d)=>o$(o$({},c),{},{[d]:a(d,b[d])}),{});function o0(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function o1(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o0(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o0(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var o2=(a,b,c)=>a+(b-a)*c,o3=a=>{var{from:b,to:c}=a;return b!==c},o4=(a,b,c)=>{var d=o_((b,c)=>{if(o3(c)){var[d,e]=a(c.from,c.to,c.velocity);return o1(o1({},c),{},{from:d,velocity:e})}return c},b);return c<1?o_((a,b)=>o3(b)?o1(o1({},b),{},{velocity:o2(b.velocity,d[a].velocity,c),from:o2(b.from,d[a].from,c)}):b,b):o4(a,d,c-1)};class o5{setTimeout(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=performance.now(),d=null,e=f=>{f-c>=b?a(f):"function"==typeof requestAnimationFrame&&(d=requestAnimationFrame(e))};return d=requestAnimationFrame(e),()=>{cancelAnimationFrame(d)}}}var o6=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function o7(){return(o7=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function o8(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function o9(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o8(Object(c),!0).forEach(function(b){pa(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o8(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pa(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}class pb extends h.PureComponent{constructor(a,b){super(a,b),pa(this,"mounted",!1),pa(this,"manager",null),pa(this,"stopJSAnimation",null),pa(this,"unSubscribe",null);var{isActive:c,attributeName:d,from:e,to:f,children:g,duration:h,animationManager:i}=this.props;if(this.manager=i,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!c||h<=0){this.state={style:{}},"function"==typeof g&&(this.state={style:f});return}if(e){if("function"==typeof g){this.state={style:e};return}this.state={style:d?{[d]:e}:e}}else this.state={style:{}}}componentDidMount(){var{isActive:a,canBegin:b}=this.props;this.mounted=!0,a&&b&&this.runAnimation(this.props)}componentDidUpdate(a){var{isActive:b,canBegin:c,attributeName:d,shouldReAnimate:e,to:f,from:g}=this.props,{style:h}=this.state;if(c){if(!b){this.state&&h&&(d&&h[d]!==f||!d&&h!==f)&&this.setState({style:d?{[d]:f}:f});return}if(!oT()(a.to,f)||!a.canBegin||!a.isActive){var i=!a.canBegin||!a.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var j=i||e?g:a.to;this.state&&h&&(d&&h[d]!==j||!d&&h!==j)&&this.setState({style:d?{[d]:j}:j}),this.runAnimation(o9(o9({},this.props),{},{from:j,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:a}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}handleStyleChange(a){this.changeStyle(a)}changeStyle(a){this.mounted&&this.setState({style:a})}runJSAnimation(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,{from:A,to:B,duration:C,easing:D,begin:E,onAnimationEnd:F,onAnimationStart:G}=a,H=(w=(a=>{if("string"==typeof a)switch(a){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return oX(a);case"spring":return oY();default:if("cubic-bezier"===a.split("(")[0])return oX(a)}return"function"==typeof a?a:null})(D),x=this.changeStyle,y=this.manager.getTimeoutController(),z=[Object.keys(A),Object.keys(B)].reduce((a,b)=>a.filter(a=>b.includes(a))),!0===w.isStepper?(b=A,c=B,d=w,e=z,f=x,g=y,i=e.reduce((a,d)=>o1(o1({},a),{},{[d]:{from:b[d],velocity:0,to:c[d]}}),{}),j=null,k=a=>{h||(h=a);var e=(a-h)/d.dt;i=o4(d,i,e),f(o1(o1(o1({},b),c),o_((a,b)=>b.from,i))),h=a,Object.values(i).filter(o3).length&&(j=g.setTimeout(k))},()=>(j=g.setTimeout(k),()=>{j()})):(l=A,m=B,n=w,o=C,p=z,q=x,r=y,t=null,u=p.reduce((a,b)=>o1(o1({},a),{},{[b]:[l[b],m[b]]}),{}),v=a=>{s||(s=a);var b=(a-s)/o,c=o_((a,c)=>o2(...c,n(b)),u);if(q(o1(o1(o1({},l),m),c)),b<1)t=r.setTimeout(v);else{var d=o_((a,b)=>o2(...b,n(1)),u);q(o1(o1(o1({},l),m),d))}},()=>(t=r.setTimeout(v),()=>{t()}))),I=()=>{this.stopJSAnimation=H()};this.manager.start([G,E,I,C,F])}runAnimation(a){let b;var{begin:c,duration:d,attributeName:e,to:f,easing:g,onAnimationStart:h,onAnimationEnd:i,children:j}=a;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof g||"function"==typeof j||"spring"===g)return void this.runJSAnimation(a);var k=e?{[e]:f}:f,l=(b=Object.keys(k),b.map(a=>"".concat(a.replace(/([A-Z])/g,a=>"-".concat(a.toLowerCase()))," ").concat(d,"ms ").concat(g)).join(","));this.manager.start([h,c,o9(o9({},k),{},{transition:l}),d,i])}render(){var a=this.props,{children:b,begin:c,duration:d,attributeName:e,easing:f,isActive:g,from:i,to:j,canBegin:k,onAnimationEnd:l,shouldReAnimate:m,onAnimationReStart:n,animationManager:o}=a,p=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,o6),q=h.Children.count(b),r=this.state.style;if("function"==typeof b)return b(r);if(!g||0===q||d<=0)return b;var s=a=>{var{style:b={},className:c}=a.props;return(0,h.cloneElement)(a,o9(o9({},p),{},{style:o9(o9({},b),r),className:c}))};return 1===q?s(h.Children.only(b)):h.createElement("div",null,h.Children.map(b,a=>s(a)))}}pa(pb,"displayName","Animate"),pa(pb,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var pc=(0,h.createContext)(null);function pd(a){var b,c,d,e,f,g,i,j=(0,h.useContext)(pc);return h.createElement(pb,o7({},a,{animationManager:null!=(g=null!=(i=a.animationManager)?i:j)?g:(b=new o5,c=()=>null,d=!1,e=null,f=a=>{if(!d){if(Array.isArray(a)){if(!a.length)return;var[g,...h]=a;if("number"==typeof g){e=b.setTimeout(f.bind(null,h),g);return}f(g),e=b.setTimeout(f.bind(null,h));return}"object"==typeof a&&c(a),"function"==typeof a&&a()}},{stop:()=>{d=!0},start:a=>{d=!1,e&&(e(),e=null),f(a)},subscribe:a=>(c=a,()=>{c=()=>null}),getTimeoutController:()=>b})}))}function pe(){return(pe=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var pf=(a,b,c,d,e)=>{var f,g=Math.min(Math.abs(c)/2,Math.abs(d)/2),h=d>=0?1:-1,i=c>=0?1:-1,j=+(d>=0&&c>=0||d<0&&c<0);if(g>0&&e instanceof Array){for(var k=[0,0,0,0],l=0;l<4;l++)k[l]=e[l]>g?g:e[l];f="M".concat(a,",").concat(b+h*k[0]),k[0]>0&&(f+="A ".concat(k[0],",").concat(k[0],",0,0,").concat(j,",").concat(a+i*k[0],",").concat(b)),f+="L ".concat(a+c-i*k[1],",").concat(b),k[1]>0&&(f+="A ".concat(k[1],",").concat(k[1],",0,0,").concat(j,",\n        ").concat(a+c,",").concat(b+h*k[1])),f+="L ".concat(a+c,",").concat(b+d-h*k[2]),k[2]>0&&(f+="A ".concat(k[2],",").concat(k[2],",0,0,").concat(j,",\n        ").concat(a+c-i*k[2],",").concat(b+d)),f+="L ".concat(a+i*k[3],",").concat(b+d),k[3]>0&&(f+="A ".concat(k[3],",").concat(k[3],",0,0,").concat(j,",\n        ").concat(a,",").concat(b+d-h*k[3])),f+="Z"}else if(g>0&&e===+e&&e>0){var m=Math.min(g,e);f="M ".concat(a,",").concat(b+h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+i*m,",").concat(b,"\n            L ").concat(a+c-i*m,",").concat(b,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c,",").concat(b+h*m,"\n            L ").concat(a+c,",").concat(b+d-h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c-i*m,",").concat(b+d,"\n            L ").concat(a+i*m,",").concat(b+d,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a,",").concat(b+d-h*m," Z")}else f="M ".concat(a,",").concat(b," h ").concat(c," v ").concat(d," h ").concat(-c," Z");return f},pg={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ph=a=>{var b=mv(a,pg),c=(0,h.useRef)(null),[d,e]=(0,h.useState)(-1);(0,h.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:f,y:g,width:i,height:j,radius:k,className:l}=b,{animationEasing:m,animationDuration:n,animationBegin:o,isAnimationActive:p,isUpdateAnimationActive:r}=b;if(f!==+f||g!==+g||i!==+i||j!==+j||0===i||0===j)return null;var s=(0,q.$)("recharts-rectangle",l);return r?h.createElement(pd,{canBegin:d>0,from:{width:i,height:j,x:f,y:g},to:{width:i,height:j,x:f,y:g},duration:n,animationEasing:m,isActive:r},a=>{var{width:e,height:f,x:g,y:i}=a;return h.createElement(pd,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:o,duration:n,isActive:p,easing:m},h.createElement("path",pe({},l7(b,!0),{className:s,d:pf(g,i,e,f,k),ref:c})))}):h.createElement("path",pe({},l7(b,!0),{className:s,d:pf(f,g,i,j,k)}))};function pi(a){var{cx:b,cy:c,radius:d,startAngle:e,endAngle:f}=a;return{points:[cz(b,c,d,e),cz(b,c,d,f)],cx:b,cy:c,radius:d,startAngle:e,endAngle:f}}function pj(){return(pj=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var pk=a=>{var{cx:b,cy:c,radius:d,angle:e,sign:f,isExternal:g,cornerRadius:h,cornerIsExternal:i}=a,j=h*(g?1:-1)+d,k=Math.asin(h/j)/cy,l=i?e:e+f*k,m=cz(b,c,j,l);return{center:m,circleTangency:cz(b,c,d,l),lineTangency:cz(b,c,j*Math.cos(k*cy),i?e-f*k:e),theta:k}},pl=a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:f,endAngle:g}=a,h=((a,b)=>v(b-a)*Math.min(Math.abs(b-a),359.999))(f,g),i=f+h,j=cz(b,c,e,f),k=cz(b,c,e,i),l="M ".concat(j.x,",").concat(j.y,"\n    A ").concat(e,",").concat(e,",0,\n    ").concat(+(Math.abs(h)>180),",").concat(+(f>i),",\n    ").concat(k.x,",").concat(k.y,"\n  ");if(d>0){var m=cz(b,c,d,f),n=cz(b,c,d,i);l+="L ".concat(n.x,",").concat(n.y,"\n            A ").concat(d,",").concat(d,",0,\n            ").concat(+(Math.abs(h)>180),",").concat(+(f<=i),",\n            ").concat(m.x,",").concat(m.y," Z")}else l+="L ".concat(b,",").concat(c," Z");return l},pm={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},pn=a=>{var b,c=mv(a,pm),{cx:d,cy:e,innerRadius:f,outerRadius:g,cornerRadius:i,forceCornerRadius:j,cornerIsExternal:k,startAngle:l,endAngle:m,className:n}=c;if(g<f||l===m)return null;var o=(0,q.$)("recharts-sector",n),p=g-f,r=C(i,p,0,!0);return b=r>0&&360>Math.abs(l-m)?(a=>{var{cx:b,cy:c,innerRadius:d,outerRadius:e,cornerRadius:f,forceCornerRadius:g,cornerIsExternal:h,startAngle:i,endAngle:j}=a,k=v(j-i),{circleTangency:l,lineTangency:m,theta:n}=pk({cx:b,cy:c,radius:e,angle:i,sign:k,cornerRadius:f,cornerIsExternal:h}),{circleTangency:o,lineTangency:p,theta:q}=pk({cx:b,cy:c,radius:e,angle:j,sign:-k,cornerRadius:f,cornerIsExternal:h}),r=h?Math.abs(i-j):Math.abs(i-j)-n-q;if(r<0)return g?"M ".concat(m.x,",").concat(m.y,"\n        a").concat(f,",").concat(f,",0,0,1,").concat(2*f,",0\n        a").concat(f,",").concat(f,",0,0,1,").concat(-(2*f),",0\n      "):pl({cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:i,endAngle:j});var s="M ".concat(m.x,",").concat(m.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(l.x,",").concat(l.y,"\n    A").concat(e,",").concat(e,",0,").concat(+(r>180),",").concat(+(k<0),",").concat(o.x,",").concat(o.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(p.x,",").concat(p.y,"\n  ");if(d>0){var{circleTangency:t,lineTangency:u,theta:w}=pk({cx:b,cy:c,radius:d,angle:i,sign:k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),{circleTangency:x,lineTangency:y,theta:z}=pk({cx:b,cy:c,radius:d,angle:j,sign:-k,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),A=h?Math.abs(i-j):Math.abs(i-j)-w-z;if(A<0&&0===f)return"".concat(s,"L").concat(b,",").concat(c,"Z");s+="L".concat(y.x,",").concat(y.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(x.x,",").concat(x.y,"\n      A").concat(d,",").concat(d,",0,").concat(+(A>180),",").concat(+(k>0),",").concat(t.x,",").concat(t.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(k<0),",").concat(u.x,",").concat(u.y,"Z")}else s+="L".concat(b,",").concat(c,"Z");return s})({cx:d,cy:e,innerRadius:f,outerRadius:g,cornerRadius:Math.min(r,p/2),forceCornerRadius:j,cornerIsExternal:k,startAngle:l,endAngle:m}):pl({cx:d,cy:e,innerRadius:f,outerRadius:g,startAngle:l,endAngle:m}),h.createElement("path",pj({},l7(c,!0),{className:o,d:b}))};function po(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pp(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?po(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):po(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pq(){return(pq=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pr(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ps(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pr(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pr(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pt(a){var b,c,d,{coordinate:e,payload:f,index:g,offset:i,tooltipAxisBandSize:j,layout:k,cursor:l,tooltipEventType:m,chartName:n}=a;if(!l||!e||"ScatterChart"!==n&&"axis"!==m)return null;if("ScatterChart"===n)c=e,d=oR;else if("BarChart"===n)b=j/2,c={stroke:"none",fill:"#ccc",x:"horizontal"===k?e.x-b:i.left+.5,y:"horizontal"===k?i.top+.5:e.y-b,width:"horizontal"===k?j:i.width-1,height:"horizontal"===k?i.height-1:j},d=ph;else if("radial"===k){var{cx:o,cy:p,radius:r,startAngle:s,endAngle:t}=pi(e);c={cx:o,cy:p,startAngle:s,endAngle:t,innerRadius:r,outerRadius:r},d=pn}else c={points:function(a,b,c){var d,e,f,g;if("horizontal"===a)f=d=b.x,e=c.top,g=c.top+c.height;else if("vertical"===a)g=e=b.y,d=c.left,f=c.left+c.width;else if(null!=b.cx&&null!=b.cy)if("centric"!==a)return pi(b);else{var{cx:h,cy:i,innerRadius:j,outerRadius:k,angle:l}=b,m=cz(h,i,j,l),n=cz(h,i,k,l);d=m.x,e=m.y,f=n.x,g=n.y}return[{x:d,y:e},{x:f,y:g}]}(k,e,i)},d=oN;var u="object"==typeof l&&"className"in l?l.className:void 0,v=ps(ps(ps(ps({stroke:"#ccc",pointerEvents:"none"},i),c),l7(l,!1)),{},{payload:f,payloadIndex:g,className:(0,q.$)("recharts-tooltip-cursor",u)});return(0,h.isValidElement)(l)?(0,h.cloneElement)(l,v):(0,h.createElement)(d,v)}function pu(a){var b,c,d,e=(b=cm(jW),c=cm(kn),d=cm(kk),cM(pp(pp({},b),{},{scale:d}),c)),f=c6(),g=da(),i=cm(hP);return h.createElement(pt,pq({},a,{coordinate:a.coordinate,index:a.index,payload:a.payload,offset:f,layout:g,tooltipAxisBandSize:e,chartName:i}))}function pv(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pw(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pv(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pv(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function px(a){return a.dataKey}var py=[],pz={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!mC.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function pA(a){var b,c,d,e=mv(a,pz),{active:f,allowEscapeViewBox:g,animationDuration:i,animationEasing:j,content:k,filterNull:l,isAnimationActive:m,offset:n,payloadUniqBy:o,position:p,reverseDirection:q,useTranslate3d:r,wrapperStyle:s,cursor:t,shared:u,trigger:v,defaultIndex:w,portal:x,axisId:y}=e;ci();var z="number"==typeof w?String(w):w,A=c4(),B=l8(),C=cm(a=>jH(a,u)),{activeIndex:D,isActive:E}=cm(a=>kM(a,C,v,z)),F=cm(a=>kL(a,C,v,z)),G=cm(a=>kK(a,C,v,z)),H=cm(a=>kJ(a,C,v,z)),I=(0,h.useContext)(mj),J=null!=f?f:E,[K,L]=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[b,c]=(0,h.useState)({height:0,left:0,top:0,width:0}),d=(0,h.useCallback)(a=>{if(null!=a){var d=a.getBoundingClientRect(),e={height:d.height,left:d.left,top:d.top,width:d.width};(Math.abs(e.height-b.height)>1||Math.abs(e.left-b.left)>1||Math.abs(e.top-b.top)>1||Math.abs(e.width-b.width)>1)&&c({height:e.height,left:e.left,top:e.top,width:e.width})}},[b.width,b.height,b.top,b.left,...a]);return[b,d]}([F,J]),M="axis"===C?G:void 0;cm(a=>((a,b,c)=>{if(null!=b){var d=jQ(a);return"axis"===b?"hover"===c?d.axisInteraction.hover.dataKey:d.axisInteraction.click.dataKey:"hover"===c?d.itemInteraction.hover.dataKey:d.itemInteraction.click.dataKey}})(a,C,v)),cm(hS),cm(hQ),cm(hR),null==(b=cm(mi))||b.active;var N=null!=x?x:I;if(null==N)return null;var O=null!=F?F:py;J||(O=py),l&&O.length&&(c=F.filter(a=>null!=a.value&&(!0!==a.hide||e.includeHidden)),O=!0===o?od()(c,px):"function"==typeof o?od()(c,o):c);var P=O.length>0,Q=h.createElement(ob,{allowEscapeViewBox:g,animationDuration:i,animationEasing:j,isAnimationActive:m,active:J,coordinate:H,hasPayload:P,offset:n,position:p,reverseDirection:q,useTranslate3d:r,viewBox:A,wrapperStyle:s,lastBoundingBox:K,innerRef:L,hasPortalFromProps:!!x},(d=pw(pw({},e),{},{payload:O,label:M,active:J,coordinate:H,accessibilityLayer:B}),h.isValidElement(k)?h.cloneElement(k,d):"function"==typeof k?h.createElement(k,d):h.createElement(n4,d)));return h.createElement(h.Fragment,null,(0,n_.createPortal)(Q,N),J&&h.createElement(pu,{cursor:t,tooltipEventType:C,coordinate:H,payload:F,index:D}))}function pB(){return(pB=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var pC=a=>{var{cx:b,cy:c,r:d,className:e}=a,f=(0,q.$)("recharts-dot",e);return b===+b&&c===+c&&d===+d?h.createElement("circle",pB({},l7(a,!1),l_(a),{className:f,cx:b,cy:c,r:d})):null},pD=c(9474),pE=c.n(pD),pF=["valueAccessor"],pG=["data","dataKey","clockWise","id","textBreakAll"];function pH(){return(pH=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pI(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function pJ(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?pI(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):pI(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function pK(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var pL=a=>Array.isArray(a.value)?pE()(a.value):a.value;function pM(a){var{valueAccessor:b=pL}=a,c=pK(a,pF),{data:d,dataKey:e,clockWise:f,id:g,textBreakAll:i}=c,j=pK(c,pG);return d&&d.length?h.createElement(mT,{className:"recharts-label-list"},d.map((a,c)=>{var d=null==e?b(a,c):cD(a&&a.payload,e),k=null==g?{}:{id:"".concat(g,"-").concat(c)};return h.createElement(ni,pH({},l7(a,!0),j,k,{parentViewBox:a.parentViewBox,value:d,textBreakAll:i,viewBox:ni.parseViewBox(null==f?a:pJ(pJ({},a),{},{clockWise:f})),key:"label-".concat(c),index:c}))})):null}function pN(a){return ci(),(0,h.useRef)(null),null}function pO(a){return ci(),null}pM.displayName="LabelList",pM.renderCallByParent=function(a,b){var c,d=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&d&&!a.label)return null;var{children:e}=a,f=l5(e,pM).map((a,c)=>(0,h.cloneElement)(a,{data:b,key:"labelList-".concat(c)}));return d?[(c=a.label,c?!0===c?h.createElement(pM,{key:"labelList-implicit",data:b}):h.isValidElement(c)||nh(c)?h.createElement(pM,{key:"labelList-implicit",data:b,content:c}):"object"==typeof c?h.createElement(pM,pH({data:b},c,{key:"labelList-implicit"})):null:null),...f]:f};var pP=["children"],pQ=()=>{},pR=(0,h.createContext)({addErrorBar:pQ,removeErrorBar:pQ}),pS=(0,h.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function pT(a){var{children:b}=a,c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,pP);return h.createElement(pS.Provider,{value:c},b)}var pU=a=>{var{children:b,xAxisId:c,yAxisId:d,zAxisId:e,dataKey:f,data:g,stackId:i,hide:j,type:k,barSize:l}=a,[m,n]=h.useState([]),o=(0,h.useCallback)(a=>{n(b=>[...b,a])},[n]),p=(0,h.useCallback)(a=>{n(b=>b.filter(b=>b!==a))},[n]),q=c1();return h.createElement(pR.Provider,{value:{addErrorBar:o,removeErrorBar:p}},h.createElement(pN,{type:k,data:g,xAxisId:c,yAxisId:d,zAxisId:e,dataKey:f,errorBars:m,stackId:i,hide:j,barSize:l,isPanorama:q}),b)};function pV(a){var{addErrorBar:b,removeErrorBar:c}=(0,h.useContext)(pR);return null}var pW=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function pX(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function pY(){return(pY=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function pZ(a){var b,c,{direction:d,width:e,dataKey:f,isAnimationActive:g,animationBegin:i,animationDuration:j,animationEasing:k}=a,l=l7(function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,pW),!1),{data:m,dataPointFormatter:n,xAxisId:o,yAxisId:p,errorBarOffset:q}=(0,h.useContext)(pS),r=(b=c1(),cm(a=>jB(a,"xAxis",o,b))),s=(c=c1(),cm(a=>jB(a,"yAxis",p,c)));if((null==r?void 0:r.scale)==null||(null==s?void 0:s.scale)==null||null==m||"x"===d&&"number"!==r.type)return null;var t=m.map(a=>{var b,c,{x:m,y:o,value:p,errorVal:t}=n(a,f,d);if(!t)return null;var u=[];if(Array.isArray(t)?[b,c]=t:b=c=t,"x"===d){var{scale:v}=r,w=o+q,x=w+e,y=w-e,z=v(p-b),A=v(p+c);u.push({x1:A,y1:x,x2:A,y2:y}),u.push({x1:z,y1:w,x2:A,y2:w}),u.push({x1:z,y1:x,x2:z,y2:y})}else if("y"===d){var{scale:B}=s,C=m+q,D=C-e,E=C+e,F=B(p-b),G=B(p+c);u.push({x1:D,y1:G,x2:E,y2:G}),u.push({x1:C,y1:F,x2:C,y2:G}),u.push({x1:D,y1:F,x2:E,y2:F})}var H="".concat(m+q,"px ").concat(o+q,"px");return h.createElement(mT,pY({className:"recharts-errorBar",key:"bar-".concat(u.map(a=>"".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2)))},l),u.map(a=>{var b=g?{transformOrigin:"".concat(a.x1-5,"px")}:void 0;return h.createElement(pd,{from:{transform:"scaleY(0)",transformOrigin:H},to:{transform:"scaleY(1)",transformOrigin:H},begin:i,easing:k,isActive:g,duration:j,key:"line-".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2),style:{transformOrigin:H}},h.createElement("line",pY({},a,{style:b})))}))});return h.createElement(mT,{className:"recharts-errorBars"},t)}var p$=(0,h.createContext)(void 0);function p_(a){var{direction:b,children:c}=a;return h.createElement(p$.Provider,{value:b},c)}var p0={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function p1(a){var b,c,d=(b=a.direction,c=(0,h.useContext)(p$),null!=b?b:null!=c?c:"x"),{width:e,isAnimationActive:f,animationBegin:g,animationDuration:i,animationEasing:j}=mv(a,p0);return h.createElement(h.Fragment,null,h.createElement(pV,{dataKey:a.dataKey,direction:d}),h.createElement(pZ,pY({},a,{direction:d,width:e,isAnimationActive:f,animationBegin:g,animationDuration:i,animationEasing:j})))}class p2 extends h.Component{render(){return h.createElement(p1,this.props)}}function p3(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p4(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?p3(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):p3(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function p5(a){var{points:b,mainColor:c,activeDot:d,itemDataKey:e}=a,f=cm(ks),g=cm(kA);if(null==b||null==g)return null;var i=b.find(a=>g.includes(a.payload));return null==i?null:(a=>{var b,{point:c,childIndex:d,mainColor:e,activeDot:f,dataKey:g}=a;if(!1===f||null==c.x||null==c.y)return null;var i=p4(p4({index:d,dataKey:g,cx:c.x,cy:c.y,r:4,fill:null!=e?e:"none",strokeWidth:2,stroke:"#fff",payload:c.payload,value:c.value},l7(f,!1)),l_(f));return b=(0,h.isValidElement)(f)?(0,h.cloneElement)(f,i):"function"==typeof f?f(i):h.createElement(pC,i),h.createElement(mT,{className:"recharts-active-dot"},b)})({point:i,childIndex:Number(f),mainColor:c,dataKey:e,activeDot:d})}function p6(a){var{fn:b,args:c}=a;return ci(),c1(),null}function p7(a,b){var c,d,e=cm(b=>id(b,a)),f=cm(a=>ig(a,b)),g=null!=(c=null==e?void 0:e.allowDataOverflow)?c:ic.allowDataOverflow,h=null!=(d=null==f?void 0:f.allowDataOverflow)?d:ie.allowDataOverflow;return{needClip:g||h,needClipX:g,needClipY:h}}function p8(a){var{xAxisId:b,yAxisId:c,clipPathId:d}=a,e=mp(),{needClipX:f,needClipY:g,needClip:i}=p7(b,c);if(!i)return null;var{x:j,y:k,width:l,height:m}=e;return h.createElement("clipPath",{id:"clipPath-".concat(d)},h.createElement("rect",{x:f?j:j-l/2,y:g?k:k-m/2,width:f?l:2*l,height:g?m:2*m}))}pX(p2,"defaultProps",p0),pX(p2,"displayName","ErrorBar");var p9=(a,b,c,d)=>jB(a,"xAxis",b,d),qa=(a,b,c,d)=>jA(a,"xAxis",b,d),qb=(a,b,c,d)=>jB(a,"yAxis",c,d),qc=(a,b,c,d)=>jA(a,"yAxis",c,d),qd=cd([c9,p9,qb,qa,qc],(a,b,c,d,e)=>cE(a,"xAxis")?cM(b,d,!1):cM(c,e,!1)),qe=cd([io,(a,b,c,d,e)=>e],(a,b)=>{if(a.some(a=>"line"===a.type&&b.dataKey===a.dataKey&&b.data===a.data))return b}),qf=cd([c9,p9,qb,qa,qc,qe,qd,gT],(a,b,c,d,e,f,g,h)=>{var i,{chartData:j,dataStartIndex:k,dataEndIndex:l}=h;if(null!=f&&null!=b&&null!=c&&null!=d&&null!=e&&0!==d.length&&0!==e.length&&null!=g){var{dataKey:m,data:n}=f;if(null!=(i=null!=n&&n.length>0?n:null==j?void 0:j.slice(k,l+1)))return function(a){var{layout:b,xAxis:c,yAxis:d,xAxisTicks:e,yAxisTicks:f,dataKey:g,bandSize:h,displayedData:i}=a;return i.map((a,i)=>{var j=cD(a,g);if("horizontal"===b)return{x:cI({axis:c,ticks:e,bandSize:h,entry:a,index:i}),y:null==j?null:d.scale(j),value:j,payload:a};return{x:null==j?null:c.scale(j),y:cI({axis:d,ticks:f,bandSize:h,entry:a,index:i}),value:j,payload:a}})}({layout:a,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,dataKey:m,bandSize:g,displayedData:i})}});function qg(a){var{legendPayload:b}=a;return ci(),c1(),null}function qh(a){var{legendPayload:b}=a;return ci(),cm(c9),null}function qi(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",c=(0,h.useRef)(B(b)),d=(0,h.useRef)(a);return d.current!==a&&(c.current=B(b),d.current=a),c.current}var qj=["type","layout","connectNulls","needClip"],qk=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function ql(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function qm(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function qn(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?qm(Object(c),!0).forEach(function(b){qo(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):qm(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function qo(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function qp(){return(qp=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function qq(a){var{dataKey:b,data:c,stroke:d,strokeWidth:e,fill:f,name:g,hide:h,unit:i}=a;return{dataDefinedOnItem:c,positions:void 0,settings:{stroke:d,strokeWidth:e,fill:f,dataKey:b,nameKey:void 0,name:cO(g,b),hide:h,type:a.tooltipType,color:a.stroke,unit:i}}}var qr=(a,b)=>"".concat(b,"px ").concat(a-b,"px");function qs(a){var{clipPathId:b,points:c,props:d}=a,{dot:e,dataKey:f,needClip:g}=d;if(null==c||!e&&1!==c.length)return null;var i=l6(e),j=l7(d,!1),k=l7(e,!0),l=c.map((a,b)=>{var d,g=qn(qn(qn({key:"dot-".concat(b),r:3},j),k),{},{index:b,cx:a.x,cy:a.y,dataKey:f,value:a.value,payload:a.payload,points:c});if(h.isValidElement(e))d=h.cloneElement(e,g);else if("function"==typeof e)d=e(g);else{var i=(0,q.$)("recharts-line-dot","boolean"!=typeof e?e.className:"");d=h.createElement(pC,qp({},g,{className:i}))}return d}),m={clipPath:g?"url(#clipPath-".concat(i?"":"dots-").concat(b,")"):null};return h.createElement(mT,qp({className:"recharts-line-dots",key:"dots"},m),l)}function qt(a){var{clipPathId:b,pathRef:c,points:d,strokeDasharray:e,props:f,showLabels:g}=a,{type:i,layout:j,connectNulls:k,needClip:l}=f,m=qn(qn({},l7(ql(f,qj),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:l?"url(#clipPath-".concat(b,")"):null,points:d,type:i,layout:j,connectNulls:k,strokeDasharray:null!=e?e:f.strokeDasharray});return h.createElement(h.Fragment,null,(null==d?void 0:d.length)>1&&h.createElement(oN,qp({},m,{pathRef:c})),h.createElement(qs,{points:d,clipPathId:b,props:f}),g&&pM.renderCallByParent(f,d))}function qu(a){var{clipPathId:b,props:c,pathRef:d,previousPointsRef:e,longestAnimatedLengthRef:f}=a,{points:g,strokeDasharray:i,isAnimationActive:j,animationBegin:k,animationDuration:l,animationEasing:m,animateNewValues:n,width:o,height:p,onAnimationEnd:q,onAnimationStart:r}=c,s=e.current,t=qi(c,"recharts-line-"),[u,v]=(0,h.useState)(!1),w=(0,h.useCallback)(()=>{"function"==typeof q&&q(),v(!1)},[q]),x=(0,h.useCallback)(()=>{"function"==typeof r&&r(),v(!0)},[r]),y=function(a){try{return a&&a.getTotalLength&&a.getTotalLength()||0}catch(a){return 0}}(d.current),z=f.current;return h.createElement(pd,{begin:k,duration:l,isActive:j,easing:m,from:{t:0},to:{t:1},onAnimationEnd:w,onAnimationStart:x,key:t},a=>{var j,{t:k}=a,l=Math.min(E(z,y+z)(k),y);if(j=i?((a,b,c)=>{var d=c.reduce((a,b)=>a+b);if(!d)return qr(b,a);for(var e=Math.floor(a/d),f=a%d,g=b-a,h=[],i=0,j=0;i<c.length;j+=c[i],++i)if(j+c[i]>f){h=[...c.slice(0,i),f-j];break}var k=h.length%2==0?[0,g]:[g];return[...function(a,b){for(var c=a.length%2!=0?[...a,0]:a,d=[],e=0;e<b;++e)d=[...d,...c];return d}(c,e),...h,...k].map(a=>"".concat(a,"px")).join(", ")})(l,y,"".concat(i).split(/[,\s]+/gim).map(a=>parseFloat(a))):qr(y,l),s){var m=s.length/g.length,q=1===k?g:g.map((a,b)=>{var c=Math.floor(b*m);if(s[c]){var d=s[c],e=E(d.x,a.x),f=E(d.y,a.y);return qn(qn({},a),{},{x:e(k),y:f(k)})}if(n){var g=E(2*o,a.x),h=E(p/2,a.y);return qn(qn({},a),{},{x:g(k),y:h(k)})}return qn(qn({},a),{},{x:a.x,y:a.y})});return e.current=q,h.createElement(qt,{props:c,points:q,clipPathId:b,pathRef:d,showLabels:!u,strokeDasharray:j})}return k>0&&y>0&&(e.current=g,f.current=l),h.createElement(qt,{props:c,points:g,clipPathId:b,pathRef:d,showLabels:!u,strokeDasharray:j})})}function qv(a){var{clipPathId:b,props:c}=a,{points:d,isAnimationActive:e}=c,f=(0,h.useRef)(null),g=(0,h.useRef)(0),i=(0,h.useRef)(null),j=f.current;return e&&d&&d.length&&j!==d?h.createElement(qu,{props:c,clipPathId:b,previousPointsRef:f,longestAnimatedLengthRef:g,pathRef:i}):h.createElement(qt,{props:c,points:d,clipPathId:b,pathRef:i,showLabels:!0})}var qw=(a,b)=>({x:a.x,y:a.y,value:a.value,errorVal:cD(a.payload,b)});class qx extends h.Component{constructor(){super(...arguments),qo(this,"id",B("recharts-line-"))}render(){var a,{hide:b,dot:c,points:d,className:e,xAxisId:f,yAxisId:g,top:i,left:j,width:k,height:l,id:m,needClip:n,layout:o}=this.props;if(b)return null;var p=(0,q.$)("recharts-line",e),r=null==m?this.id:m,{r:s=3,strokeWidth:t=2}=null!=(a=l7(c,!1))?a:{r:3,strokeWidth:2},u=l6(c),v=2*s+t;return h.createElement(h.Fragment,null,h.createElement(mT,{className:p},n&&h.createElement("defs",null,h.createElement(p8,{clipPathId:r,xAxisId:f,yAxisId:g}),!u&&h.createElement("clipPath",{id:"clipPath-dots-".concat(r)},h.createElement("rect",{x:j-v/2,y:i-v/2,width:k+v,height:l+v}))),h.createElement(qv,{props:this.props,clipPathId:r}),h.createElement(p_,{direction:"horizontal"===o?"y":"x"},h.createElement(pT,{xAxisId:f,yAxisId:g,data:d,dataPointFormatter:qw,errorBarOffset:0},this.props.children))),h.createElement(p5,{activeDot:this.props.activeDot,points:d,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var qy={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!mC.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function qz(a){var b=mv(a,qy),{activeDot:c,animateNewValues:d,animationBegin:e,animationDuration:f,animationEasing:g,connectNulls:i,dot:j,hide:k,isAnimationActive:l,label:m,legendType:n,xAxisId:o,yAxisId:p}=b,q=ql(b,qk),{needClip:r}=p7(o,p),{height:s,width:t,x:u,y:v}=mp(),w=da(),x=c1(),y=(0,h.useMemo)(()=>({dataKey:a.dataKey,data:a.data}),[a.dataKey,a.data]),z=cm(a=>qf(a,o,p,x,y));return"horizontal"!==w&&"vertical"!==w?null:h.createElement(qx,qp({},q,{connectNulls:i,dot:j,activeDot:c,animateNewValues:d,animationBegin:e,animationDuration:f,animationEasing:g,isAnimationActive:l,hide:k,label:m,legendType:n,xAxisId:o,yAxisId:p,points:z,layout:w,height:s,width:t,left:u,top:v,needClip:r}))}class qA extends h.PureComponent{render(){return h.createElement(pU,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},h.createElement(qg,{legendPayload:(a=>{var{dataKey:b,name:c,stroke:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:cO(c,b),payload:a}]})(this.props)}),h.createElement(p6,{fn:qq,args:this.props}),h.createElement(qz,this.props))}}qo(qA,"displayName","Line"),qo(qA,"defaultProps",qy);var qB=["axis","item"],qC=(0,h.forwardRef)((a,b)=>h.createElement(mz,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:qB,tooltipPayloadSearcher:bn,categoricalChartProps:a,ref:b})),qD=a=>null;qD.displayName="Cell";var qE=c(92867),qF=c.n(qE);function qG(){return(qG=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var qH=(a,b,c,d,e)=>{var f=c-d;return"M ".concat(a,",").concat(b)+"L ".concat(a+c,",").concat(b)+"L ".concat(a+c-f/2,",").concat(b+e)+"L ".concat(a+c-f/2-d,",").concat(b+e)+"L ".concat(a,",").concat(b," Z")},qI={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},qJ=a=>{var b=mv(a,qI),c=(0,h.useRef)(),[d,e]=(0,h.useState)(-1);(0,h.useEffect)(()=>{if(c.current&&c.current.getTotalLength)try{var a=c.current.getTotalLength();a&&e(a)}catch(a){}},[]);var{x:f,y:g,upperWidth:i,lowerWidth:j,height:k,className:l}=b,{animationEasing:m,animationDuration:n,animationBegin:o,isUpdateAnimationActive:p}=b;if(f!==+f||g!==+g||i!==+i||j!==+j||k!==+k||0===i&&0===j||0===k)return null;var r=(0,q.$)("recharts-trapezoid",l);return p?h.createElement(pd,{canBegin:d>0,from:{upperWidth:0,lowerWidth:0,height:k,x:f,y:g},to:{upperWidth:i,lowerWidth:j,height:k,x:f,y:g},duration:n,animationEasing:m,isActive:p},a=>{var{upperWidth:e,lowerWidth:f,height:g,x:i,y:j}=a;return h.createElement(pd,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:o,duration:n,easing:m},h.createElement("path",qG({},l7(b,!0),{className:r,d:qH(i,j,e,f,g),ref:c})))}):h.createElement("g",null,h.createElement("path",qG({},l7(b,!0),{className:r,d:qH(f,g,i,j,k)})))};let qK=Math.cos,qL=Math.sin,qM=Math.sqrt,qN=Math.PI,qO=2*qN,qP={draw(a,b){let c=qM(b/qN);a.moveTo(c,0),a.arc(0,0,c,0,qO)}},qQ=qM(1/3),qR=2*qQ,qS=qL(qN/10)/qL(7*qN/10),qT=qL(qO/10)*qS,qU=-qK(qO/10)*qS,qV=qM(3),qW=qM(3)/2,qX=1/qM(12),qY=(qX/2+1)*3;qM(3),qM(3);var qZ=["type","size","sizeType"];function q$(){return(q$=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function q_(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function q0(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?q_(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):q_(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var q1={symbolCircle:qP,symbolCross:{draw(a,b){let c=qM(b/5)/2;a.moveTo(-3*c,-c),a.lineTo(-c,-c),a.lineTo(-c,-3*c),a.lineTo(c,-3*c),a.lineTo(c,-c),a.lineTo(3*c,-c),a.lineTo(3*c,c),a.lineTo(c,c),a.lineTo(c,3*c),a.lineTo(-c,3*c),a.lineTo(-c,c),a.lineTo(-3*c,c),a.closePath()}},symbolDiamond:{draw(a,b){let c=qM(b/qR),d=c*qQ;a.moveTo(0,-c),a.lineTo(d,0),a.lineTo(0,c),a.lineTo(-d,0),a.closePath()}},symbolSquare:{draw(a,b){let c=qM(b),d=-c/2;a.rect(d,d,c,c)}},symbolStar:{draw(a,b){let c=qM(.8908130915292852*b),d=qT*c,e=qU*c;a.moveTo(0,-c),a.lineTo(d,e);for(let b=1;b<5;++b){let f=qO*b/5,g=qK(f),h=qL(f);a.lineTo(h*c,-g*c),a.lineTo(g*d-h*e,h*d+g*e)}a.closePath()}},symbolTriangle:{draw(a,b){let c=-qM(b/(3*qV));a.moveTo(0,2*c),a.lineTo(-qV*c,-c),a.lineTo(qV*c,-c),a.closePath()}},symbolWye:{draw(a,b){let c=qM(b/qY),d=c/2,e=c*qX,f=c*qX+c,g=-d;a.moveTo(d,e),a.lineTo(d,f),a.lineTo(g,f),a.lineTo(-.5*d-qW*e,qW*d+-.5*e),a.lineTo(-.5*d-qW*f,qW*d+-.5*f),a.lineTo(-.5*g-qW*f,qW*g+-.5*f),a.lineTo(-.5*d+qW*e,-.5*e-qW*d),a.lineTo(-.5*d+qW*f,-.5*f-qW*d),a.lineTo(-.5*g+qW*f,-.5*f-qW*g),a.closePath()}}},q2=Math.PI/180,q3=a=>{var{type:b="circle",size:c=64,sizeType:d="area"}=a,e=q0(q0({},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,qZ)),{},{type:b,size:c,sizeType:d}),{className:f,cx:g,cy:i}=e,j=l7(e,!0);return g===+g&&i===+i&&c===+c?h.createElement("path",q$({},j,{className:(0,q.$)("recharts-symbols",f),transform:"translate(".concat(g,", ").concat(i,")"),d:(()=>{var a=q1["symbol".concat(G(b))]||qP;return(function(a,b){let c=null,d=oB(e);function e(){let e;if(c||(c=e=d()),a.apply(this,arguments).draw(c,+b.apply(this,arguments)),e)return c=null,e+""||null}return a="function"==typeof a?a:cs(a||qP),b="function"==typeof b?b:cs(void 0===b?64:+b),e.type=function(b){return arguments.length?(a="function"==typeof b?b:cs(b),e):a},e.size=function(a){return arguments.length?(b="function"==typeof a?a:cs(+a),e):b},e.context=function(a){return arguments.length?(c=null==a?null:a,e):c},e})().type(a).size(((a,b,c)=>{if("area"===b)return a;switch(c){case"cross":return 5*a*a/9;case"diamond":return .5*a*a/Math.sqrt(3);case"square":return a*a;case"star":var d=18*q2;return 1.25*a*a*(Math.tan(d)-Math.tan(2*d)*Math.tan(d)**2);case"triangle":return Math.sqrt(3)*a*a/4;case"wye":return(21-10*Math.sqrt(3))*a*a/8;default:return Math.PI*a*a/4}})(c,d,b))()})()})):null};q3.registerSymbol=(a,b)=>{q1["symbol".concat(G(a))]=b};var q4=["option","shapeType","propTransformer","activeClassName","isActive"];function q5(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function q6(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?q5(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):q5(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function q7(a,b){return q6(q6({},b),a)}function q8(a){var{shapeType:b,elementProps:c}=a;switch(b){case"rectangle":return h.createElement(ph,c);case"trapezoid":return h.createElement(qJ,c);case"sector":return h.createElement(pn,c);case"symbols":if("symbols"===b)return h.createElement(q3,c);break;default:return null}}function q9(a){var b,{option:c,shapeType:d,propTransformer:e=q7,activeClassName:f="recharts-active-shape",isActive:g}=a,i=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,q4);if((0,h.isValidElement)(c))b=(0,h.cloneElement)(c,q6(q6({},i),(0,h.isValidElement)(c)?c.props:c));else if("function"==typeof c)b=c(i);else if(qF()(c)&&"boolean"!=typeof c){var j=e(c,i);b=h.createElement(q8,{shapeType:d,elementProps:j})}else b=h.createElement(q8,{shapeType:d,elementProps:i});return g?h.createElement(mT,{className:f},b):b}var ra=["x","y"];function rb(){return(rb=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function rc(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function rd(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rc(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rc(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function re(a,b){var{x:c,y:d}=a,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,ra),f=parseInt("".concat(c),10),g=parseInt("".concat(d),10),h=parseInt("".concat(b.height||e.height),10),i=parseInt("".concat(b.width||e.width),10);return rd(rd(rd(rd(rd({},b),e),f?{x:f}:{}),g?{y:g}:{}),{},{height:h,width:i,name:b.name,radius:b.radius})}function rf(a){return h.createElement(q9,rb({shapeType:"rectangle",propTransformer:re,activeClassName:"recharts-active-bar"},a))}var rg=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(c,d)=>{if(y(a))return a;var e=y(c)||null==c;return e?a(c,d):(e||function(a,b){if(!a)throw Error("Invariant failed")}(!1),b)}},rh=(a,b)=>{var c=ci();return(d,e)=>f=>{null==a||a(d,e,f),c(bE({activeIndex:String(e),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}},ri=a=>{var b=ci();return(c,d)=>e=>{null==a||a(c,d,e),b(bF())}},rj=(a,b)=>{var c=ci();return(d,e)=>f=>{null==a||a(d,e,f),c(bH({activeIndex:String(e),activeDataKey:b,activeCoordinate:d.tooltipPosition}))}},rk=()=>{var a=ci();return(0,h.useEffect)(()=>(a(k4()),()=>{a(k5())})),null};function rl(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function rm(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rl(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rl(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var rn=(a,b,c,d,e)=>e,ro=(a,b,c)=>{var d=null!=c?c:a;if(null!=d)return C(d,b,0)},rp=cd([c9,io,(a,b)=>b,(a,b,c)=>c,(a,b,c,d)=>d],(a,b,c,d,e)=>b.filter(b=>"horizontal"===a?b.xAxisId===c:b.yAxisId===d).filter(a=>a.isPanorama===e).filter(a=>!1===a.hide).filter(a=>"bar"===a.type));function rq(a){return null!=a.stackId&&null!=a.dataKey}var rr=cd([rp,a=>a.rootProps.barSize,(a,b,c)=>"horizontal"===c9(a)?jt(a,"xAxis",b):jt(a,"yAxis",c)],(a,b,c)=>{var d=a.filter(rq),e=a.filter(a=>null==a.stackId);return[...Object.entries(d.reduce((a,b)=>(a[b.stackId]||(a[b.stackId]=[]),a[b.stackId].push(b),a),{})).map(a=>{var[d,e]=a;return{stackId:d,dataKeys:e.map(a=>a.dataKey),barSize:ro(b,c,e[0].barSize)}}),...e.map(a=>({stackId:void 0,dataKeys:[a.dataKey].filter(a=>null!=a),barSize:ro(b,c,a.barSize)}))]}),rs=(a,b,c,d)=>{var e,f;return"horizontal"===c9(a)?(e=jB(a,"xAxis",b,d),f=jA(a,"xAxis",b,d)):(e=jB(a,"yAxis",c,d),f=jA(a,"yAxis",c,d)),cM(e,f)},rt=cd([rr,hM,a=>a.rootProps.barGap,hN,(a,b,c,d,e)=>{var f,g,h,i,j=c9(a),k=hM(a),{maxBarSize:l}=e,m=null==l?k:l;return"horizontal"===j?(h=jB(a,"xAxis",b,d),i=jA(a,"xAxis",b,d)):(h=jB(a,"yAxis",c,d),i=jA(a,"yAxis",c,d)),null!=(f=null!=(g=cM(h,i,!0))?g:m)?f:0},rs,(a,b,c,d,e)=>e.maxBarSize],(a,b,c,d,e,f,g)=>{var h=function(a,b,c,d,e){var f,g=d.length;if(!(g<1)){var h=C(a,c,0,!0),i=[];if(gU(d[0].barSize)){var j=!1,k=c/g,l=d.reduce((a,b)=>a+(b.barSize||0),0);(l+=(g-1)*h)>=c&&(l-=(g-1)*h,h=0),l>=c&&k>0&&(j=!0,k*=.9,l=g*k);var m={offset:((c-l)/2|0)-h,size:0};f=d.reduce((a,b)=>{var c,d=[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:m.offset+m.size+h,size:j?k:null!=(c=b.barSize)?c:0}}];return m=d[d.length-1].position,d},i)}else{var n=C(b,c,0,!0);c-2*n-(g-1)*h<=0&&(h=0);var o=(c-2*n-(g-1)*h)/g;o>1&&(o>>=0);var p=gU(e)?Math.min(o,e):o;f=d.reduce((a,b,c)=>[...a,{stackId:b.stackId,dataKeys:b.dataKeys,position:{offset:n+(o+h)*c+(o-p)/2,size:p}}],i)}return f}}(c,d,e!==f?e:f,a,null==g?b:g);return e!==f&&null!=h&&(h=h.map(a=>rm(rm({},a),{},{position:rm(rm({},a.position),{},{offset:a.position.offset-e/2})}))),h}),ru=cd([rt,rn],(a,b)=>{if(null!=a){var c=a.find(a=>a.stackId===b.stackId&&a.dataKeys.includes(b.dataKey));if(null!=c)return c.position}}),rv=cd([io,rn],(a,b)=>{if(a.some(a=>"bar"===a.type&&b.dataKey===a.dataKey&&b.stackId===a.stackId&&b.stackId===a.stackId))return b}),rw=cd([(a,b,c,d)=>"horizontal"===c9(a)?iD(a,"yAxis",c,d):iD(a,"xAxis",b,d),rn],(a,b)=>{if(!a||(null==b?void 0:b.dataKey)==null)return;var{stackId:c}=b;if(null!=c){var d=a[c];if(d){var{stackedData:e}=d;if(e)return e.find(a=>a.key===b.dataKey)}}}),rx=cd([cZ,(a,b,c,d)=>jB(a,"xAxis",b,d),(a,b,c,d)=>jB(a,"yAxis",c,d),(a,b,c,d)=>jA(a,"xAxis",b,d),(a,b,c,d)=>jA(a,"yAxis",c,d),ru,c9,gT,rs,rw,rv,(a,b,c,d,e,f)=>f],(a,b,c,d,e,f,g,h,i,j,k,l)=>{var m,{chartData:n,dataStartIndex:o,dataEndIndex:p}=h;if(null!=k&&null!=f&&("horizontal"===g||"vertical"===g)&&null!=b&&null!=c&&null!=d&&null!=e&&null!=i){var{data:q}=k;if(null!=(m=null!=q&&q.length>0?q:null==n?void 0:n.slice(o,p+1)))return function(a){var{layout:b,barSettings:{dataKey:c,minPointSize:d},pos:e,bandSize:f,xAxis:g,yAxis:h,xAxisTicks:i,yAxisTicks:j,stackedData:k,displayedData:l,offset:m,cells:n}=a,o="horizontal"===b?h:g,p=k?o.scale.domain():null,q=(a=>{var{numericAxis:b}=a,c=b.scale.domain();if("number"===b.type){var d=Math.min(c[0],c[1]),e=Math.max(c[0],c[1]);return d<=0&&e>=0?0:e<0?e:d}return c[0]})({numericAxis:o});return l.map((a,l)=>{k?r=((a,b)=>{if(!b||2!==b.length||!y(b[0])||!y(b[1]))return a;var c=Math.min(b[0],b[1]),d=Math.max(b[0],b[1]),e=[a[0],a[1]];return(!y(a[0])||a[0]<c)&&(e[0]=c),(!y(a[1])||a[1]>d)&&(e[1]=d),e[0]>d&&(e[0]=d),e[1]<c&&(e[1]=c),e})(k[l],p):Array.isArray(r=cD(a,c))||(r=[q,r]);var o=rg(d,0)(r[1],l);if("horizontal"===b){var r,s,t,u,x,z,A,[B,C]=[h.scale(r[0]),h.scale(r[1])];s=cJ({axis:g,ticks:i,bandSize:f,offset:e.offset,entry:a,index:l}),t=null!=(A=null!=C?C:B)?A:void 0,u=e.size;var D=B-C;if(x=w(D)?0:D,z={x:s,y:m.top,width:u,height:m.height},Math.abs(o)>0&&Math.abs(x)<Math.abs(o)){var E=v(x||o)*(Math.abs(o)-Math.abs(x));t-=E,x+=E}}else{var[F,G]=[g.scale(r[0]),g.scale(r[1])];if(s=F,t=cJ({axis:h,ticks:j,bandSize:f,offset:e.offset,entry:a,index:l}),u=G-F,x=e.size,z={x:m.left,y:t,width:m.width,height:x},Math.abs(o)>0&&Math.abs(u)<Math.abs(o)){var H=v(u||o)*(Math.abs(o)-Math.abs(u));u+=H}}return rD(rD({},a),{},{x:s,y:t,width:u,height:x,value:k?r:r[1],payload:a,background:z,tooltipPosition:{x:s+u/2,y:t+x/2}},n&&n[l]&&n[l].props)})}({layout:g,barSettings:k,pos:f,bandSize:i,xAxis:b,yAxis:c,xAxisTicks:d,yAxisTicks:e,stackedData:j,displayedData:m,offset:a,cells:l})}}),ry=["onMouseEnter","onMouseLeave","onClick"],rz=["value","background","tooltipPosition"],rA=["onMouseEnter","onClick","onMouseLeave"];function rB(){return(rB=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function rC(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function rD(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?rC(Object(c),!0).forEach(function(b){rE(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):rC(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function rE(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function rF(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function rG(a){var{dataKey:b,stroke:c,strokeWidth:d,fill:e,name:f,hide:g,unit:h}=a;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:c,strokeWidth:d,fill:e,dataKey:b,nameKey:void 0,name:cO(f,b),hide:g,type:a.tooltipType,color:a.fill,unit:h}}}function rH(a){var b=cm(ks),{data:c,dataKey:d,background:e,allOtherBarProps:f}=a,{onMouseEnter:g,onMouseLeave:i,onClick:j}=f,k=rF(f,ry),l=rh(g,d),m=ri(i),n=rj(j,d);if(!e||null==c)return null;var o=l7(e,!1);return h.createElement(h.Fragment,null,c.map((a,c)=>{var{value:f,background:g,tooltipPosition:i}=a,j=rF(a,rz);if(!g)return null;var p=l(a,c),q=m(a,c),r=n(a,c),s=rD(rD(rD(rD(rD({option:e,isActive:String(c)===b},j),{},{fill:"#eee"},g),o),l0(k,a,c)),{},{onMouseEnter:p,onMouseLeave:q,onClick:r,dataKey:d,index:c,className:"recharts-bar-background-rectangle"});return h.createElement(rf,rB({key:"background-bar-".concat(c)},s))}))}function rI(a){var{data:b,props:c,showLabels:d}=a,e=l7(c,!1),{shape:f,dataKey:g,activeBar:i}=c,j=cm(ks),k=cm(ku),{onMouseEnter:l,onClick:m,onMouseLeave:n}=c,o=rF(c,rA),p=rh(l,g),q=ri(n),r=rj(m,g);return b?h.createElement(h.Fragment,null,b.map((a,b)=>{var c=i&&String(b)===j&&(null==k||g===k),d=rD(rD(rD({},e),a),{},{isActive:c,option:c?i:f,index:b,dataKey:g});return h.createElement(mT,rB({className:"recharts-bar-rectangle"},l0(o,a,b),{onMouseEnter:p(a,b),onMouseLeave:q(a,b),onClick:r(a,b),key:"rectangle-".concat(null==a?void 0:a.x,"-").concat(null==a?void 0:a.y,"-").concat(null==a?void 0:a.value,"-").concat(b)}),h.createElement(rf,d))}),d&&pM.renderCallByParent(c,b)):null}function rJ(a){var{props:b,previousRectanglesRef:c}=a,{data:d,layout:e,isAnimationActive:f,animationBegin:g,animationDuration:i,animationEasing:j,onAnimationEnd:k,onAnimationStart:l}=b,m=c.current,n=qi(b,"recharts-bar-"),[o,p]=(0,h.useState)(!1),q=(0,h.useCallback)(()=>{"function"==typeof k&&k(),p(!1)},[k]),r=(0,h.useCallback)(()=>{"function"==typeof l&&l(),p(!0)},[l]);return h.createElement(pd,{begin:g,duration:i,isActive:f,easing:j,from:{t:0},to:{t:1},onAnimationEnd:q,onAnimationStart:r,key:n},a=>{var{t:f}=a,g=1===f?d:d.map((a,b)=>{var c=m&&m[b];if(c){var d=E(c.x,a.x),g=E(c.y,a.y),h=E(c.width,a.width),i=E(c.height,a.height);return rD(rD({},a),{},{x:d(f),y:g(f),width:h(f),height:i(f)})}if("horizontal"===e){var j=E(0,a.height)(f);return rD(rD({},a),{},{y:a.y+a.height-j,height:j})}var k=E(0,a.width)(f);return rD(rD({},a),{},{width:k})});return f>0&&(c.current=g),h.createElement(mT,null,h.createElement(rI,{props:b,data:g,showLabels:!o}))})}function rK(a){var{data:b,isAnimationActive:c}=a,d=(0,h.useRef)(null);return c&&b&&b.length&&(null==d.current||d.current!==b)?h.createElement(rJ,{previousRectanglesRef:d,props:a}):h.createElement(rI,{props:a,data:b,showLabels:!0})}var rL=(a,b)=>{var c=Array.isArray(a.value)?a.value[1]:a.value;return{x:a.x,y:a.y,value:c,errorVal:cD(a,b)}};class rM extends h.PureComponent{constructor(){super(...arguments),rE(this,"id",B("recharts-bar-"))}render(){var{hide:a,data:b,dataKey:c,className:d,xAxisId:e,yAxisId:f,needClip:g,background:i,id:j,layout:k}=this.props;if(a)return null;var l=(0,q.$)("recharts-bar",d),m=null==j?this.id:j;return h.createElement(mT,{className:l},g&&h.createElement("defs",null,h.createElement(p8,{clipPathId:m,xAxisId:e,yAxisId:f})),h.createElement(mT,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(m,")"):null},h.createElement(rH,{data:b,dataKey:c,background:i,allOtherBarProps:this.props}),h.createElement(rK,this.props)),h.createElement(p_,{direction:"horizontal"===k?"y":"x"},this.props.children))}}var rN={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!mC.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function rO(a){var b,{xAxisId:c,yAxisId:d,hide:e,legendType:f,minPointSize:g,activeBar:i,animationBegin:j,animationDuration:k,animationEasing:l,isAnimationActive:m}=mv(a,rN),{needClip:n}=p7(c,d),o=da(),p=c1(),q=(0,h.useMemo)(()=>{var b;return{barSize:a.barSize,data:void 0,dataKey:a.dataKey,maxBarSize:a.maxBarSize,minPointSize:g,stackId:null==(b=a.stackId)?void 0:String(b)}},[a.barSize,a.dataKey,a.maxBarSize,g,a.stackId]),r=l5(a.children,qD),s=cm(a=>rx(a,c,d,p,q,r));if("vertical"!==o&&"horizontal"!==o)return null;var t=null==s?void 0:s[0];return b=null==t||null==t.height||null==t.width?0:"vertical"===o?t.height/2:t.width/2,h.createElement(pT,{xAxisId:c,yAxisId:d,data:s,dataPointFormatter:rL,errorBarOffset:b},h.createElement(rM,rB({},a,{layout:o,needClip:n,data:s,xAxisId:c,yAxisId:d,hide:e,legendType:f,minPointSize:g,activeBar:i,animationBegin:j,animationDuration:k,animationEasing:l,isAnimationActive:m})))}class rP extends h.PureComponent{render(){return h.createElement(pU,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},h.createElement(rk,null),h.createElement(qg,{legendPayload:(a=>{var{dataKey:b,name:c,fill:d,legendType:e,hide:f}=a;return[{inactive:f,dataKey:b,type:e,color:d,value:cO(c,b),payload:a}]})(this.props)}),h.createElement(p6,{fn:rG,args:this.props}),h.createElement(rO,this.props))}}function rQ(a){return ci(),null}rE(rP,"displayName","Bar"),rE(rP,"defaultProps",rN);var rR=["width","height","layout"];function rS(){return(rS=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}var rT={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},rU=(0,h.forwardRef)(function(a,b){var c,d=mv(a.categoricalChartProps,rT),{width:e,height:f,layout:g}=d,i=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(d,rR);if(!gV(e)||!gV(f))return null;var{chartName:j,defaultTooltipEventType:k,validateTooltipEventTypes:l,tooltipPayloadSearcher:m}=a;return h.createElement(lS,{preloadedState:{options:{chartName:j,defaultTooltipEventType:k,validateTooltipEventTypes:l,tooltipPayloadSearcher:m,eventEmitter:void 0}},reduxStoreName:null!=(c=d.id)?c:j},h.createElement(lT,{chartData:d.data}),h.createElement(lU,{width:e,height:f,layout:g,margin:d.margin}),h.createElement(lV,{accessibilityLayer:d.accessibilityLayer,barCategoryGap:d.barCategoryGap,maxBarSize:d.maxBarSize,stackOffset:d.stackOffset,barGap:d.barGap,barSize:d.barSize,syncId:d.syncId,syncMethod:d.syncMethod,className:d.className}),h.createElement(rQ,{cx:d.cx,cy:d.cy,startAngle:d.startAngle,endAngle:d.endAngle,innerRadius:d.innerRadius,outerRadius:d.outerRadius}),h.createElement(mt,rS({width:e,height:f},i,{ref:b})))}),rV=["item"],rW={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},rX=(0,h.forwardRef)((a,b)=>{var c=mv(a,rW);return h.createElement(rU,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:rV,tooltipPayloadSearcher:bn,categoricalChartProps:c,ref:b})}),rY=a=>a.graphicalItems.polarItems,rZ=cd([h7,h8],im),r$=cd([rY,ij,rZ],iq),r_=cd([r$],iu),r0=cd([r_,gS],iw),r1=cd([r0,ij,r$],iy),r2=cd([r0,ij,r$],(a,b,c)=>c.length>0?a.flatMap(a=>c.flatMap(c=>{var d;return{value:cD(a,null!=(d=b.dataKey)?d:c.dataKey),errorDomain:[]}})).filter(Boolean):(null==b?void 0:b.dataKey)!=null?a.map(a=>({value:cD(a,b.dataKey),errorDomain:[]})):a.map(a=>({value:a,errorDomain:[]}))),r3=()=>void 0,r4=cd([ij,iZ,r3,r2,r3],i$),r5=cd([ij,c9,r0,r1,hO,h7,r4],i1),r6=cd([r5,ij,i4],i6);function r7(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function r8(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?r7(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):r7(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}cd([ij,r5,r6,h7],i8);var r9=(a,b)=>b,sa=[],sb=(a,b,c)=>(null==c?void 0:c.length)===0?sa:c,sc=cd([gS,r9,sb],(a,b,c)=>{var d,{chartData:e}=a;if((d=(null==b?void 0:b.data)!=null&&b.data.length>0?b.data:e)&&d.length||null==c||(d=c.map(a=>r8(r8({},b.presentationProps),a.props))),null!=d)return d}),sd=cd([sc,r9,sb],(a,b,c)=>{if(null!=a)return a.map((a,d)=>{var e,f,g=cD(a,b.nameKey,b.name);return f=null!=c&&null!=(e=c[d])&&null!=(e=e.props)&&e.fill?c[d].props.fill:"object"==typeof a&&null!=a&&"fill"in a?a.fill:b.fill,{value:cO(g,b.dataKey),color:f,payload:a,type:b.legendType}})}),se=cd([rY,r9],(a,b)=>{if(a.some(a=>"pie"===a.type&&b.dataKey===a.dataKey&&b.data===a.data))return b}),sf=cd([sc,se,sb,cZ],(a,b,c,d)=>{if(null!=b&&null!=a)return function(a){var b,c,d,{pieSettings:e,displayedData:f,cells:g,offset:h}=a,{cornerRadius:i,startAngle:j,endAngle:k,dataKey:l,nameKey:m,tooltipType:n}=e,o=Math.abs(e.minAngle),p=v(k-j)*Math.min(Math.abs(k-j),360),q=Math.abs(p),r=f.length<=1?0:null!=(b=e.paddingAngle)?b:0,s=f.filter(a=>0!==cD(a,l,0)).length,t=q-s*o-(q>=360?s:s-1)*r,u=f.reduce((a,b)=>{var c=cD(b,l,0);return a+(y(c)?c:0)},0);return u>0&&(c=f.map((a,b)=>{var c,f=cD(a,l,0),k=cD(a,m,b),q=((a,b,c)=>{let d,e,f;var{top:g,left:h,width:i,height:j}=b,k=cA(i,j),l=h+C(a.cx,i,i/2),m=g+C(a.cy,j,j/2),n=C(a.innerRadius,k,0);return{cx:l,cy:m,innerRadius:n,outerRadius:(d=c,e=a.outerRadius,f=k,"function"==typeof e?e(d):C(e,f,.8*f)),maxRadius:a.maxRadius||Math.sqrt(i*i+j*j)/2}})(e,h,a),s=(y(f)?f:0)/u,w=si(si({},a),g&&g[b]&&g[b].props),x=(c=b?d.endAngle+v(p)*r*(0!==f):j)+v(p)*((0!==f?o:0)+s*t),z=(c+x)/2,A=(q.innerRadius+q.outerRadius)/2,B=[{name:k,value:f,payload:w,dataKey:l,type:n}],D=cz(q.cx,q.cy,A,z);return d=si(si(si(si({},e.presentationProps),{},{percent:s,cornerRadius:i,name:k,tooltipPayload:B,midAngle:z,middleRadius:A,tooltipPosition:D},w),q),{},{value:cD(a,l),startAngle:c,endAngle:x,payload:w,paddingAngle:v(p)*r})})),c}({offset:d,pieSettings:b,displayedData:a,cells:c})}),sg=["onMouseEnter","onClick","onMouseLeave"];function sh(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function si(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?sh(Object(c),!0).forEach(function(b){sj(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):sh(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function sj(a,b,c){var d;return(b="symbol"==typeof(d=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"))?d:d+"")in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function sk(){return(sk=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}function sl(a){var b=(0,h.useMemo)(()=>l7(a,!1),[a]),c=(0,h.useMemo)(()=>l5(a.children,qD),[a.children]),d=(0,h.useMemo)(()=>({name:a.name,nameKey:a.nameKey,tooltipType:a.tooltipType,data:a.data,dataKey:a.dataKey,cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,minAngle:a.minAngle,paddingAngle:a.paddingAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius,cornerRadius:a.cornerRadius,legendType:a.legendType,fill:a.fill,presentationProps:b}),[a.cornerRadius,a.cx,a.cy,a.data,a.dataKey,a.endAngle,a.innerRadius,a.minAngle,a.name,a.nameKey,a.outerRadius,a.paddingAngle,a.startAngle,a.tooltipType,a.legendType,a.fill,b]),e=cm(a=>sd(a,d,c));return h.createElement(qh,{legendPayload:e})}function sm(a){var{dataKey:b,nameKey:c,sectors:d,stroke:e,strokeWidth:f,fill:g,name:h,hide:i,tooltipType:j}=a;return{dataDefinedOnItem:null==d?void 0:d.map(a=>a.tooltipPayload),positions:null==d?void 0:d.map(a=>a.tooltipPosition),settings:{stroke:e,strokeWidth:f,fill:g,dataKey:b,nameKey:c,name:cO(h,b),hide:i,type:j,color:g,unit:""}}}function sn(a){var{sectors:b,props:c,showLabels:d}=a,{label:e,labelLine:f,dataKey:g}=c;if(!d||!e||!b)return null;var i=l7(c,!1),j=l7(e,!1),k=l7(f,!1),l="object"==typeof e&&"offsetRadius"in e&&e.offsetRadius||20,m=b.map((a,b)=>{var c,d,m=(a.startAngle+a.endAngle)/2,n=cz(a.cx,a.cy,a.outerRadius+l,m),o=si(si(si(si({},i),a),{},{stroke:"none"},j),{},{index:b,textAnchor:(c=n.x)>(d=a.cx)?"start":c<d?"end":"middle"},n),p=si(si(si(si({},i),a),{},{fill:"none",stroke:a.fill},k),{},{index:b,points:[cz(a.cx,a.cy,a.outerRadius,m),n],key:"line"});return h.createElement(mT,{key:"label-".concat(a.startAngle,"-").concat(a.endAngle,"-").concat(a.midAngle,"-").concat(b)},f&&((a,b)=>{if(h.isValidElement(a))return h.cloneElement(a,b);if("function"==typeof a)return a(b);var c=(0,q.$)("recharts-pie-label-line","boolean"!=typeof a?a.className:"");return h.createElement(oN,sk({},b,{type:"linear",className:c}))})(f,p),((a,b,c)=>{if(h.isValidElement(a))return h.cloneElement(a,b);var d=c;if("function"==typeof a&&(d=a(b),h.isValidElement(d)))return d;var e=(0,q.$)("recharts-pie-label-text","boolean"!=typeof a&&"function"!=typeof a?a.className:"");return h.createElement(na,sk({},b,{alignmentBaseline:"middle",className:e}),d)})(e,o,cD(a,g)))});return h.createElement(mT,{className:"recharts-pie-labels"},m)}function so(a){var{sectors:b,activeShape:c,inactiveShape:d,allOtherPieProps:e,showLabels:f}=a,g=cm(ks),{onMouseEnter:i,onClick:j,onMouseLeave:k}=e,l=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(-1!==b.indexOf(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],-1===b.indexOf(c)&&({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(e,sg),m=rh(i,e.dataKey),n=ri(k),o=rj(j,e.dataKey);return null==b?null:h.createElement(h.Fragment,null,b.map((a,f)=>{if((null==a?void 0:a.startAngle)===0&&(null==a?void 0:a.endAngle)===0&&1!==b.length)return null;var i=c&&String(f)===g,j=i?c:g?d:null,k=si(si({},a),{},{stroke:a.stroke,tabIndex:-1,[cV]:f,[cW]:e.dataKey});return h.createElement(mT,sk({tabIndex:-1,className:"recharts-pie-sector"},l0(l,a,f),{onMouseEnter:m(a,f),onMouseLeave:n(a,f),onClick:o(a,f),key:"sector-".concat(null==a?void 0:a.startAngle,"-").concat(null==a?void 0:a.endAngle,"-").concat(a.midAngle,"-").concat(f)}),h.createElement(q9,sk({option:j,isActive:i,shapeType:"sector"},k)))}),h.createElement(sn,{sectors:b,props:e,showLabels:f}))}function sp(a){var{props:b,previousSectorsRef:c}=a,{sectors:d,isAnimationActive:e,animationBegin:f,animationDuration:g,animationEasing:i,activeShape:j,inactiveShape:k,onAnimationStart:l,onAnimationEnd:m}=b,n=qi(b,"recharts-pie-"),o=c.current,[p,q]=(0,h.useState)(!0),r=(0,h.useCallback)(()=>{"function"==typeof m&&m(),q(!1)},[m]),s=(0,h.useCallback)(()=>{"function"==typeof l&&l(),q(!0)},[l]);return h.createElement(pd,{begin:f,duration:g,isActive:e,easing:i,from:{t:0},to:{t:1},onAnimationStart:s,onAnimationEnd:r,key:n},a=>{var{t:e}=a,f=[],g=(d&&d[0]).startAngle;return d.forEach((a,b)=>{var c=o&&o[b],d=b>0?u()(a,"paddingAngle",0):0;if(c){var h=E(c.endAngle-c.startAngle,a.endAngle-a.startAngle),i=si(si({},a),{},{startAngle:g+d,endAngle:g+h(e)+d});f.push(i),g=i.endAngle}else{var{endAngle:j,startAngle:k}=a,l=E(0,j-k)(e),m=si(si({},a),{},{startAngle:g+d,endAngle:g+l+d});f.push(m),g=m.endAngle}}),c.current=f,h.createElement(mT,null,h.createElement(so,{sectors:f,activeShape:j,inactiveShape:k,allOtherPieProps:b,showLabels:!p}))})}function sq(a){var{sectors:b,isAnimationActive:c,activeShape:d,inactiveShape:e}=a,f=(0,h.useRef)(null),g=f.current;return c&&b&&b.length&&(!g||g!==b)?h.createElement(sp,{props:a,previousSectorsRef:f}):h.createElement(so,{sectors:b,activeShape:d,inactiveShape:e,allOtherPieProps:a,showLabels:!0})}function sr(a){var{hide:b,className:c,rootTabIndex:d}=a,e=(0,q.$)("recharts-pie",c);return b?null:h.createElement(mT,{tabIndex:d,className:e},h.createElement(sq,a))}var ss={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!mC.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function st(a){var b=mv(a,ss),c=(0,h.useMemo)(()=>l5(a.children,qD),[a.children]),d=l7(b,!1),e=(0,h.useMemo)(()=>({name:b.name,nameKey:b.nameKey,tooltipType:b.tooltipType,data:b.data,dataKey:b.dataKey,cx:b.cx,cy:b.cy,startAngle:b.startAngle,endAngle:b.endAngle,minAngle:b.minAngle,paddingAngle:b.paddingAngle,innerRadius:b.innerRadius,outerRadius:b.outerRadius,cornerRadius:b.cornerRadius,legendType:b.legendType,fill:b.fill,presentationProps:d}),[b.cornerRadius,b.cx,b.cy,b.data,b.dataKey,b.endAngle,b.innerRadius,b.minAngle,b.name,b.nameKey,b.outerRadius,b.paddingAngle,b.startAngle,b.tooltipType,b.legendType,b.fill,d]),f=cm(a=>sf(a,e,c));return h.createElement(h.Fragment,null,h.createElement(p6,{fn:sm,args:si(si({},b),{},{sectors:f})}),h.createElement(sr,sk({},b,{sectors:f})))}class su extends h.PureComponent{constructor(){super(...arguments),sj(this,"id",B("recharts-pie-"))}render(){return h.createElement(h.Fragment,null,h.createElement(pO,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),h.createElement(sl,this.props),h.createElement(st,this.props),this.props.children)}}sj(su,"displayName","Pie"),sj(su,"defaultProps",ss);let sv=[{month:"Jan",revenue:12e3,bookings:45},{month:"Feb",revenue:15e3,bookings:52},{month:"Mar",revenue:18e3,bookings:68},{month:"Apr",revenue:22e3,bookings:78},{month:"May",revenue:25e3,bookings:89},{month:"Jun",revenue:28e3,bookings:95}],sw=[{month:"Jan",users:120,providers:15},{month:"Feb",users:145,providers:18},{month:"Mar",users:178,providers:22},{month:"Apr",users:210,providers:28},{month:"May",users:245,providers:32},{month:"Jun",users:280,providers:38}],sx=[{name:"Dog Walking",value:35,color:"#3B82F6"},{name:"Pet Sitting",value:25,color:"#10B981"},{name:"Grooming",value:20,color:"#F59E0B"},{name:"Training",value:12,color:"#EF4444"},{name:"Veterinary",value:8,color:"#8B5CF6"}],sy=[{name:"PetCare Plus",bookings:45,revenue:1125},{name:"Happy Tails",bookings:38,revenue:950},{name:"Furry Friends",bookings:32,revenue:800},{name:"Pet Paradise",bookings:28,revenue:700},{name:"Loving Paws",bookings:25,revenue:625}];function sz(){let{user:a,adminUser:b,loading:c}=(0,f.A)();if((0,g.useRouter)(),c)return(0,e.jsx)("div",{className:"min-h-screen bg-slate-50 flex items-center justify-center",children:(0,e.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!a||!b)return null;let d=sv[sv.length-1].revenue,h=sv[sv.length-2].revenue,q=(0,p.tP)(d,h),r=sv[sv.length-1].bookings,s=sv[sv.length-2].bookings,t=(0,p.tP)(r,s);return(0,e.jsx)(i.A,{title:"Analytics",children:(0,e.jsxs)("div",{className:"px-4 sm:px-6 lg:px-8",children:[(0,e.jsxs)("div",{className:"mb-6",children:[(0,e.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Analytics Dashboard"}),(0,e.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Track performance metrics and business insights"})]}),(0,e.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8",children:[(0,e.jsx)("div",{className:"card p-6",children:(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"flex-shrink-0",children:(0,e.jsx)(j.A,{className:"h-8 w-8 text-green-600"})}),(0,e.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,e.jsxs)("dl",{children:[(0,e.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Monthly Revenue"}),(0,e.jsxs)("dd",{className:"flex items-baseline",children:[(0,e.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:(0,p.vv)(d)}),(0,e.jsxs)("div",{className:`ml-2 flex items-baseline text-sm font-semibold ${q>=0?"text-green-600":"text-red-600"}`,children:[q>=0?(0,e.jsx)(k.A,{className:"h-4 w-4 flex-shrink-0 self-center"}):(0,e.jsx)(l.A,{className:"h-4 w-4 flex-shrink-0 self-center"}),Math.abs(q).toFixed(1),"%"]})]})]})})]})}),(0,e.jsx)("div",{className:"card p-6",children:(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"flex-shrink-0",children:(0,e.jsx)(m.A,{className:"h-8 w-8 text-blue-600"})}),(0,e.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,e.jsxs)("dl",{children:[(0,e.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Monthly Bookings"}),(0,e.jsxs)("dd",{className:"flex items-baseline",children:[(0,e.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:r}),(0,e.jsxs)("div",{className:`ml-2 flex items-baseline text-sm font-semibold ${t>=0?"text-green-600":"text-red-600"}`,children:[t>=0?(0,e.jsx)(k.A,{className:"h-4 w-4 flex-shrink-0 self-center"}):(0,e.jsx)(l.A,{className:"h-4 w-4 flex-shrink-0 self-center"}),Math.abs(t).toFixed(1),"%"]})]})]})})]})}),(0,e.jsx)("div",{className:"card p-6",children:(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"flex-shrink-0",children:(0,e.jsx)(n.A,{className:"h-8 w-8 text-purple-600"})}),(0,e.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,e.jsxs)("dl",{children:[(0,e.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active Users"}),(0,e.jsx)("dd",{className:"text-2xl font-semibold text-gray-900",children:sw[sw.length-1].users})]})})]})}),(0,e.jsx)("div",{className:"card p-6",children:(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"flex-shrink-0",children:(0,e.jsx)(o.A,{className:"h-8 w-8 text-yellow-600"})}),(0,e.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,e.jsxs)("dl",{children:[(0,e.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Active Providers"}),(0,e.jsx)("dd",{className:"text-2xl font-semibold text-gray-900",children:sw[sw.length-1].providers})]})})]})})]}),(0,e.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,e.jsxs)("div",{className:"card p-6",children:[(0,e.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Revenue Trend"}),(0,e.jsx)(K,{width:"100%",height:300,children:(0,e.jsxs)(mB,{data:sv,children:[(0,e.jsx)(nJ,{strokeDasharray:"3 3"}),(0,e.jsx)(nS,{dataKey:"month"}),(0,e.jsx)(n$,{}),(0,e.jsx)(pA,{formatter:a=>[(0,p.vv)(Number(a)),"Revenue"]}),(0,e.jsx)(qA,{type:"monotone",dataKey:"revenue",stroke:"#3B82F6",strokeWidth:2})]})})]}),(0,e.jsxs)("div",{className:"card p-6",children:[(0,e.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"User Growth"}),(0,e.jsx)(K,{width:"100%",height:300,children:(0,e.jsxs)(qC,{data:sw,children:[(0,e.jsx)(nJ,{strokeDasharray:"3 3"}),(0,e.jsx)(nS,{dataKey:"month"}),(0,e.jsx)(n$,{}),(0,e.jsx)(pA,{}),(0,e.jsx)(rP,{dataKey:"users",fill:"#10B981"}),(0,e.jsx)(rP,{dataKey:"providers",fill:"#F59E0B"})]})})]})]}),(0,e.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,e.jsxs)("div",{className:"card p-6",children:[(0,e.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Service Distribution"}),(0,e.jsx)(K,{width:"100%",height:300,children:(0,e.jsxs)(rX,{children:[(0,e.jsx)(su,{data:sx,cx:"50%",cy:"50%",outerRadius:80,fill:"#8884d8",dataKey:"value",label:({name:a,percent:b})=>`${a} ${(100*b).toFixed(0)}%`,children:sx.map((a,b)=>(0,e.jsx)(qD,{fill:a.color},`cell-${b}`))}),(0,e.jsx)(pA,{})]})})]}),(0,e.jsxs)("div",{className:"card p-6",children:[(0,e.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Top Providers"}),(0,e.jsx)("div",{className:"space-y-4",children:sy.map((a,b)=>(0,e.jsxs)("div",{className:"flex items-center justify-between",children:[(0,e.jsxs)("div",{className:"flex items-center",children:[(0,e.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,e.jsx)("span",{className:"text-sm font-medium text-blue-700",children:b+1})}),(0,e.jsxs)("div",{className:"ml-3",children:[(0,e.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a.name}),(0,e.jsxs)("p",{className:"text-sm text-gray-500",children:[a.bookings," bookings"]})]})]}),(0,e.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(0,p.vv)(a.revenue)})]},a.name))})]})]})]})})}},29862:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92923),e=c(27469);b.cloneDeepWith=function(a,b){return d.cloneDeepWith(a,(c,f,g,h)=>{let i=b?.(c,f,g,h);if(null!=i)return i;if("object"==typeof a)switch(Object.prototype.toString.call(a)){case e.numberTag:case e.stringTag:case e.booleanTag:{let b=new a.constructor(a?.valueOf());return d.copyProperties(b,a),b}case e.argumentsTag:{let b={};return d.copyProperties(b,a),b.length=a.length,b[Symbol.iterator]=a[Symbol.iterator],b}default:return}})}},30415:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(53038),f=c(59138),g=c(87509),h=c(57841);b.matchesProperty=function(a,b){switch(typeof a){case"object":Object.is(a?.valueOf(),-0)&&(a="-0");break;case"number":a=e.toKey(a)}return b=f.cloneDeep(b),function(c){let e=g.get(c,a);return void 0===e?h.has(c,a):void 0===b?void 0===e:d.isMatch(e,b)}}},30921:(a,b,c)=>{a.exports=c(71337).range},33731:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(97766),e=c(21424),f=c(26349);b.last=function(a){if(f.isArrayLike(a))return d.last(e.toArray(a))}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},35314:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isUnsafeProperty=function(a){return"__proto__"===a}},36023:(a,b)=>{"use strict";function c(a){return"symbol"==typeof a?1:null===a?2:void 0===a?3:4*(a!=a)}Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.compareValues=(a,b,d)=>{if(a!==b){let e=c(a),f=c(b);if(e===f&&0===e){if(a<b)return"desc"===d?1:-1;if(a>b)return"desc"===d?-1:1}return"desc"===d?f-e:e-f}return 0}},37366:a=>{"use strict";a.exports=require("dns")},37586:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(36023),e=c(76021),f=c(43574);b.orderBy=function(a,b,c,g){if(null==a)return[];c=g?void 0:c,Array.isArray(a)||(a=Object.values(a)),Array.isArray(b)||(b=null==b?[null]:[b]),0===b.length&&(b=[null]),Array.isArray(c)||(c=null==c?[]:[c]),c=c.map(a=>String(a));let h=(a,b)=>{let c=a;for(let a=0;a<b.length&&null!=c;++a)c=c[b[a]];return c},i=b.map(a=>(Array.isArray(a)&&1===a.length&&(a=a[0]),null==a||"function"==typeof a||Array.isArray(a)||e.isKey(a))?a:{key:a,path:f.toPath(a)});return a.map(a=>({original:a,criteria:i.map(b=>{var c,d;return c=b,null==(d=a)||null==c?d:"object"==typeof c&&"key"in c?Object.hasOwn(d,c.key)?d[c.key]:h(d,c.path):"function"==typeof c?c(d):Array.isArray(c)?h(d,c):"object"==typeof d?d[c]:d})})).slice().sort((a,b)=>{for(let e=0;e<i.length;e++){let f=d.compareValues(a.criteria[e],b.criteria[e],c[e]);if(0!==f)return f}return 0}).map(a=>a.original)}},39733:(a,b,c)=>{"use strict";a.exports=c(10907)},40144:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(87509);b.property=function(a){return function(b){return d.get(b,a)}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42066:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(14454);b.isMatch=function(a,b){return d.isMatchWith(a,b,()=>void 0)}},42750:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(15708);b.toFinite=function(a){return a?(a=d.toNumber(a))===1/0||a===-1/0?(a<0?-1:1)*Number.MAX_VALUE:a==a?a:0:0===a?a:0}},43084:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(20911);b.throttle=function(a,b=0,c={}){let{leading:e=!0,trailing:f=!0}=c;return d.debounce(a,b,{leading:e,maxWait:b,trailing:f})}},43574:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toPath=function(a){let b=[],c=a.length;if(0===c)return b;let d=0,e="",f="",g=!1;for(46===a.charCodeAt(0)&&(b.push(""),d++);d<c;){let h=a[d];f?"\\"===h&&d+1<c?e+=a[++d]:h===f?f="":e+=h:g?'"'===h||"'"===h?f=h:"]"===h?(g=!1,b.push(e),e=""):e+=h:"["===h?(g=!0,e&&(b.push(e),e="")):"."===h?e&&(b.push(e),e=""):e+=h,d++}return e&&b.push(e),b}},45263:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(59618),e=c(92681),f=c(90830),g=c(17617);b.uniqBy=function(a,b=e.identity){return f.isArrayLikeObject(a)?d.uniqBy(Array.from(a),g.iteratee(b)):[]}},48130:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isLength=function(a){return Number.isSafeInteger(a)&&a>=0}},49899:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isObjectLike=function(a){return"object"==typeof a&&null!==a}},52371:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(92923);b.cloneDeep=function(a){return d.cloneDeepWithImpl(a,void 0,a,new Map,void 0)}},53038:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.toKey=function(a){return"string"==typeof a||"symbol"==typeof a?a:Object.is(a?.valueOf?.(),-0)?"-0":String(a)}},53332:(a,b,c)=>{"use strict";var d=c(43210),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},55100:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.debounce=function(a,b,{signal:c,edges:d}={}){let e,f=null,g=null!=d&&d.includes("leading"),h=null==d||d.includes("trailing"),i=()=>{null!==f&&(a.apply(e,f),e=void 0,f=null)},j=null,k=()=>{null!=j&&clearTimeout(j),j=setTimeout(()=>{j=null,h&&i(),l()},b)},l=()=>{null!==j&&(clearTimeout(j),j=null),e=void 0,f=null},m=function(...a){if(c?.aborted)return;e=this,f=a;let b=null==j;k(),g&&b&&i()};return m.schedule=k,m.cancel=l,m.flush=()=>{i()},c?.addEventListener("abort",l,{once:!0}),m}},55296:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(43210);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))})},55511:a=>{"use strict";a.exports=require("crypto")},57379:(a,b,c)=>{"use strict";a.exports=c(53332)},57841:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(76431),e=c(98150),f=c(29243),g=c(43574);b.has=function(a,b){let c;if(0===(c=Array.isArray(b)?b:"string"==typeof b&&d.isDeepKey(b)&&a?.[b]==null?g.toPath(b):[b]).length)return!1;let h=a;for(let a=0;a<c.length;a++){let b=c[a];if((null==h||!Object.hasOwn(h,b))&&!((Array.isArray(h)||f.isArguments(h))&&e.isIndex(b)&&b<h.length))return!1;h=h[b]}return!0}},59138:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(29862);b.cloneDeep=function(a){return d.cloneDeepWith(a)}},59618:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.uniqBy=function(a,b){let c=new Map;for(let d=0;d<a.length;d++){let e=a[d],f=b(e);c.has(f)||c.set(f,e)}return Array.from(c.values())}},60324:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isPlainObject=function(a){if("object"!=typeof a||null==a)return!1;if(null===Object.getPrototypeOf(a))return!0;if("[object Object]"!==Object.prototype.toString.call(a)){let b=a[Symbol.toStringTag];return null!=b&&!!Object.getOwnPropertyDescriptor(a,Symbol.toStringTag)?.writable&&a.toString()===`[object ${b}]`}let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66777:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(98150),e=c(26349),f=c(1640),g=c(1706);b.isIterateeCall=function(a,b,c){return!!f.isObject(c)&&(!!("number"==typeof b&&e.isArrayLike(c)&&d.isIndex(b))&&b<c.length||"string"==typeof b&&b in c)&&g.eq(c[b],a)}},67766:(a,b,c)=>{a.exports=c(43084).throttle},69404:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(175),e=c(91653),f=c(91428),g=c(27469),h=c(1706);b.isEqualWith=function(a,b,c){return function a(b,c,i,j,k,l,m){let n=m(b,c,i,j,k,l);if(void 0!==n)return n;if(typeof b==typeof c)switch(typeof b){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return b===c;case"number":return b===c||Object.is(b,c)}return function b(c,i,j,k){if(Object.is(c,i))return!0;let l=f.getTag(c),m=f.getTag(i);if(l===g.argumentsTag&&(l=g.objectTag),m===g.argumentsTag&&(m=g.objectTag),l!==m)return!1;switch(l){case g.stringTag:return c.toString()===i.toString();case g.numberTag:{let a=c.valueOf(),b=i.valueOf();return h.eq(a,b)}case g.booleanTag:case g.dateTag:case g.symbolTag:return Object.is(c.valueOf(),i.valueOf());case g.regexpTag:return c.source===i.source&&c.flags===i.flags;case g.functionTag:return c===i}let n=(j=j??new Map).get(c),o=j.get(i);if(null!=n&&null!=o)return n===i;j.set(c,i),j.set(i,c);try{switch(l){case g.mapTag:if(c.size!==i.size)return!1;for(let[b,d]of c.entries())if(!i.has(b)||!a(d,i.get(b),b,c,i,j,k))return!1;return!0;case g.setTag:{if(c.size!==i.size)return!1;let b=Array.from(c.values()),d=Array.from(i.values());for(let e=0;e<b.length;e++){let f=b[e],g=d.findIndex(b=>a(f,b,void 0,c,i,j,k));if(-1===g)return!1;d.splice(g,1)}return!0}case g.arrayTag:case g.uint8ArrayTag:case g.uint8ClampedArrayTag:case g.uint16ArrayTag:case g.uint32ArrayTag:case g.bigUint64ArrayTag:case g.int8ArrayTag:case g.int16ArrayTag:case g.int32ArrayTag:case g.bigInt64ArrayTag:case g.float32ArrayTag:case g.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(c)!==Buffer.isBuffer(i)||c.length!==i.length)return!1;for(let b=0;b<c.length;b++)if(!a(c[b],i[b],b,c,i,j,k))return!1;return!0;case g.arrayBufferTag:if(c.byteLength!==i.byteLength)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.dataViewTag:if(c.byteLength!==i.byteLength||c.byteOffset!==i.byteOffset)return!1;return b(new Uint8Array(c),new Uint8Array(i),j,k);case g.errorTag:return c.name===i.name&&c.message===i.message;case g.objectTag:{if(!(b(c.constructor,i.constructor,j,k)||d.isPlainObject(c)&&d.isPlainObject(i)))return!1;let f=[...Object.keys(c),...e.getSymbols(c)],g=[...Object.keys(i),...e.getSymbols(i)];if(f.length!==g.length)return!1;for(let b=0;b<f.length;b++){let d=f[b],e=c[d];if(!Object.hasOwn(i,d))return!1;let g=i[d];if(!a(e,g,d,c,i,j,k))return!1}return!0}default:return!1}}finally{j.delete(c),j.delete(i)}}(b,c,l,m)}(a,b,void 0,void 0,void 0,void 0,c)}},70149:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(43210);let e=d.forwardRef(function({title:a,titleId:b,...c},e){return d.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":b},c),a?d.createElement("title",{id:b},a):null,d.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},71337:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(66777),e=c(42750);b.range=function(a,b,c){c&&"number"!=typeof c&&d.isIterateeCall(a,b,c)&&(b=c=void 0),a=e.toFinite(a),void 0===b?(b=a,a=0):b=e.toFinite(b),c=void 0===c?a<b?1:-1:e.toFinite(c);let f=Math.max(Math.ceil((b-a)/(c||1)),0),g=Array(f);for(let b=0;b<f;b++)g[b]=a,a+=c;return g}},71599:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,4080)),"/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/analytics/page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/augment-projects/fetchly admin/fetchly-admin/src/app/dashboard/analytics/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/analytics/page",pathname:"/dashboard/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/analytics/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},71625:(a,b,c)=>{Promise.resolve().then(c.bind(c,29808))},73496:a=>{"use strict";a.exports=require("http2")},74075:a=>{"use strict";a.exports=require("zlib")},74838:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(42066),e=c(52371);b.matches=function(a){return a=e.cloneDeep(a),b=>d.isMatch(b,a)}},75446:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(37586),e=c(28382),f=c(66777);b.sortBy=function(a,...b){let c=b.length;return c>1&&f.isIterateeCall(a,b[0],b[1])?b=[]:c>2&&f.isIterateeCall(b[0],b[1],b[2])&&(b=[b[0]]),d.orderBy(a,e.flatten(b),["asc"])}},76021:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(95819),e=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,f=/^\w*$/;b.isKey=function(a,b){return!Array.isArray(a)&&(!!("number"==typeof a||"boolean"==typeof a||null==a||d.isSymbol(a))||"string"==typeof a&&(f.test(a)||!e.test(a))||null!=b&&Object.hasOwn(b,a))}},76431:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isDeepKey=function(a){switch(typeof a){case"number":case"symbol":return!1;case"string":return a.includes(".")||a.includes("[")||a.includes("]")}}},79551:a=>{"use strict";a.exports=require("url")},81353:(a,b,c)=>{Promise.resolve().then(c.bind(c,4080))},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87509:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(35314),e=c(76431),f=c(53038),g=c(43574);b.get=function a(b,c,h){if(null==b)return h;switch(typeof c){case"string":{if(d.isUnsafeProperty(c))return h;let f=b[c];if(void 0===f)if(e.isDeepKey(c))return a(b,g.toPath(c),h);else return h;return f}case"number":case"symbol":{"number"==typeof c&&(c=f.toKey(c));let a=b[c];if(void 0===a)return h;return a}default:{if(Array.isArray(c)){var i=b,j=c,k=h;if(0===j.length)return k;let a=i;for(let b=0;b<j.length;b++){if(null==a||d.isUnsafeProperty(j[b]))return k;a=a[j[b]]}return void 0===a?k:a}if(c=Object.is(c?.valueOf(),-0)?"-0":String(c),d.isUnsafeProperty(c))return h;let a=b[c];if(void 0===a)return h;return a}}}},90015:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.noop=function(){}},90830:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(26349),e=c(49899);b.isArrayLikeObject=function(a){return e.isObjectLike(a)&&d.isArrayLike(a)}},91428:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getTag=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":Object.prototype.toString.call(a)}},91645:a=>{"use strict";a.exports=require("net")},91653:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.getSymbols=function(a){return Object.getOwnPropertySymbols(a).filter(b=>Object.prototype.propertyIsEnumerable.call(a,b))}},92292:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(69404),e=c(90015);b.isEqual=function(a,b){return d.isEqualWith(a,b,e.noop)}},92681:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.identity=function(a){return a}},92867:(a,b,c)=>{a.exports=c(60324).isPlainObject},92923:(a,b,c)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let d=c(91653),e=c(91428),f=c(27469),g=c(23457),h=c(21251);function i(a,b,c,d=new Map,k){let l=k?.(a,b,c,d);if(null!=l)return l;if(g.isPrimitive(a))return a;if(d.has(a))return d.get(a);if(Array.isArray(a)){let b=Array(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return Object.hasOwn(a,"index")&&(b.index=a.index),Object.hasOwn(a,"input")&&(b.input=a.input),b}if(a instanceof Date)return new Date(a.getTime());if(a instanceof RegExp){let b=new RegExp(a.source,a.flags);return b.lastIndex=a.lastIndex,b}if(a instanceof Map){let b=new Map;for(let[e,f]of(d.set(a,b),a))b.set(e,i(f,e,c,d,k));return b}if(a instanceof Set){let b=new Set;for(let e of(d.set(a,b),a))b.add(i(e,void 0,c,d,k));return b}if("undefined"!=typeof Buffer&&Buffer.isBuffer(a))return a.subarray();if(h.isTypedArray(a)){let b=new(Object.getPrototypeOf(a)).constructor(a.length);d.set(a,b);for(let e=0;e<a.length;e++)b[e]=i(a[e],e,c,d,k);return b}if(a instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&a instanceof SharedArrayBuffer)return a.slice(0);if(a instanceof DataView){let b=new DataView(a.buffer.slice(0),a.byteOffset,a.byteLength);return d.set(a,b),j(b,a,c,d,k),b}if("undefined"!=typeof File&&a instanceof File){let b=new File([a],a.name,{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Blob){let b=new Blob([a],{type:a.type});return d.set(a,b),j(b,a,c,d,k),b}if(a instanceof Error){let b=new a.constructor;return d.set(a,b),b.message=a.message,b.name=a.name,b.stack=a.stack,b.cause=a.cause,j(b,a,c,d,k),b}if("object"==typeof a&&function(a){switch(e.getTag(a)){case f.argumentsTag:case f.arrayTag:case f.arrayBufferTag:case f.dataViewTag:case f.booleanTag:case f.dateTag:case f.float32ArrayTag:case f.float64ArrayTag:case f.int8ArrayTag:case f.int16ArrayTag:case f.int32ArrayTag:case f.mapTag:case f.numberTag:case f.objectTag:case f.regexpTag:case f.setTag:case f.stringTag:case f.symbolTag:case f.uint8ArrayTag:case f.uint8ClampedArrayTag:case f.uint16ArrayTag:case f.uint32ArrayTag:return!0;default:return!1}}(a)){let b=Object.create(Object.getPrototypeOf(a));return d.set(a,b),j(b,a,c,d,k),b}return a}function j(a,b,c=a,e,f){let g=[...Object.keys(b),...d.getSymbols(b)];for(let d=0;d<g.length;d++){let h=g[d],j=Object.getOwnPropertyDescriptor(a,h);(null==j||j.writable)&&(a[h]=i(b[h],h,c,e,f))}}b.cloneDeepWith=function(a,b){return i(a,void 0,a,new Map,b)},b.cloneDeepWithImpl=i,b.copyProperties=j},94735:a=>{"use strict";a.exports=require("events")},95819:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.isSymbol=function(a){return"symbol"==typeof a||a instanceof Symbol}},97668:(a,b)=>{"use strict";var c="function"==typeof Symbol&&Symbol.for,d=c?Symbol.for("react.element"):60103,e=c?Symbol.for("react.portal"):60106,f=c?Symbol.for("react.fragment"):60107,g=c?Symbol.for("react.strict_mode"):60108,h=c?Symbol.for("react.profiler"):60114,i=c?Symbol.for("react.provider"):60109,j=c?Symbol.for("react.context"):60110,k=c?Symbol.for("react.async_mode"):60111,l=c?Symbol.for("react.concurrent_mode"):60111,m=c?Symbol.for("react.forward_ref"):60112,n=c?Symbol.for("react.suspense"):60113,o=(c&&Symbol.for("react.suspense_list"),c?Symbol.for("react.memo"):60115),p=c?Symbol.for("react.lazy"):60116;function q(a){if("object"==typeof a&&null!==a){var b=a.$$typeof;switch(b){case d:switch(a=a.type){case k:case l:case f:case h:case g:case n:return a;default:switch(a=a&&a.$$typeof){case j:case m:case p:case o:case i:return a;default:return b}}case e:return b}}}c&&Symbol.for("react.block"),c&&Symbol.for("react.fundamental"),c&&Symbol.for("react.responder"),c&&Symbol.for("react.scope");b.isFragment=function(a){return q(a)===f}},97766:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"}),b.last=function(a){return a[a.length-1]}},98150:(a,b)=>{"use strict";Object.defineProperty(b,Symbol.toStringTag,{value:"Module"});let c=/^(?:0|[1-9]\d*)$/;b.isIndex=function(a,b=Number.MAX_SAFE_INTEGER){switch(typeof a){case"number":return Number.isInteger(a)&&a>=0&&a<b;case"symbol":return!1;case"string":return c.test(a)}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,698,180,852],()=>b(b.s=71599));module.exports=c})();