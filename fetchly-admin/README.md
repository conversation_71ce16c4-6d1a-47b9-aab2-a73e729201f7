# Fetchly Admin Dashboard

A comprehensive administrative portal for the Fetchly pet services platform. Built with Next.js 15, TypeScript, Tailwind CSS, Firebase, and Stripe integration.

## Features

### 🎯 Core Admin Features
- **Dashboard Overview** - Key metrics, recent activities, and quick actions
- **User Management** - View, manage, and monitor all platform users
- **Provider Management** - Approve/reject providers, manage verifications
- **Booking Management** - Monitor all bookings and appointments
- **Payment Management** - Handle transactions, refunds, and withdrawals
- **Analytics Dashboard** - Comprehensive business insights and charts
- **Settings Panel** - Platform configuration and preferences

### 🔐 Authentication & Security
- Firebase Authentication integration
- Role-based access control (Admin, Super Admin, Moderator)
- Secure session management
- Protected routes and API endpoints

### 💳 Payment Integration
- Stripe payment processing
- Transaction monitoring
- Withdrawal request management
- Revenue tracking and analytics

### 📊 Analytics & Reporting
- Real-time dashboard metrics
- Revenue and booking trends
- User growth analytics
- Service distribution insights
- Top provider performance

### 🎨 Design & UX
- Fetchly-inspired design system
- Responsive layout for all devices
- Modern UI components
- Intuitive navigation
- Professional color scheme

## Tech Stack

- **Framework**: Next.js 15 with Turbopack
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Authentication**: Firebase Auth
- **Database**: Firebase Firestore
- **Payments**: Stripe
- **Charts**: Recharts
- **Icons**: Heroicons
- **State Management**: React Context API

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Firebase project
- Stripe account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fetchly-admin
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**

   Copy the `.env.local` file and update with your credentials:
   ```bash
   cp .env.local .env.local.example
   ```

   Update the following variables in `.env.local`:
   ```env
   # Firebase Configuration
   NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

   # Stripe Configuration
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
   STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

   # Admin Configuration
   ADMIN_SECRET_KEY=your_admin_secret_key
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Firebase Setup**

   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Authentication with Email/Password
   - Create Firestore database
   - Add your domain to authorized domains
   - Create an `admins` collection in Firestore with admin user documents

5. **Stripe Setup**

   - Create a Stripe account at [Stripe Dashboard](https://dashboard.stripe.com)
   - Get your API keys from the developers section
   - Set up webhooks for payment events

6. **Run the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
fetchly-admin/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── dashboard/          # Dashboard pages
│   │   ├── login/              # Authentication
│   │   ├── globals.css         # Global styles
│   │   ├── layout.tsx          # Root layout
│   │   └── page.tsx            # Home page
│   ├── components/             # Reusable components
│   │   └── layout/             # Layout components
│   ├── contexts/               # React contexts
│   │   └── AuthContext.tsx     # Authentication context
│   ├── lib/                    # Utilities and configurations
│   │   ├── firebase.ts         # Firebase config
│   │   └── utils.ts            # Utility functions
│   └── types/                  # TypeScript type definitions
│       └── index.ts            # Type definitions
├── public/                     # Static assets
├── .env.local                  # Environment variables
├── next.config.ts              # Next.js configuration
├── tailwind.config.js          # Tailwind CSS configuration
└── package.json                # Dependencies and scripts
```

## Usage

### Admin Authentication

1. Create admin users in Firebase Firestore:
   ```javascript
   // Add to 'admins' collection
   {
     email: "<EMAIL>",
     displayName: "Admin User",
     role: "super_admin",
     permissions: ["manage_users", "manage_providers", "manage_bookings", "manage_payments"],
     createdAt: new Date(),
     lastLoginAt: null
   }
   ```

2. The admin user must also exist in Firebase Authentication

### Key Features Usage

- **Dashboard**: Overview of platform metrics and recent activities
- **Users**: Search, filter, and manage user accounts
- **Providers**: Approve/reject provider applications and manage verifications
- **Bookings**: Monitor booking status and handle disputes
- **Payments**: Process withdrawals and monitor transactions
- **Analytics**: View business insights and performance metrics
- **Settings**: Configure platform settings and preferences

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- Render

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]

---

Built with ❤️ for the Fetchly pet services platform.
