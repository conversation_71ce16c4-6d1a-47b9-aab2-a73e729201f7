{"name": "fetchly-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@stripe/stripe-js": "^7.8.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^12.0.0", "firebase-admin": "^13.4.0", "lucide-react": "^0.536.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.0", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "firebase-tools": "^14.11.2", "tailwindcss": "^4", "typescript": "^5"}}