const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, Timestamp } = require('firebase/firestore');

// Your Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyC5FyKjWvSxqKr8OqYxqxqxqxqxqxqxqxq",
  authDomain: "fetchly-724b6.firebaseapp.com",
  projectId: "fetchly-724b6",
  storageBucket: "fetchly-724b6.firebasestorage.app",
  messagingSenderId: "192530321990",
  appId: "1:192530321990:web:abcdefghijklmnop"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function createCollections() {
  try {
    console.log('🔥 Creating missing Firestore collections...\n');

    // 1. Reviews Collection
    console.log('📝 Creating reviews collection...');
    const reviewsData = [
      {
        id: 'review1',
        userId: 'user123',
        providerId: 'provider456',
        bookingId: 'booking789',
        rating: 5,
        title: 'Excellent Pet Care Service!',
        comment: 'Amazing service! My dog Max was so well taken care of. The provider was professional and caring.',
        userName: 'Sarah <PERSON>',
        userPhoto: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        providerName: 'PetCare Plus',
        serviceType: 'Dog Walking',
        isApproved: true,
        isFeatured: true,
        helpfulCount: 12,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'review2',
        userId: 'user456',
        providerId: 'provider789',
        bookingId: 'booking123',
        rating: 4,
        title: 'Great Pet Sitting Experience',
        comment: 'Very reliable pet sitter. My cat Luna was comfortable and happy. Will book again!',
        userName: 'Mike Chen',
        userPhoto: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        providerName: 'Happy Paws Pet Sitting',
        serviceType: 'Pet Sitting',
        isApproved: true,
        isFeatured: false,
        helpfulCount: 8,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ];

    for (const review of reviewsData) {
      await addDoc(collection(db, 'reviews'), review);
    }
    console.log('✅ Reviews collection created with sample data');

    // 2. Featured Providers Collection
    console.log('🌟 Creating featured providers collection...');
    const featuredProvidersData = [
      {
        id: 'featured1',
        providerId: 'provider456',
        providerName: 'PetCare Plus',
        providerLogo: 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=200',
        description: 'Premium pet care services with certified professionals',
        services: ['Dog Walking', 'Pet Sitting', 'Grooming'],
        rating: 4.9,
        reviewCount: 127,
        location: 'New York, NY',
        isActive: true,
        featuredUntil: new Date('2024-12-31'),
        priority: 1,
        badgeType: 'premium',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'featured2',
        providerId: 'provider789',
        providerName: 'Happy Paws Pet Sitting',
        providerLogo: 'https://images.unsplash.com/photo-**********-03cce0bbc87b?w=200',
        description: 'Trusted pet sitting services in your neighborhood',
        services: ['Pet Sitting', 'Dog Walking', 'Pet Training'],
        rating: 4.8,
        reviewCount: 89,
        location: 'Los Angeles, CA',
        isActive: true,
        featuredUntil: new Date('2024-11-30'),
        priority: 2,
        badgeType: 'verified',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ];

    for (const featured of featuredProvidersData) {
      await addDoc(collection(db, 'featuredProviders'), featured);
    }
    console.log('✅ Featured providers collection created with sample data');

    // 3. Blog Collection
    console.log('📰 Creating blog collection...');
    const blogData = [
      {
        id: 'blog1',
        title: '10 Essential Tips for First-Time Pet Owners',
        slug: '10-essential-tips-first-time-pet-owners',
        excerpt: 'Getting your first pet? Here are the essential tips every new pet owner should know to ensure a happy and healthy relationship with their furry friend.',
        content: 'Full blog content would go here...',
        featuredImage: 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=800',
        author: {
          name: 'Dr. Emily Rodriguez',
          avatar: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=150',
          bio: 'Veterinarian with 10+ years experience'
        },
        category: 'Pet Care',
        tags: ['pet care', 'first time owners', 'tips', 'pets'],
        status: 'published',
        isPublished: true,
        isFeatured: true,
        viewCount: 1247,
        likeCount: 89,
        commentCount: 23,
        seoTitle: '10 Essential Tips for First-Time Pet Owners | Fetchly Blog',
        seoDescription: 'Complete guide for first-time pet owners with essential tips for pet care, training, and building a strong bond with your new companion.',
        publishedAt: Timestamp.now(),
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'blog2',
        title: 'How to Choose the Right Pet Sitter for Your Furry Friend',
        slug: 'how-to-choose-right-pet-sitter',
        excerpt: 'Finding the perfect pet sitter can be challenging. Learn what to look for and questions to ask to ensure your pet gets the best care.',
        content: 'Full blog content would go here...',
        featuredImage: 'https://images.unsplash.com/photo-**********-03cce0bbc87b?w=800',
        author: {
          name: 'Sarah Mitchell',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
          bio: 'Pet care specialist and blogger'
        },
        category: 'Pet Sitting',
        tags: ['pet sitting', 'pet care', 'tips', 'safety'],
        status: 'published',
        isPublished: true,
        isFeatured: false,
        viewCount: 892,
        likeCount: 67,
        commentCount: 15,
        seoTitle: 'How to Choose the Right Pet Sitter | Fetchly Blog',
        seoDescription: 'Expert guide on selecting the perfect pet sitter. Tips, questions to ask, and red flags to avoid.',
        publishedAt: Timestamp.now(),
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ];

    for (const blog of blogData) {
      await addDoc(collection(db, 'blog'), blog);
    }
    console.log('✅ Blog collection created with sample data');

    console.log('\n🎉 All collections created successfully!');
    console.log('📊 Collections created:');
    console.log('   - reviews (2 sample reviews)');
    console.log('   - featuredProviders (2 featured providers)');
    console.log('   - blog (2 blog posts)');
    
  } catch (error) {
    console.error('💥 Error creating collections:', error);
  }
}

createCollections().then(() => {
  console.log('\n✅ Collection creation complete!');
  process.exit(0);
}).catch(error => {
  console.error('❌ Failed to create collections:', error);
  process.exit(1);
});
