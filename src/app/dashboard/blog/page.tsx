'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { formatDate } from '@/lib/utils';
import { getAllBlogPosts, updateBlogPostStatus } from '@/lib/firestore';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage?: string;
  author: {
    name: string;
    avatar?: string;
    bio: string;
  };
  category: string;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  isPublished: boolean;
  isFeatured: boolean;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  seoTitle: string;
  seoDescription: string;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt?: Date;
}

export default function BlogPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loadingPosts, setLoadingPosts] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft' | 'featured'>('all');

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      if (user && adminUser) {
        try {
          setLoadingPosts(true);
          console.log('📰 Fetching real blog posts from Firestore...');
          
          const realPosts = await getAllBlogPosts(100);
          setBlogPosts(realPosts as BlogPost[]);
          
          console.log(`✅ Loaded ${realPosts.length} blog posts`);
        } catch (error) {
          console.error('💥 Error fetching blog posts:', error);
          setBlogPosts([]);
        } finally {
          setLoadingPosts(false);
        }
      }
    };

    fetchBlogPosts();
  }, [user, adminUser]);

  const handlePublishPost = async (postId: string, isPublished: boolean) => {
    try {
      const post = blogPosts.find(p => p.id === postId);
      if (post) {
        await updateBlogPostStatus(postId, !isPublished, post.isFeatured);
        setBlogPosts(posts => 
          posts.map(p => 
            p.id === postId ? { 
              ...p, 
              isPublished: !isPublished, 
              status: !isPublished ? 'published' : 'draft',
              publishedAt: !isPublished ? new Date() : undefined
            } : p
          )
        );
      }
    } catch (error) {
      console.error('Error updating blog post status:', error);
    }
  };

  const handleFeaturePost = async (postId: string, isFeatured: boolean) => {
    try {
      const post = blogPosts.find(p => p.id === postId);
      if (post) {
        await updateBlogPostStatus(postId, post.isPublished, !isFeatured);
        setBlogPosts(posts => 
          posts.map(p => 
            p.id === postId ? { ...p, isFeatured: !isFeatured } : p
          )
        );
      }
    } catch (error) {
      console.error('Error updating blog post featured status:', error);
    }
  };

  if (loading || loadingPosts) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {loading ? 'Authenticating...' : 'Loading blog posts from Firestore...'}
          </p>
        </div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.author.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'published' && post.isPublished) ||
                         (statusFilter === 'draft' && !post.isPublished) ||
                         (statusFilter === 'featured' && post.isFeatured);
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DashboardLayout title="Blog Management">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Blog Posts</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage blog content and articles
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <div className="text-sm text-gray-600">
              Total: {blogPosts.length} | Published: {blogPosts.filter(p => p.isPublished).length} | Featured: {blogPosts.filter(p => p.isFeatured).length}
            </div>
            <button className="btn btn-primary">
              <PlusIcon className="h-4 w-4 mr-2" />
              New Post
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search blog posts..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
              >
                <option value="all">All Posts</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="featured">Featured</option>
              </select>
            </div>
          </div>
        </div>

        {/* Blog Posts List */}
        {filteredPosts.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <PencilIcon className="mx-auto h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {blogPosts.length === 0 ? 'No Blog Posts' : 'No Posts Match Your Search'}
            </h3>
            <p className="text-gray-600 mb-4">
              {blogPosts.length === 0 
                ? 'No blog posts have been created yet. Start writing to engage your audience.'
                : 'Try adjusting your search terms or filters.'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredPosts.map((post) => (
              <div key={post.id} className="card p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {post.featuredImage && (
                      <div className="h-20 w-32 rounded-lg bg-gray-200 overflow-hidden flex-shrink-0">
                        <img src={post.featuredImage} alt={post.title} className="h-20 w-32 object-cover" />
                      </div>
                    )}
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{post.title}</h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(post.status)}`}>
                          {post.status}
                        </span>
                        {post.isFeatured && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            Featured
                          </span>
                        )}
                      </div>
                      
                      <p className="text-gray-600 mb-3 line-clamp-2">{post.excerpt}</p>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center space-x-2">
                          {post.author.avatar && (
                            <img src={post.author.avatar} alt={post.author.name} className="h-6 w-6 rounded-full" />
                          )}
                          <span>{post.author.name}</span>
                        </div>
                        <span>{post.category}</span>
                        <div className="flex items-center space-x-4">
                          <span>{post.viewCount} views</span>
                          <span>{post.likeCount} likes</span>
                          <span>{post.commentCount} comments</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mb-3">
                        {post.tags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            #{tag}
                          </span>
                        ))}
                        {post.tags.length > 3 && (
                          <span className="text-xs text-gray-500">+{post.tags.length - 3} more</span>
                        )}
                      </div>
                      
                      <div className="text-sm text-gray-500">
                        {post.publishedAt ? `Published: ${formatDate(post.publishedAt)}` : `Created: ${formatDate(post.createdAt)}`}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button className="btn btn-sm btn-secondary">
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button className="btn btn-sm btn-secondary">
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleFeaturePost(post.id, post.isFeatured)}
                      className={`btn btn-sm ${post.isFeatured ? 'btn-secondary' : 'btn-outline'}`}
                    >
                      <StarIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handlePublishPost(post.id, post.isPublished)}
                      className={`btn btn-sm ${post.isPublished ? 'btn-secondary' : 'btn-primary'}`}
                    >
                      {post.isPublished ? (
                        <XCircleIcon className="h-4 w-4" />
                      ) : (
                        <CheckCircleIcon className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
