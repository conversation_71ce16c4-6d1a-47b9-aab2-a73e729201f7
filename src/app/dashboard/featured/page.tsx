'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  MagnifyingGlassIcon,
  StarIcon,
  MapPinIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { formatDate } from '@/lib/utils';
import { getAllFeaturedProviders, updateFeaturedProvider } from '@/lib/firestore';

interface FeaturedProvider {
  id: string;
  providerId: string;
  providerName: string;
  providerLogo?: string;
  description: string;
  services: string[];
  rating: number;
  reviewCount: number;
  location: string;
  isActive: boolean;
  featuredUntil: Date;
  priority: number;
  badgeType: 'premium' | 'verified' | 'featured';
  createdAt: Date;
  updatedAt?: Date;
}

export default function FeaturedProvidersPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [featuredProviders, setFeaturedProviders] = useState<FeaturedProvider[]>([]);
  const [loadingProviders, setLoadingProviders] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  useEffect(() => {
    const fetchFeaturedProviders = async () => {
      if (user && adminUser) {
        try {
          setLoadingProviders(true);
          console.log('🌟 Fetching real featured providers from Firestore...');
          
          const realProviders = await getAllFeaturedProviders(100);
          setFeaturedProviders(realProviders as FeaturedProvider[]);
          
          console.log(`✅ Loaded ${realProviders.length} featured providers`);
        } catch (error) {
          console.error('💥 Error fetching featured providers:', error);
          setFeaturedProviders([]);
        } finally {
          setLoadingProviders(false);
        }
      }
    };

    fetchFeaturedProviders();
  }, [user, adminUser]);

  const handleToggleActive = async (providerId: string, isActive: boolean) => {
    try {
      await updateFeaturedProvider(providerId, !isActive);
      setFeaturedProviders(providers => 
        providers.map(provider => 
          provider.id === providerId ? { ...provider, isActive: !isActive } : provider
        )
      );
    } catch (error) {
      console.error('Error updating featured provider status:', error);
    }
  };

  const handleUpdatePriority = async (providerId: string, newPriority: number) => {
    try {
      const provider = featuredProviders.find(p => p.id === providerId);
      if (provider) {
        await updateFeaturedProvider(providerId, provider.isActive, newPriority);
        setFeaturedProviders(providers => 
          providers.map(p => 
            p.id === providerId ? { ...p, priority: newPriority } : p
          ).sort((a, b) => a.priority - b.priority)
        );
      }
    } catch (error) {
      console.error('Error updating provider priority:', error);
    }
  };

  if (loading || loadingProviders) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {loading ? 'Authenticating...' : 'Loading featured providers from Firestore...'}
          </p>
        </div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const filteredProviders = featuredProviders.filter(provider => {
    const matchesSearch = provider.providerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.services.some(service => service.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && provider.isActive) ||
                         (statusFilter === 'inactive' && !provider.isActive);
    return matchesSearch && matchesStatus;
  });

  const getBadgeColor = (badgeType: string) => {
    switch (badgeType) {
      case 'premium': return 'bg-purple-100 text-purple-800';
      case 'verified': return 'bg-blue-100 text-blue-800';
      case 'featured': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DashboardLayout title="Featured Providers">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Featured Providers</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage featured providers displayed on the homepage
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <div className="text-sm text-gray-600">
              Total: {featuredProviders.length} | Active: {featuredProviders.filter(p => p.isActive).length}
            </div>
            <button className="btn btn-primary">
              Add Featured Provider
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search featured providers..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        {/* Featured Providers List */}
        {filteredProviders.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <StarIcon className="mx-auto h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {featuredProviders.length === 0 ? 'No Featured Providers' : 'No Providers Match Your Search'}
            </h3>
            <p className="text-gray-600 mb-4">
              {featuredProviders.length === 0 
                ? 'No providers are currently featured. Add providers to showcase them on the homepage.'
                : 'Try adjusting your search terms or filters.'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredProviders.map((provider) => (
              <div key={provider.id} className="card p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center overflow-hidden">
                      {provider.providerLogo ? (
                        <img src={provider.providerLogo} alt={provider.providerName} className="h-16 w-16 object-cover" />
                      ) : (
                        <span className="text-lg font-medium text-gray-600">
                          {provider.providerName.charAt(0)}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{provider.providerName}</h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBadgeColor(provider.badgeType)}`}>
                          {provider.badgeType}
                        </span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          provider.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {provider.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      
                      <p className="text-gray-600 mb-3">{provider.description}</p>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center space-x-1">
                          <StarIcon className="h-4 w-4 text-yellow-400" />
                          <span>{provider.rating} ({provider.reviewCount} reviews)</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPinIcon className="h-4 w-4" />
                          <span>{provider.location}</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mb-3">
                        {provider.services.map((service, index) => (
                          <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {service}
                          </span>
                        ))}
                      </div>
                      
                      <div className="text-sm text-gray-500">
                        Priority: {provider.priority} | Featured until: {formatDate(provider.featuredUntil)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <div className="flex flex-col space-y-1">
                      <button
                        onClick={() => handleUpdatePriority(provider.id, Math.max(1, provider.priority - 1))}
                        className="btn btn-sm btn-secondary"
                        disabled={provider.priority === 1}
                      >
                        <ArrowUpIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleUpdatePriority(provider.id, provider.priority + 1)}
                        className="btn btn-sm btn-secondary"
                      >
                        <ArrowDownIcon className="h-4 w-4" />
                      </button>
                    </div>
                    
                    <button
                      onClick={() => handleToggleActive(provider.id, provider.isActive)}
                      className={`btn btn-sm ${provider.isActive ? 'btn-secondary' : 'btn-primary'}`}
                    >
                      {provider.isActive ? (
                        <XCircleIcon className="h-4 w-4" />
                      ) : (
                        <CheckCircleIcon className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
