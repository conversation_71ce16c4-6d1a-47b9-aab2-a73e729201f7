'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  MagnifyingGlassIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { formatDate } from '@/lib/utils';
import { fetchAllReviews, updateReviewStatus } from '@/lib/firestore';

interface Review {
  id: string;
  userId: string;
  providerId: string;
  bookingId: string;
  rating: number;
  title: string;
  comment: string;
  userName: string;
  userPhoto?: string;
  providerName: string;
  serviceType: string;
  isApproved: boolean;
  isFeatured: boolean;
  helpfulCount: number;
  createdAt: Date;
  updatedAt?: Date;
}

export default function ReviewsPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loadingReviews, setLoadingReviews] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'approved' | 'pending' | 'featured'>('all');

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  useEffect(() => {
    const fetchReviews = async () => {
      if (user && adminUser) {
        try {
          setLoadingReviews(true);
          console.log('⭐ Fetching real reviews from Firestore...');
          
          const realReviews = await fetchAllReviews(100);
          setReviews(realReviews as Review[]);
          
          console.log(`✅ Loaded ${realReviews.length} real reviews`);
        } catch (error) {
          console.error('💥 Error fetching reviews:', error);
          setReviews([]);
        } finally {
          setLoadingReviews(false);
        }
      }
    };

    fetchReviews();
  }, [user, adminUser]);

  const handleApproveReview = async (reviewId: string, isApproved: boolean) => {
    try {
      await updateReviewStatus(reviewId, isApproved);
      setReviews(reviews.map(review => 
        review.id === reviewId ? { ...review, isApproved } : review
      ));
    } catch (error) {
      console.error('Error updating review status:', error);
    }
  };

  const handleFeatureReview = async (reviewId: string, isFeatured: boolean) => {
    try {
      const review = reviews.find(r => r.id === reviewId);
      if (review) {
        await updateReviewStatus(reviewId, review.isApproved, isFeatured);
        setReviews(reviews.map(r => 
          r.id === reviewId ? { ...r, isFeatured } : r
        ));
      }
    } catch (error) {
      console.error('Error updating review featured status:', error);
    }
  };

  if (loading || loadingReviews) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {loading ? 'Authenticating...' : 'Loading real reviews from Firestore...'}
          </p>
        </div>
      </div>
    );
  }

  if (!user || !adminUser) {
    return null;
  }

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.providerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'approved' && review.isApproved) ||
                         (statusFilter === 'pending' && !review.isApproved) ||
                         (statusFilter === 'featured' && review.isFeatured);
    return matchesSearch && matchesStatus;
  });

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIconSolid
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <DashboardLayout title="Reviews Management">
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reviews</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage customer reviews and testimonials
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <div className="text-sm text-gray-600">
              Total: {reviews.length} | Approved: {reviews.filter(r => r.isApproved).length} | Featured: {reviews.filter(r => r.isFeatured).length}
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search reviews..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
              >
                <option value="all">All Reviews</option>
                <option value="approved">Approved</option>
                <option value="pending">Pending</option>
                <option value="featured">Featured</option>
              </select>
            </div>
          </div>
        </div>

        {/* Reviews Grid */}
        {filteredReviews.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <StarIcon className="mx-auto h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {reviews.length === 0 ? 'No Reviews Found' : 'No Reviews Match Your Search'}
            </h3>
            <p className="text-gray-600 mb-4">
              {reviews.length === 0 
                ? 'No reviews have been submitted yet. Reviews will appear here once customers leave feedback.'
                : 'Try adjusting your search terms or filters to find reviews.'
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredReviews.map((review) => (
              <div key={review.id} className="card p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                      {review.userPhoto ? (
                        <img src={review.userPhoto} alt={review.userName} className="h-10 w-10 rounded-full" />
                      ) : (
                        <span className="text-sm font-medium text-gray-600">
                          {review.userName.charAt(0)}
                        </span>
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{review.userName}</p>
                      <p className="text-sm text-gray-500">{review.providerName}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {renderStars(review.rating)}
                  </div>
                </div>

                <h3 className="font-semibold text-gray-900 mb-2">{review.title}</h3>
                <p className="text-gray-600 mb-4 line-clamp-3">{review.comment}</p>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <span>{review.serviceType}</span>
                  <span>{formatDate(review.createdAt)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      review.isApproved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {review.isApproved ? 'Approved' : 'Pending'}
                    </span>
                    {review.isFeatured && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        Featured
                      </span>
                    )}
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleApproveReview(review.id, !review.isApproved)}
                      className={`btn btn-sm ${review.isApproved ? 'btn-secondary' : 'btn-primary'}`}
                    >
                      {review.isApproved ? (
                        <XCircleIcon className="h-4 w-4" />
                      ) : (
                        <CheckCircleIcon className="h-4 w-4" />
                      )}
                    </button>
                    <button
                      onClick={() => handleFeatureReview(review.id, !review.isFeatured)}
                      className={`btn btn-sm ${review.isFeatured ? 'btn-secondary' : 'btn-outline'}`}
                    >
                      <StarIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
