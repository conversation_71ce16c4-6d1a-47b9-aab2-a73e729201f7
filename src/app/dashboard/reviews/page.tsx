'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  MagnifyingGlassIcon,
  StarIcon,
  CheckIcon,
  XMarkIcon,
  EyeIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';
import { formatDate, getStatusColor } from '@/lib/utils';
import { fetchAllReviews, updateReviewStatus, updateReviewFeatured } from '@/lib/firestore';

interface Review {
  id: string;
  userId: string;
  providerId: string;
  rating: number;
  comment: string;
  isApproved: boolean;
  isFeatured: boolean;
  createdAt: any;
  userInfo?: {
    name: string;
    email: string;
  };
  providerInfo?: {
    businessName: string;
    name: string;
  };
}

export default function ReviewsPage() {
  const { user, adminUser, loading } = useAuth();
  const router = useRouter();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loadingReviews, setLoadingReviews] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'approved' | 'pending' | 'featured'>('all');

  useEffect(() => {
    if (!loading && (!user || !adminUser)) {
      router.push('/login');
    }
  }, [user, adminUser, loading, router]);

  useEffect(() => {
    const fetchReviews = async () => {
      if (user && adminUser) {
        try {
          setLoadingReviews(true);
          console.log('⭐ Fetching real reviews from Firebase...');
          
          const realReviews = await fetchAllReviews(100);
          setReviews(realReviews as Review[]);
          
          console.log(`✅ Loaded ${realReviews.length} real reviews`);
          console.log('⭐ Sample review data:', realReviews[0]);
        } catch (error) {
          console.error('💥 Error fetching reviews:', error);
          setReviews([]);
        } finally {
          setLoadingReviews(false);
        }
      }
    };

    fetchReviews();
  }, [user, adminUser]);

  const handleApprove = async (reviewId: string) => {
    try {
      await updateReviewStatus(reviewId, true);
      setReviews(prev => prev.map(review => 
        review.id === reviewId ? { ...review, isApproved: true } : review
      ));
    } catch (error) {
      console.error('Error approving review:', error);
    }
  };

  const handleReject = async (reviewId: string) => {
    try {
      await updateReviewStatus(reviewId, false);
      setReviews(prev => prev.map(review => 
        review.id === reviewId ? { ...review, isApproved: false } : review
      ));
    } catch (error) {
      console.error('Error rejecting review:', error);
    }
  };

  const handleToggleFeatured = async (reviewId: string, isFeatured: boolean) => {
    try {
      await updateReviewFeatured(reviewId, isFeatured);
      setReviews(prev => prev.map(review => 
        review.id === reviewId ? { ...review, isFeatured } : review
      ));
    } catch (error) {
      console.error('Error updating featured status:', error);
    }
  };

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = 
      review.comment?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.userInfo?.name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'approved' && review.isApproved) ||
                         (statusFilter === 'pending' && !review.isApproved) ||
                         (statusFilter === 'featured' && review.isFeatured);
    
    return matchesSearch && matchesStatus;
  });

  if (loading || loadingReviews) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {loading ? 'Authenticating...' : 'Loading real reviews from Firebase...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reviews Management</h1>
            <p className="text-gray-600">Moderate and feature customer reviews</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="card p-4">
            <div className="text-2xl font-bold text-blue-600">{reviews.length}</div>
            <div className="text-sm text-gray-600">Total Reviews</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-green-600">
              {reviews.filter(r => r.isApproved).length}
            </div>
            <div className="text-sm text-gray-600">Approved Reviews</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {reviews.filter(r => !r.isApproved).length}
            </div>
            <div className="text-sm text-gray-600">Pending Approval</div>
          </div>
          <div className="card p-4">
            <div className="text-2xl font-bold text-purple-600">
              {reviews.filter(r => r.isFeatured).length}
            </div>
            <div className="text-sm text-gray-600">Featured Reviews</div>
          </div>
        </div>

        {/* Filters */}
        <div className="card p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search reviews..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <select
              className="input"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
            >
              <option value="all">All Status</option>
              <option value="approved">Approved</option>
              <option value="pending">Pending</option>
              <option value="featured">Featured</option>
            </select>
          </div>
        </div>

        {/* Reviews Table */}
        <div className="card overflow-hidden">
          {filteredReviews.length === 0 ? (
            <div className="text-center py-12">
              <StarIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {reviews.length === 0 ? 'No Reviews Found' : 'No Reviews Match Your Search'}
              </h3>
              <p className="text-gray-600">
                {reviews.length === 0 
                  ? 'No reviews have been submitted yet. Reviews will appear here once customers leave feedback.'
                  : 'Try adjusting your search terms or filters.'
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Review
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rating
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Provider
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredReviews.map((review) => (
                    <tr key={review.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs truncate">
                          {review.comment || 'No comment provided'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <StarIcon
                              key={i}
                              className={`h-4 w-4 ${
                                i < (review.rating || 0) ? 'text-yellow-400' : 'text-gray-300'
                              }`}
                            />
                          ))}
                          <span className="ml-2 text-sm text-gray-600">{review.rating || 'N/A'}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {review.userInfo?.name || 'Unknown User'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {review.providerInfo?.businessName || 'Unknown Provider'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <span className={`badge ${getStatusColor(review.isApproved ? 'approved' : 'pending')}`}>
                            {review.isApproved ? 'Approved' : 'Pending'}
                          </span>
                          {review.isFeatured && (
                            <span className="badge bg-purple-100 text-purple-800">Featured</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {review.createdAt ? formatDate(review.createdAt) : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {!review.isApproved ? (
                            <button 
                              onClick={() => handleApprove(review.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Approve Review"
                            >
                              <CheckIcon className="h-4 w-4" />
                            </button>
                          ) : (
                            <button 
                              onClick={() => handleReject(review.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Reject Review"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button 
                            onClick={() => handleToggleFeatured(review.id, !review.isFeatured)}
                            className={`${review.isFeatured ? 'text-purple-600' : 'text-gray-400'} hover:text-purple-600`}
                            title={review.isFeatured ? 'Remove from Featured' : 'Add to Featured'}
                          >
                            <HeartIcon className={`h-4 w-4 ${review.isFeatured ? 'fill-current' : ''}`} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
