'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import SupportLayout from '@/components/layout/SupportLayout';
import {
  ChatBubbleLeftRightIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  UserIcon,
  BuildingOfficeIcon,
  PaperAirplaneIcon,
} from '@heroicons/react/24/outline';
import { formatDate } from '@/lib/utils';
import { getSupportTickets, updateTicketStatus, addTicketResponse } from '@/lib/firestore';

interface SupportTicket {
  id: string;
  subject: string;
  message: string;
  status: 'open' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  userType: 'client' | 'provider';
  userId: string;
  userEmail: string;
  userName: string;
  createdAt: any;
  updatedAt: any;
  assignedTo?: string;
  responses?: Array<{
    id: string;
    message: string;
    respondedBy: string;
    respondedAt: any;
    isSupport: boolean;
  }>;
}

export default function TicketsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const statusParam = searchParams.get('status');
  
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [loadingTickets, setLoadingTickets] = useState(true);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [responseMessage, setResponseMessage] = useState('');
  const [sendingResponse, setSendingResponse] = useState(false);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/support/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    const fetchTickets = async () => {
      if (user) {
        try {
          setLoadingTickets(true);
          console.log('🎫 Fetching support tickets...');
          
          const supportTickets = await getSupportTickets();
          let filteredTickets = supportTickets as SupportTicket[];
          
          // Filter by status if provided
          if (statusParam && statusParam !== 'all') {
            filteredTickets = filteredTickets.filter(ticket => ticket.status === statusParam);
          }
          
          setTickets(filteredTickets);
          console.log(`✅ Loaded ${filteredTickets.length} tickets`);
        } catch (error) {
          console.error('💥 Error fetching tickets:', error);
          setTickets([]);
        } finally {
          setLoadingTickets(false);
        }
      }
    };

    fetchTickets();
  }, [user, statusParam]);

  const handleStatusUpdate = async (ticketId: string, newStatus: string) => {
    try {
      await updateTicketStatus(ticketId, newStatus);
      setTickets(prev => prev.map(ticket => 
        ticket.id === ticketId ? { ...ticket, status: newStatus as any } : ticket
      ));
      if (selectedTicket && selectedTicket.id === ticketId) {
        setSelectedTicket(prev => prev ? { ...prev, status: newStatus as any } : null);
      }
      console.log(`✅ Ticket ${ticketId} status updated to ${newStatus}`);
    } catch (error) {
      console.error('💥 Error updating ticket status:', error);
    }
  };

  const handleSendResponse = async () => {
    if (!selectedTicket || !responseMessage.trim() || !user) return;

    try {
      setSendingResponse(true);
      
      await addTicketResponse(selectedTicket.id, {
        message: responseMessage.trim(),
        respondedBy: user.email || 'Support Team',
        isSupport: true
      });

      // Update local state
      const newResponse = {
        id: Date.now().toString(),
        message: responseMessage.trim(),
        respondedBy: user.email || 'Support Team',
        respondedAt: new Date(),
        isSupport: true
      };

      setSelectedTicket(prev => prev ? {
        ...prev,
        responses: [...(prev.responses || []), newResponse],
        status: 'in-progress' as any
      } : null);

      setTickets(prev => prev.map(ticket => 
        ticket.id === selectedTicket.id 
          ? { ...ticket, responses: [...(ticket.responses || []), newResponse], status: 'in-progress' as any }
          : ticket
      ));

      setResponseMessage('');
      console.log(`✅ Response sent to ticket ${selectedTicket.id}`);
    } catch (error) {
      console.error('💥 Error sending response:', error);
    } finally {
      setSendingResponse(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-red-100 text-red-800';
      case 'in-progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading || loadingTickets) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-white/20 border-t-white mx-auto mb-6"></div>
          <p className="text-white/80 text-lg font-medium">Loading tickets...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <SupportLayout title={`Support Tickets ${statusParam ? `(${statusParam})` : ''}`}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Tickets List */}
        <div className="lg:col-span-1">
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {statusParam ? `${statusParam.charAt(0).toUpperCase() + statusParam.slice(1)} Tickets` : 'All Tickets'}
              </h2>
              <p className="text-sm text-gray-600 mt-1">{tickets.length} tickets found</p>
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {tickets.length === 0 ? (
                <div className="p-6 text-center">
                  <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">No tickets found</p>
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {tickets.map((ticket) => (
                    <div
                      key={ticket.id}
                      onClick={() => setSelectedTicket(ticket)}
                      className={`p-4 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedTicket?.id === ticket.id 
                          ? 'bg-blue-50 border-2 border-blue-200' 
                          : 'hover:bg-gray-50 border-2 border-transparent'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {ticket.userType === 'client' ? (
                            <UserIcon className="h-4 w-4 text-purple-600" />
                          ) : (
                            <BuildingOfficeIcon className="h-4 w-4 text-teal-600" />
                          )}
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                            {ticket.status}
                          </span>
                        </div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority}
                        </span>
                      </div>
                      
                      <h3 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2">
                        {ticket.subject}
                      </h3>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{ticket.userName}</span>
                        <span>{ticket.createdAt ? formatDate(ticket.createdAt.toDate()) : 'N/A'}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Ticket Detail */}
        <div className="lg:col-span-2">
          {selectedTicket ? (
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
              {/* Ticket Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      {selectedTicket.subject}
                    </h2>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        {selectedTicket.userType === 'client' ? (
                          <UserIcon className="h-4 w-4 mr-1" />
                        ) : (
                          <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                        )}
                        {selectedTicket.userName} ({selectedTicket.userEmail})
                      </div>
                      <span>•</span>
                      <span>{selectedTicket.createdAt ? formatDate(selectedTicket.createdAt.toDate()) : 'N/A'}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedTicket.status)}`}>
                      {selectedTicket.status}
                    </span>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(selectedTicket.priority)}`}>
                      {selectedTicket.priority}
                    </span>
                  </div>
                </div>

                {/* Status Actions */}
                <div className="flex space-x-2">
                  {selectedTicket.status !== 'in-progress' && (
                    <button
                      onClick={() => handleStatusUpdate(selectedTicket.id, 'in-progress')}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                    >
                      <ClockIcon className="h-4 w-4 mr-1" />
                      Mark In Progress
                    </button>
                  )}
                  {selectedTicket.status !== 'resolved' && (
                    <button
                      onClick={() => handleStatusUpdate(selectedTicket.id, 'resolved')}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      <CheckCircleIcon className="h-4 w-4 mr-1" />
                      Mark Resolved
                    </button>
                  )}
                </div>
              </div>

              {/* Ticket Content */}
              <div className="p-6 max-h-96 overflow-y-auto">
                {/* Original Message */}
                <div className="mb-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-gray-600">
                          {selectedTicket.userName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">{selectedTicket.userName}</p>
                        <p className="text-xs text-gray-500">Original message</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                      {selectedTicket.message}
                    </p>
                  </div>
                </div>

                {/* Responses */}
                {selectedTicket.responses && selectedTicket.responses.length > 0 && (
                  <div className="space-y-4 mb-6">
                    {selectedTicket.responses.map((response) => (
                      <div key={response.id} className={`${response.isSupport ? 'ml-8' : 'mr-8'}`}>
                        <div className={`rounded-lg p-4 ${response.isSupport ? 'bg-blue-50' : 'bg-gray-50'}`}>
                          <div className="flex items-center mb-2">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              response.isSupport ? 'bg-blue-500' : 'bg-gray-300'
                            }`}>
                              <span className="text-xs font-medium text-white">
                                {response.respondedBy.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div className="ml-3">
                              <p className="text-sm font-medium text-gray-900">
                                {response.isSupport ? 'Support Team' : selectedTicket.userName}
                              </p>
                              <p className="text-xs text-gray-500">
                                {response.respondedAt ? formatDate(response.respondedAt.toDate ? response.respondedAt.toDate() : response.respondedAt) : 'N/A'}
                              </p>
                            </div>
                          </div>
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">
                            {response.message}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Response Form */}
              <div className="p-6 border-t border-gray-200">
                <div className="space-y-4">
                  <textarea
                    rows={4}
                    className="block w-full rounded-xl border border-slate-200 px-4 py-3 text-sm placeholder-slate-400 focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500/20 bg-white/80 backdrop-blur-sm transition-all duration-300"
                    placeholder="Type your response to the customer..."
                    value={responseMessage}
                    onChange={(e) => setResponseMessage(e.target.value)}
                    disabled={sendingResponse}
                  />
                  <div className="flex justify-end">
                    <button
                      onClick={handleSendResponse}
                      disabled={!responseMessage.trim() || sendingResponse}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {sendingResponse ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/20 border-t-white mr-2"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                          Send Response
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 p-12 text-center">
              <ChatBubbleLeftRightIcon className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Ticket</h3>
              <p className="text-gray-600">Choose a ticket from the list to view details and respond to customers.</p>
            </div>
          )}
        </div>
      </div>
    </SupportLayout>
  );
}
