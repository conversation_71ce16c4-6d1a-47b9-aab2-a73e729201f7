'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import {
  Bars3Icon,
  XMarkIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowRightOnRectangleIcon,
  BellIcon,
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

interface SupportLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const navigation = [
  { name: 'Support Dashboard', href: '/support', icon: ChatBubbleLeftRightIcon },
  { name: 'Open Tickets', href: '/support/tickets?status=open', icon: ClockIcon },
  { name: 'Resolved Tickets', href: '/support/tickets?status=resolved', icon: CheckCircleIcon },
];

export default function SupportLayout({ children, title }: SupportLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, signOut } = useAuth();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/support/login');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Modern Mobile sidebar */}
      <div className={cn(
        "fixed inset-0 z-50 lg:hidden",
        sidebarOpen ? "block" : "hidden"
      )}>
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-72 flex-col glass-effect shadow-2xl">
          <div className="flex h-20 items-center justify-between px-6 border-b border-white/20">
            <div className="flex items-center">
              <div className="h-12 w-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4">
                <span className="text-xl font-bold text-white">Fetchly</span>
                <p className="text-sm text-white/70">Support Center</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-white/80 hover:text-white p-2 rounded-lg hover:bg-white/10 transition-all duration-300"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 mx-2 my-1 text-white/80 hover:text-white hover:bg-white/10"
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.name}
              </a>
            ))}
          </nav>
          
          {/* User Profile Section */}
          <div className="p-4 border-t border-white/20">
            <div className="flex items-center mb-4">
              <div className="h-10 w-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {user?.email?.charAt(0).toUpperCase() || 'S'}
                </span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-white">Support Team</p>
                <p className="text-xs text-white/70">{user?.email}</p>
              </div>
            </div>
            <button
              onClick={handleSignOut}
              className="w-full flex items-center px-4 py-2 text-sm text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      </div>

      {/* Modern Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-72 lg:flex-col">
        <div className="flex flex-col flex-grow glass-effect shadow-2xl">
          <div className="flex h-20 items-center px-6 border-b border-white/20">
            <div className="flex items-center">
              <div className="h-12 w-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4">
                <span className="text-xl font-bold text-white">Fetchly</span>
                <p className="text-sm text-white/70">Support Center</p>
              </div>
            </div>
          </div>
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 mx-2 my-1 text-white/80 hover:text-white hover:bg-white/10"
              >
                <item.icon className="h-5 w-5 mr-3" />
                {item.name}
              </a>
            ))}
          </nav>
          
          {/* User Profile Section */}
          <div className="p-6 border-t border-white/20">
            <div className="flex items-center mb-4">
              <div className="h-12 w-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-semibold text-lg">
                  {user?.email?.charAt(0).toUpperCase() || 'S'}
                </span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-semibold text-white">Support Team</p>
                <p className="text-xs text-white/70">{user?.email}</p>
              </div>
            </div>
            <button
              onClick={handleSignOut}
              className="flex items-center w-full px-4 py-3 text-sm text-white/80 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-300"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3" />
              Sign Out
            </button>
          </div>
        </div>
      </div>

      {/* Modern Main content */}
      <div className="lg:pl-72">
        {/* Modern Top bar */}
        <div className="sticky top-0 z-40 glass-effect border-b border-white/20 shadow-lg">
          <div className="flex h-20 items-center justify-between px-6 sm:px-8 lg:px-10">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-white/80 hover:text-white p-2 rounded-lg hover:bg-white/10 transition-all duration-300"
              >
                <Bars3Icon className="h-6 w-6" />
              </button>
              {title && (
                <h1 className="ml-4 lg:ml-0 text-2xl font-bold text-white">{title}</h1>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <button className="text-white/80 hover:text-white p-2 rounded-lg hover:bg-white/10 transition-all duration-300">
                <BellIcon className="h-6 w-6" />
              </button>
              <div className="h-10 w-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center lg:hidden shadow-lg">
                <span className="text-white font-semibold text-sm">
                  {user?.email?.charAt(0).toUpperCase() || 'S'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Page content */}
        <main className="flex-1">
          <div className="py-8 px-6 sm:px-8 lg:px-10">
            <div className="animate-fade-in-up">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
