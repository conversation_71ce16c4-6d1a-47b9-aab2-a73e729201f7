import Stripe from 'stripe';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2024-06-20',
});

export interface StripePayment {
  id: string;
  amount: number;
  currency: string;
  status: string;
  customerEmail: string;
  customerName: string;
  description: string;
  created: Date;
  paymentMethod: string;
  refunded: boolean;
  refundAmount?: number;
  metadata: {
    bookingId?: string;
    userId?: string;
    providerId?: string;
    serviceType?: string;
  };
}

export interface StripeStats {
  totalRevenue: number;
  totalTransactions: number;
  successfulPayments: number;
  refundedPayments: number;
  pendingPayments: number;
  averageTransactionAmount: number;
  monthlyRevenue: number;
  weeklyRevenue: number;
}

// Get real Stripe payment data
export const getStripePayments = async (limit: number = 100): Promise<StripePayment[]> => {
  try {
    console.log('💳 Fetching real Stripe payments...');
    
    const charges = await stripe.charges.list({
      limit,
      expand: ['data.customer', 'data.payment_method']
    });

    const payments: StripePayment[] = charges.data.map(charge => ({
      id: charge.id,
      amount: charge.amount / 100, // Convert from cents
      currency: charge.currency.toUpperCase(),
      status: charge.status,
      customerEmail: charge.billing_details?.email || charge.receipt_email || 'N/A',
      customerName: charge.billing_details?.name || 'N/A',
      description: charge.description || 'Payment',
      created: new Date(charge.created * 1000),
      paymentMethod: charge.payment_method_details?.type || 'card',
      refunded: charge.refunded,
      refundAmount: charge.amount_refunded ? charge.amount_refunded / 100 : undefined,
      metadata: {
        bookingId: charge.metadata.bookingId,
        userId: charge.metadata.userId,
        providerId: charge.metadata.providerId,
        serviceType: charge.metadata.serviceType,
      }
    }));

    console.log(`✅ Fetched ${payments.length} Stripe payments`);
    return payments;
  } catch (error) {
    console.error('💥 Error fetching Stripe payments:', error);
    return [];
  }
};

// Get Stripe statistics
export const getStripeStats = async (): Promise<StripeStats> => {
  try {
    console.log('📊 Calculating real Stripe statistics...');
    
    // Get payments from last 30 days
    const thirtyDaysAgo = Math.floor(Date.now() / 1000) - (30 * 24 * 60 * 60);
    const sevenDaysAgo = Math.floor(Date.now() / 1000) - (7 * 24 * 60 * 60);
    
    const [allCharges, monthlyCharges, weeklyCharges] = await Promise.all([
      stripe.charges.list({ limit: 1000 }),
      stripe.charges.list({ 
        created: { gte: thirtyDaysAgo },
        limit: 1000 
      }),
      stripe.charges.list({ 
        created: { gte: sevenDaysAgo },
        limit: 1000 
      })
    ]);

    const totalRevenue = allCharges.data
      .filter(charge => charge.status === 'succeeded')
      .reduce((sum, charge) => sum + charge.amount, 0) / 100;

    const monthlyRevenue = monthlyCharges.data
      .filter(charge => charge.status === 'succeeded')
      .reduce((sum, charge) => sum + charge.amount, 0) / 100;

    const weeklyRevenue = weeklyCharges.data
      .filter(charge => charge.status === 'succeeded')
      .reduce((sum, charge) => sum + charge.amount, 0) / 100;

    const totalTransactions = allCharges.data.length;
    const successfulPayments = allCharges.data.filter(c => c.status === 'succeeded').length;
    const refundedPayments = allCharges.data.filter(c => c.refunded).length;
    const pendingPayments = allCharges.data.filter(c => c.status === 'pending').length;
    
    const averageTransactionAmount = successfulPayments > 0 ? totalRevenue / successfulPayments : 0;

    const stats: StripeStats = {
      totalRevenue,
      totalTransactions,
      successfulPayments,
      refundedPayments,
      pendingPayments,
      averageTransactionAmount,
      monthlyRevenue,
      weeklyRevenue
    };

    console.log('✅ Stripe stats calculated:', stats);
    return stats;
  } catch (error) {
    console.error('💥 Error calculating Stripe stats:', error);
    return {
      totalRevenue: 0,
      totalTransactions: 0,
      successfulPayments: 0,
      refundedPayments: 0,
      pendingPayments: 0,
      averageTransactionAmount: 0,
      monthlyRevenue: 0,
      weeklyRevenue: 0
    };
  }
};

// Process refund
export const processRefund = async (paymentIntentId: string, amount?: number): Promise<boolean> => {
  try {
    console.log(`💸 Processing refund for payment: ${paymentIntentId}`);
    
    const refund = await stripe.refunds.create({
      charge: paymentIntentId,
      amount: amount ? Math.round(amount * 100) : undefined, // Convert to cents
    });

    console.log(`✅ Refund processed: ${refund.id}`);
    return true;
  } catch (error) {
    console.error('💥 Error processing refund:', error);
    throw error;
  }
};

// Get customer information
export const getStripeCustomer = async (customerId: string) => {
  try {
    const customer = await stripe.customers.retrieve(customerId);
    return customer;
  } catch (error) {
    console.error('💥 Error fetching Stripe customer:', error);
    return null;
  }
};

// Create payment intent (for new bookings)
export const createPaymentIntent = async (
  amount: number,
  currency: string = 'usd',
  metadata: Record<string, string> = {}
) => {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      metadata,
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return paymentIntent;
  } catch (error) {
    console.error('💥 Error creating payment intent:', error);
    throw error;
  }
};

export default stripe;
