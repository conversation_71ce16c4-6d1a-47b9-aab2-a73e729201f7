const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword } = require('firebase/auth');
const { getFirestore, doc, setDoc, Timestamp } = require('firebase/firestore');
require('dotenv').config({ path: '../fetchly-admin/.env.local' });

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

async function setupSupportUser() {
  try {
    console.log('🔧 Setting up Fetchly Support Team user...');
    
    const supportEmail = process.env.SUPPORT_EMAIL || '<EMAIL>';
    const supportPassword = process.env.SUPPORT_PASSWORD || 'Supp0rt2025!';
    const supportName = process.env.SUPPORT_NAME || 'Fetchly Support Team';

    console.log(`📧 Creating support user: ${supportEmail}`);

    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, supportEmail, supportPassword);
    const user = userCredential.user;

    console.log(`✅ Support user created with UID: ${user.uid}`);

    // Add user to supportTeam collection
    await setDoc(doc(db, 'supportTeam', user.uid), {
      uid: user.uid,
      email: supportEmail,
      name: supportName,
      role: 'support',
      permissions: ['read_tickets', 'write_tickets', 'update_tickets'],
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      lastLogin: null
    });

    console.log('✅ Support user added to supportTeam collection');

    // Create some sample support tickets for testing
    console.log('🎫 Creating sample support tickets...');

    const sampleTickets = [
      {
        subject: 'Unable to book a service',
        message: 'Hi, I am trying to book a dog walking service but the app keeps crashing when I try to submit the booking. Can you please help?',
        status: 'open',
        priority: 'high',
        userType: 'client',
        userId: 'sample_client_1',
        userEmail: '<EMAIL>',
        userName: 'Sarah Johnson',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        responses: []
      },
      {
        subject: 'Payment not processed',
        message: 'I completed a pet sitting service yesterday but the payment has not been processed to my account. My service ID is #12345. Please check.',
        status: 'open',
        priority: 'urgent',
        userType: 'provider',
        userId: 'sample_provider_1',
        userEmail: '<EMAIL>',
        userName: 'Mike\'s Pet Care',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        responses: []
      },
      {
        subject: 'Profile verification issue',
        message: 'I uploaded my documents for verification 3 days ago but my profile is still showing as unverified. Can you please check the status?',
        status: 'in-progress',
        priority: 'medium',
        userType: 'provider',
        userId: 'sample_provider_2',
        userEmail: '<EMAIL>',
        userName: 'Happy Paws Services',
        createdAt: Timestamp.fromDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)), // 2 days ago
        updatedAt: Timestamp.now(),
        responses: [
          {
            id: '1',
            message: 'Thank you for contacting us. We are reviewing your documents and will get back to you within 24 hours.',
            respondedBy: supportEmail,
            respondedAt: Timestamp.fromDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)), // 1 day ago
            isSupport: true
          }
        ]
      },
      {
        subject: 'App feedback and suggestions',
        message: 'Love the app! Just wanted to suggest adding a feature to schedule recurring services. It would be very helpful for regular dog walking.',
        status: 'resolved',
        priority: 'low',
        userType: 'client',
        userId: 'sample_client_2',
        userEmail: '<EMAIL>',
        userName: 'David Chen',
        createdAt: Timestamp.fromDate(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)), // 5 days ago
        updatedAt: Timestamp.fromDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)), // 3 days ago
        responses: [
          {
            id: '1',
            message: 'Thank you for your feedback! We really appreciate suggestions from our users. I\'ll forward this to our product team for consideration in future updates.',
            respondedBy: supportEmail,
            respondedAt: Timestamp.fromDate(new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)), // 4 days ago
            isSupport: true
          },
          {
            id: '2',
            message: 'Thanks for the quick response! Looking forward to future updates.',
            respondedBy: '<EMAIL>',
            respondedAt: Timestamp.fromDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)), // 3 days ago
            isSupport: false
          }
        ]
      }
    ];

    // Add sample tickets to Firestore
    for (let i = 0; i < sampleTickets.length; i++) {
      const ticketRef = doc(db, 'supportTickets', `sample_ticket_${i + 1}`);
      await setDoc(ticketRef, sampleTickets[i]);
      console.log(`✅ Created sample ticket: ${sampleTickets[i].subject}`);
    }

    console.log('\n🎉 Setup completed successfully!');
    console.log('\n📋 Support Team Login Credentials:');
    console.log(`📧 Email: ${supportEmail}`);
    console.log(`🔑 Password: ${supportPassword}`);
    console.log('\n🔗 Access the support dashboard at: http://localhost:3000/support');
    console.log('\n📊 Sample tickets created for testing the support system');

  } catch (error) {
    if (error.code === 'auth/email-already-in-use') {
      console.log('⚠️  Support user already exists. Updating supportTeam collection...');
      
      // If user exists, just update the supportTeam collection
      const supportEmail = process.env.SUPPORT_EMAIL || '<EMAIL>';
      const supportName = process.env.SUPPORT_NAME || 'Fetchly Support Team';
      
      // We need to get the user UID first - this is a limitation of this approach
      console.log('ℹ️  Please manually add the support user to the supportTeam collection in Firebase Console');
      console.log(`📧 Email: ${supportEmail}`);
      
    } else {
      console.error('💥 Error setting up support user:', error);
    }
  }
}

// Run the setup
setupSupportUser().then(() => {
  console.log('🏁 Setup script completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Setup failed:', error);
  process.exit(1);
});
