const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = {
  type: "service_account",
  project_id: "fetchly-724b6",
  private_key_id: "your_private_key_id",
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  client_email: "<EMAIL>",
  client_id: "your_client_id",
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40fetchly-724b6.iam.gserviceaccount.com"
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://fetchly-724b6-default-rtdb.firebaseapp.com"
});

const db = admin.firestore();

async function checkCollections() {
  try {
    console.log('🔍 Checking all collections in your Firestore database...\n');
    
    const collections = await db.listCollections();
    
    console.log('📊 Found Collections:');
    console.log('===================');
    
    for (const collection of collections) {
      console.log(`\n📁 Collection: ${collection.id}`);
      
      try {
        const snapshot = await collection.limit(3).get();
        console.log(`   📈 Document count: ${snapshot.size}+ documents`);
        
        if (!snapshot.empty) {
          console.log('   📄 Sample document structure:');
          const sampleDoc = snapshot.docs[0];
          const data = sampleDoc.data();
          
          // Show field names and types
          Object.keys(data).forEach(key => {
            const value = data[key];
            const type = typeof value;
            if (value && value.constructor && value.constructor.name === 'Timestamp') {
              console.log(`      ${key}: [Timestamp]`);
            } else if (Array.isArray(value)) {
              console.log(`      ${key}: [Array with ${value.length} items]`);
            } else if (type === 'object' && value !== null) {
              console.log(`      ${key}: [Object]`);
            } else {
              console.log(`      ${key}: ${type}`);
            }
          });
        }
      } catch (error) {
        console.log(`   ❌ Error reading collection: ${error.message}`);
      }
    }
    
    console.log('\n✅ Collection scan complete!');
    
  } catch (error) {
    console.error('💥 Error checking collections:', error);
  }
}

checkCollections().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('Check failed:', error);
  process.exit(1);
});
